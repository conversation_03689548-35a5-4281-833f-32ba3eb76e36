import os

def get_paths_with_valid_folder(root_dir: str, relevant_extensions: list = None, max_depth: int = None, exclude_dirs: list = None, require_files: bool = True) -> list[str]:
    """
    Find all subdirectories that contain a 'valid' folder and return their paths.

    Args:
        root_dir (str): Root directory to start the search from
        relevant_extensions (list, optional): List of file extensions to consider (e.g., ['.csv', '.xlsx'])
        max_depth (int, optional): Maximum directory depth to search
        exclude_dirs (list, optional): List of directory names to exclude from search
        require_files (bool, optional): Whether to require relevant files to be present

    Returns:
        list[str]: List of directory paths containing 'valid' folder
    """
    valid_paths = []

    # Set defaults
    if relevant_extensions is None:
        relevant_extensions = ['.csv', '.xlsx', '.xls']  # Default to common data file types
    if exclude_dirs is None:
        # Remove 'sorted' and 'verified' from exclusions since they might contain valid folders
        exclude_dirs = ['invalid', 'output', 'process', 'splits']

    # Walk through directory tree
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Skip the root directory itself
        if dirpath == root_dir:
            continue

        # Check depth if max_depth is specified
        if max_depth is not None:
            # Calculate current depth by counting path separators
            current_depth = dirpath.replace(root_dir, '').count(os.sep)
            if current_depth > max_depth:
                # Remove directories from dirnames to prevent further traversal
                dirnames[:] = []
                continue

        # Skip excluded directories - only if the directory name exactly matches an excluded name
        # Get the current directory name (not the full path)
        current_dir_name = os.path.basename(dirpath).lower()
        if current_dir_name in [d.lower() for d in exclude_dirs]:
            continue

        # First check if the directory has a 'valid' folder
        if 'valid' in dirnames:
            # If it has a valid folder, we'll check for files
            has_relevant_files = False

            # If no extensions specified, no files required, or require_files is False, consider it valid
            if not require_files or not relevant_extensions or len(relevant_extensions) == 0:
                has_relevant_files = True
            else:
                # Check parent directory and valid subdirectory for relevant files
                # First check the current directory
                for filename in filenames:
                    if any(filename.lower().endswith(ext) for ext in relevant_extensions):
                        has_relevant_files = True
                        break

                # If no relevant files found in current directory, check the valid subdirectory
                if not has_relevant_files:
                    valid_dir_path = os.path.join(dirpath, 'valid')
                    if os.path.isdir(valid_dir_path):
                        try:
                            valid_dir_files = os.listdir(valid_dir_path)
                            for filename in valid_dir_files:
                                if os.path.isfile(os.path.join(valid_dir_path, filename)) and any(filename.lower().endswith(ext) for ext in relevant_extensions):
                                    has_relevant_files = True
                                    break
                        except (PermissionError, OSError):
                            # If we can't access the directory, assume it might have relevant files
                            has_relevant_files = True

            # If we have a valid folder and either have relevant files or no file check is needed
            if has_relevant_files:
                # Get the full path including the 'valid' folder
                valid_folder_path = os.path.join(dirpath, 'valid')

                # Clean the path by removing common output directories if needed
                # (keeping this in case you want to clean the paths in the future)
                cleaned_path = valid_folder_path
                for output_dir in ['\\output', '\\process', '\\sorted', '\\splits']:
                    cleaned_path = cleaned_path.replace(output_dir, "")

                # Add the path if it's not already in our list
                if cleaned_path not in valid_paths:
                    valid_paths.append(cleaned_path)

    return valid_paths

def verify_paths(paths: list[str]) -> list[str]:
    """
    Verify that paths exist and are valid directories.

    Args:
        paths (list[str]): List of paths to verify

    Returns:
        list[str]: List of verified paths
    """
    verified_paths = []
    for path in paths:
        # Check if the path exists and is a directory
        if os.path.isdir(path):
            verified_paths.append(path)
    return verified_paths

def save_paths_to_file(paths: list[str], root_dir: str, no_valid_paths: bool = False):
    """
    Save the list of paths to dirs.txt in the root directory.

    Args:
        paths (list[str]): List of paths to save
        root_dir (str): Root directory where dirs.txt will be saved
        no_valid_paths (bool): Whether no directories with valid folders were found
    """
    output_file = os.path.join(root_dir, "dirs.txt")
    with open(output_file, 'w', encoding='utf-8') as f:
        if no_valid_paths:
            f.write("No directories with 'valid' folder found.\n")
        else:
            for path in paths:
                f.write(f"{path}\n")
    return output_file

def print_status(message: str, count: int = None):
    """
    Print status message with optional count.

    Args:
        message (str): Status message to print
        count (int, optional): Count to append to message
    """
    if count is not None:
        print(f"{message}: {count}")
    else:
        print(message)

if __name__ == "__main__":
    # Get the root directory from user
    root_directory = input("Enter the root directory path: ").strip('"')  # Remove quotes if present

    # Ask for additional filtering options
    use_advanced = input("Use advanced filtering options? (y/n): ").strip().lower() == 'y'

    max_depth = None
    relevant_extensions = ['.csv', '.xlsx', '.xls']
    # Remove 'sorted' and 'verified' from exclusions since they might contain valid folders
    exclude_dirs = ['invalid', 'output', 'process', 'splits']
    require_files = True  # Default to requiring files

    if use_advanced:
        # Ask for max depth
        depth_input = input("Enter maximum directory depth (leave blank for unlimited): ").strip()
        if depth_input and depth_input.isdigit():
            max_depth = int(depth_input)

        # Ask for file extensions
        ext_input = input("Enter file extensions to look for (comma-separated, e.g., .csv,.xlsx): ").strip()
        if ext_input:
            relevant_extensions = [ext.strip() for ext in ext_input.split(',')]

        # Ask for directories to exclude
        exclude_input = input(f"Enter directories to exclude (comma-separated, default: {','.join(exclude_dirs)}): ").strip()
        if exclude_input:
            exclude_dirs = [dir.strip() for dir in exclude_input.split(',')]

        # Ask whether to require files
        require_files_input = input("Require relevant files in directories? (y/n, default: y): ").strip().lower()
        if require_files_input == 'n':
            require_files = False

    print_status("Searching for 'valid' folders...")
    print_status(f"Using filters: max_depth={max_depth}, extensions={relevant_extensions}, exclude={exclude_dirs}, require_files={require_files}")

    # Get all paths containing 'valid' folder with the specified filters
    valid_paths = get_paths_with_valid_folder(
        root_directory,
        relevant_extensions=relevant_extensions,
        max_depth=max_depth,
        exclude_dirs=exclude_dirs,
        require_files=require_files
    )

    # Verify that all paths exist and are valid directories
    print_status("Verifying paths...")
    verified_paths = verify_paths(valid_paths)

    if len(verified_paths) != len(valid_paths):
        print_status(f"Warning: {len(valid_paths) - len(verified_paths)} paths were invalid and removed")

    # Save paths to dirs.txt in root directory (even if empty)
    output_file = save_paths_to_file(verified_paths, root_directory, no_valid_paths=(len(verified_paths) == 0))

    if verified_paths:
        # Sort paths for better readability
        verified_paths.sort()

        print_status("\nFound 'valid' folders", len(verified_paths))
        print_status(f"Full paths have been saved to: {output_file}")

        # Print first few paths as example
        print_status("\nExample paths:")
        for path in verified_paths[:5]:
            print(f"- {path}")
        if len(verified_paths) > 5:
            print("...")
    else:
        print_status("\nNo 'valid' folders found")
        print_status(f"Status saved to: {output_file}")



