import os
import glob
import pandas as pd
import re
import traceback
import rich_progress
from datetime import datetime

# Function to extract segment from path
def extract_segment_from_path(path):
    """Extract conference segment name from path.

    Args:
        path (str): The file path to extract segment from

    Returns:
        str or None: The extracted segment name or None if not found
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        rich_progress.print_status(f"Found segment: {segment}", "success")
        return segment
    else:
        rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
        return None

def process_files(path, csn):
    try:
        # Change to the specified directory
        os.chdir(path)
        rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

        # Find CSV files
        csv_files = glob.glob('*.csv')
        if not csv_files:
            rich_progress.print_status("No CSV files found in the directory.", "error")
            return

        rich_progress.print_status(f"Found {len(csv_files)} CSV files: {', '.join(csv_files[:5])}{'...' if len(csv_files) > 5 else ''}", "info")

        # Initialize progress bar with gradient colors
        total_steps = len(csv_files) + 2  # Reading files + processing invalid + processing valid
        progress_bar, update_progress = rich_progress.create_progress_bar(
            total=total_steps,
            description="Processing files",
            color_scheme="green"
        )

        # Step 1: Read and concatenate CSV files
        try:
            df_list = []
            for f in csv_files:
                try:
                    df = pd.read_csv(f, on_bad_lines='skip')
                    df_list.append(df)
                    update_progress(1, f"Reading file {f}")  # Update progress for each file read
                except Exception as e:
                    print_error(f"Could not read file {f}: {str(e)}")

            if not df_list:
                print_error("Could not read any CSV files.")
                progress_bar.stop()
                return

            df_hbs = pd.concat(df_list, ignore_index=True)
            rich_progress.print_status(f"Combined data shape: {df_hbs.shape}", "info")

            # Check if 'result' column exists
            if 'result' not in df_hbs.columns:
                print_error(f"'result' column not found in data. Available columns: {', '.join(df_hbs.columns)}")
                progress_bar.stop()
                return

        except Exception as e:
            print_error(f"Error reading CSV files: {str(e)}", show_traceback=True)
            progress_bar.stop()
            return

        # Step 2: Process invalid results
        try:
            invalid_options = ['unknown', 'invalid', 'disposable']
            df_invalid = df_hbs[df_hbs['result'].isin(invalid_options)]
            os.makedirs("invalid", exist_ok=True)

            # Check if 'Email' column exists
            if 'Email' not in df_invalid.columns:
                rich_progress.print_status(f"Warning: 'Email' column not found in invalid data. Available columns: {', '.join(df_invalid.columns)}", "warning")
                # Try to find an alternative column that might contain email addresses
                email_cols = [col for col in df_invalid.columns if 'email' in col.lower()]
                if email_cols:
                    rich_progress.print_status(f"Using '{email_cols[0]}' column instead of 'Email'", "warning")
                    df_invalid.rename(columns={email_cols[0]: 'Email'}, inplace=True)
                else:
                    rich_progress.print_status("No suitable email column found. Cannot proceed with invalid records.", "error")
                    update_progress(1, "Skipping invalid records")  # Skip this step
                    return

            invalid_path = f'invalid/{csn}_MV_Invalid.csv'
            df_invalid['Email'].to_csv(invalid_path, index=False)
            rich_progress.print_status(f"Saved invalid records to {invalid_path}", "success")
            update_progress(1, "Processed invalid records")  # Update progress for processing invalid results
        except Exception as e:
            rich_progress.print_status(f"Error processing invalid results: {str(e)}", "error")
            update_progress(1, "Skipped due to error")  # Skip this step

        # Step 3: Process valid results
        try:
            valid_options = ['ok', 'catch_all']
            df_valid = df_hbs[df_hbs['result'].isin(valid_options)]
            os.makedirs("valid", exist_ok=True)

            # Check for Name column or alternatives
            if "Author Name" in df_valid.columns:
                df_valid.rename(columns={"Author Name": "Name"}, inplace=True)
            elif "Name" not in df_valid.columns:
                # Try to find an alternative column that might contain names
                name_cols = [col for col in df_valid.columns if any(name_part in col.lower() for name_part in ['name', 'author', 'person'])]
                if name_cols:
                    print(f"Using '{name_cols[0]}' column as 'Name'")
                    df_valid.rename(columns={name_cols[0]: 'Name'}, inplace=True)
                else:
                    print("No suitable name column found. Adding empty 'Name' column.")
                    df_valid['Name'] = ''

            # Ensure Email column exists
            if 'Email' not in df_valid.columns:
                print(f"Warning: 'Email' column not found in valid data. Available columns: {', '.join(df_valid.columns)}")
                # Try to find an alternative column
                email_cols = [col for col in df_valid.columns if 'email' in col.lower()]
                if email_cols:
                    print(f"Using '{email_cols[0]}' column as 'Email'")
                    df_valid.rename(columns={email_cols[0]: 'Email'}, inplace=True)
                else:
                    print("No suitable email column found. Cannot proceed with valid records.")
                    progress_bar.update(1)  # Skip this step
                    return

            valid_path = f'valid/{csn}_MV_Valid.csv'
            df_valid.to_csv(valid_path, columns=['Name', 'Email'], index=False)
            rich_progress.print_status(f"Saved valid records to {valid_path}", "success")
            update_progress(1, "Processed valid records")  # Update progress for processing valid results
        except Exception as e:
            rich_progress.print_status(f"Error processing valid results: {str(e)}", "error")
            update_progress(1, "Skipped valid processing due to error")  # Skip this step

        # Joining invalids in Hardbounces
        try:
            master_hb_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
            if os.path.exists(master_hb_path):
                df_hb_all = pd.read_csv(master_hb_path)
                df_concat_allinvalids = pd.concat([df_invalid, df_hb_all], axis=0)
                # Remove duplicates
                df_concat_allinvalids = df_concat_allinvalids.drop_duplicates(subset='Email')
                df_concat_allinvalids.Email.to_csv(master_hb_path, mode='w', index=False)
                rich_progress.print_status(f"Updated Master_Hardbounces.csv with {len(df_invalid)} new invalid records", "success")
            else:
                rich_progress.print_status(f"Warning: Master hardbounces file not found at {master_hb_path}", "warning")
        except Exception as e:
            rich_progress.print_status(f"Error updating master hardbounces: {str(e)}", "error")

        # Close progress bar
        progress_bar.stop()

        # Print summary
        rich_progress.print_status("\nProcessing Summary:", "header")
        rich_progress.print_status(f"Invalid records: {len(df_invalid)}", "info")
        rich_progress.print_status(f"Valid records: {len(df_valid)}", "info")
        rich_progress.print_status(f"Total records processed: {len(df_hbs)}", "info")
        rich_progress.print_status(f"Conference segment name used: {csn}", "success")

    except Exception as e:
        print_error(f"Error in process_files: {str(e)}", show_traceback=True)
        try:
            progress_bar.stop()
        except:
            pass

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def print_error(message, show_traceback=False):
    """Print an error message in red."""
    rich_progress.print_status(f"ERROR: {message}", "error")

    if show_traceback:
        rich_progress.print_status("=" * 40, "error")
        traceback.print_exc()
        rich_progress.print_status("=" * 40, "error")

if __name__ == "__main__":
    try:
        # Print welcome header
        print_header("MV Email Validation Processor")

        # Get input path
        print_section("Input Path")
        path = input("Enter location path: ")

        # Extract segment from path
        print_section("Conference Segment Detection")
        csn = extract_segment_from_path(path)

        # If segment extraction failed, prompt user for manual input
        if not csn:
            print_section("Manual Input Required")
            rich_progress.print_status("Proceeding with manual input since segment couldn't be detected from path.", "info")
            csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ").strip()

            # Validate that the user provided a non-empty input
            while not csn:
                rich_progress.print_status("Conference segment name cannot be empty.", "warning")
                csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ").strip()

            rich_progress.print_status(f"Using manually entered segment: {csn}", "success")

        # Confirm the segment name that will be used
        print_section("Confirmation")
        rich_progress.print_status(f"Conference segment name: {csn}", "info")
        proceed = input("Proceed with this segment name? (y/n): ").lower()

        if proceed in ['y', 'yes']:
            print_section("Processing Files")
            process_files(path, csn)
            print_header("Processing Complete")
        else:
            rich_progress.print_status("Operation cancelled by user.", "warning")

    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user (Ctrl+C).")
    except Exception as e:
        print_error(f"An unexpected error occurred: {str(e)}", show_traceback=True)