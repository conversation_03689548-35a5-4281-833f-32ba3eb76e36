import pandas as pd
import os
import re

def normalize_whitespace(text):
    """
    Normalize whitespace in text by:
    1. Replacing multiple spaces with a single space
    2. Removing leading and trailing whitespace
    3. Handling other whitespace characters (tabs, newlines, etc.)

    Args:
        text: Text to normalize

    Returns:
        Normalized text
    """
    if not isinstance(text, str):
        return text

    # Replace all whitespace characters (spaces, tabs, newlines) with a single space
    normalized = re.sub(r'\s+', ' ', text)
    # Remove leading and trailing whitespace
    normalized = normalized.strip()
    return normalized

def clean_names(file_path: str) -> None:
    """
    Clean names by:
    1. Removing text after the first comma
    2. Normalizing whitespace (removing leading/trailing spaces and extra spaces between words)
    3. Handling other whitespace characters (tabs, newlines, etc.)

    Args:
        file_path: Path to the CSV file
    """
    try:
        # Read the CSV file
        df = pd.read_csv(file_path)

        # Check if Name column exists
        if 'Name' not in df.columns:
            if 'Author Name' in df.columns:
                df.rename(columns={'Author Name': 'Name'}, inplace=True)
            else:
                print("Error: No Name column found in the file")
                return

        # Clean the Name column by:
        # 1. Taking only the part before the first comma
        # 2. Normalizing whitespace (removing leading/trailing spaces and extra spaces between words)
        df['Name'] = df['Name'].astype(str).apply(lambda x: normalize_whitespace(x.split(',')[0]))

        # Print a sample of cleaned names for verification
        print("\nSample of cleaned names:")
        for i, name in enumerate(df['Name'].head(5).tolist()):
            print(f"  {i+1}. '{name}'")
        print("")

        # Save the file with _cleaned suffix
        base_path = os.path.splitext(file_path)[0]
        output_path = f"{base_path}_cleaned.csv"
        df.to_csv(output_path, index=False, encoding='utf-8-sig')

        print(f"Successfully cleaned names and saved to: {output_path}")
        print(f"Processed {len(df)} records")

    except Exception as e:
        print(f"Error processing file: {str(e)}")

if __name__ == "__main__":
    file_path = input("Enter the path to your CSV file: ")
    clean_names(file_path)