from tqdm import tqdm
import os
import glob 
import pandas as pd 
import numpy as np
import re
import warnings
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

path = input(r'Enter Folder Path: ')
os.chdir(path)
txt_files = glob.glob('*.{}'.format('txt')) #xlsx

df_concat = pd.concat([pd.read_table(f, on_bad_lines='skip', low_memory=False) for f in txt_files], ignore_index=True) #read_excel

#df_concat.to_csv("txt_exp.csv", encoding = 'utf-8-sig', index=False)
df = df_concat
#df = pd.read_csv('txt_exp.csv')

author = df[["AUTHOR"]]
email = df[["EMAIL "]]
df1 = author['AUTHOR'].str.split(";", expand=True)
df2 = email['EMAIL '].str.split(";", expand=True)

list = set(df1.columns.tolist())
for i in iter(list):
    col = i
    df3 = pd.concat([df1[col], df2[col]], axis=1)
    df3.to_csv('mdpi'+str(col)+'.csv', encoding='utf-8-sig', index=False)
    pass

os.system("copy *.csv check.csv")
df = pd.read_csv('check.csv', sep= ',', names=['Name', 'Email'])

def trim_all_columns(df):
    """
    Trim whitespace from ends of each value across all series in dataframe
    """
    trim_strings = lambda x: x.strip(',;() ') if isinstance(x, str) else x
    return df.applymap(trim_strings)
# simple example of trimming whitespace from data elements
#df4 = pd.DataFrame(['Conference Short Name', 'Email'])
dff = trim_all_columns(df)
dff.drop_duplicates(subset='Email', keep='first', inplace=True)
dff.dropna(subset='Email', inplace=True)
os.makedirs(("output"), exist_ok=True)
dff.dropna(axis=0).to_csv('output/output.csv', encoding='utf-8-sig', index=False)
os.system("del *.csv")


df = pd.read_csv(path+'/output/output.csv')

# Replace empty strings with NaN
df.replace('', np.nan, inplace=True)

# Drop rows where 'Email' is NaN
df.dropna(subset=['Email'], inplace=True)

# Filter out rows where 'Email' doesn't contain '@'
df = df[df['Email'].str.contains('@', na=False)]

# Remove rows where 'Email' contains 'info' or 'contact'
df = df[~df['Email'].str.contains('info|contact', case=False, na=False)]

# Drop rows where 'Name' is NaN
df.dropna(subset=['Name'], inplace=True)

# Split the 'Name' column on the comma and then reorder and join names
df['Name'] = df['Name'].apply(lambda x: ' '.join(x.split(', ')[::-1]))

# Remove rows containing the words 'behalf' or 'group'
df = df[~df['Name'].str.contains('behalf|group', case=False, na=False)]

# Strip whitespace from all string entries
df = df.apply(lambda x: x.str.strip() if x.dtype == 'object' else x)

# Save to CSV
df.to_csv('output/output.csv', columns=['Name', 'Email'], encoding='utf-8-sig', index=False)

print(len(df))
