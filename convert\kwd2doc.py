import os
import pandas as pd
import docx

def keywords_to_docx(excel_filepath, docx_filepath, sheet_name='Sheet1', column_name='Keywords'):
    """
    Extracts keywords from an Excel file, formats them as a comma-separated string,
    and saves them to a Word document.

    Args:
        excel_filepath: The path to the Excel file.
        docx_filepath: The path to save the Word document.
        sheet_name: The name of the sheet in the Excel file (default: 'Sheet1').
        column_name: The name of the column containing keywords (default: 'Keywords').
    """
    try:
        df = pd.read_excel(excel_filepath, sheet_name=sheet_name)
    except FileNotFoundError:
        print(f"Error: Excel file not found: {excel_filepath}")
        return
    except ValueError:
        print(f"Error: Sheet '{sheet_name}' not found in {excel_filepath}")
        return
    except Exception as e:
        print(f"An unexpected error occurred while reading the Excel file: {e}")
        return

    if column_name not in df.columns:
        print(f"Error: Column '{column_name}' not found in the Excel file.")
        return
    
    if df.empty:
        print(f"Error: The dataframe is empty.")
        return

    try:
        df_keywords = df[column_name].str.replace('\n', ',')
        keywords_string = ", ".join(df_keywords)

        doc = docx.Document()
        doc.add_paragraph(keywords_string)
        doc.save(docx_filepath)
        print(f"Keywords saved to: {docx_filepath}")

    except Exception as e:
        print(f"An unexpected error occurred while processing or saving the document: {e}")

def main():
    """
    Main function to run the script.
    """
    directory = input("Enter the directory containing the Excel file (or press Enter to use the current directory): ")
    if directory:
        try:
            os.chdir(directory)
        except FileNotFoundError:
            print(f"Error: Directory not found: {directory}")
            return
    
    filename = input("Enter the Excel filename (without extension): ")
    excel_filepath = filename + ".xlsx"
    docx_filepath = filename + ".docx"

    keywords_to_docx(excel_filepath, docx_filepath)

if __name__ == "__main__":
    main()
