#!/usr/bin/env python3
"""
Elastic Email CSV Extractor
Extracts hard bounce emails and unsubscribes from multiple Elastic Email CSV exports.
"""

import os
import csv
import pandas as pd
from pathlib import Path
import argparse
from datetime import datetime

def extract_emails_from_csv(file_path, email_column='to', status_column=None, target_status=None, reason=None):
    """
    Extract emails from a CSV file based on status criteria.

    Args:
        file_path: Path to the CSV file
        email_column: Name of the email column (default: 'to')
        status_column: Name of the status column (optional)
        target_status: Target status to filter by (optional)
        reason: Reason to assign to extracted emails (optional)

    Returns:
        List of tuples (email, reason)
    """
    email_reason_pairs = []

    try:
        # Try to read with pandas first for better handling of various CSV formats
        df = pd.read_csv(file_path, encoding='utf-8')

        # Look for the specific email column first, then fallback to common variations
        email_col = None
        if email_column in df.columns:
            email_col = email_column
        else:
            # Common column name variations for emails
            email_cols = [col for col in df.columns if any(term in col.lower() for term in ['email', 'mail', 'address', 'to'])]
            if email_cols:
                email_col = email_cols[0]

        if not email_col:
            print(f"Warning: No email column found in {file_path}")
            print(f"Available columns: {list(df.columns)}")
            return email_reason_pairs

        # If status filtering is specified
        if status_column and target_status:
            status_col = None
            if status_column in df.columns:
                status_col = status_column
            else:
                status_cols = [col for col in df.columns if status_column.lower() in col.lower()]
                if status_cols:
                    status_col = status_cols[0]

            if status_col:
                filtered_df = df[df[status_col].str.contains(target_status, case=False, na=False)]
                emails = filtered_df[email_col].dropna().astype(str).tolist()
                for email in emails:
                    email_reason_pairs.append((email.strip(), reason or target_status))
                print(f"  Found {len(filtered_df)} rows with '{target_status}' in '{status_col}' column")
            else:
                print(f"Warning: Status column '{status_column}' not found in {file_path}")
                print(f"Available columns: {list(df.columns)}")
        else:
            # Extract all emails if no status filtering
            emails = df[email_col].dropna().astype(str).tolist()
            for email in emails:
                email_reason_pairs.append((email.strip(), reason or "unknown"))

    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        # Fallback to basic CSV reading
        try:
            with open(file_path, 'r', encoding='utf-8', newline='') as csvfile:
                # Try to detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                reader = csv.DictReader(csvfile, delimiter=delimiter)
                for row in reader:
                    # Look for email in any column that might contain emails
                    for key, value in row.items():
                        if value and '@' in str(value):
                            email_reason_pairs.append((str(value).strip(), reason or "unknown"))
                            break
        except Exception as e2:
            print(f"Failed to read {file_path} with fallback method: {e2}")

    return email_reason_pairs

def process_directory(directory_path, email_type="emails"):
    """
    Process all CSV files in a directory and extract emails.

    Args:
        directory_path: Path to directory containing CSV files
        email_type: Type of emails being processed (for logging)

    Returns:
        Path to the output file
    """
    directory = Path(directory_path)

    if not directory.exists():
        print(f"Error: Directory {directory_path} does not exist")
        return None

    all_email_reason_pairs = []
    processed_files = 0

    # Find all CSV files in the directory
    csv_files = list(directory.glob("*.csv"))

    if not csv_files:
        print(f"No CSV files found in {directory_path}")
        return None

    print(f"Processing {len(csv_files)} CSV files for {email_type}...")

    for csv_file in csv_files:
        print(f"Processing: {csv_file.name}")

        # Extract emails based on type
        if email_type == "hardbounces":
            # For hard bounces, look for "NoMailbox" in "messagecategory" column, emails in "to" column
            email_reason_pairs = extract_emails_from_csv(
                csv_file,
                email_column='to',
                status_column='messagecategory',
                target_status='NoMailbox',
                reason='hardbounce'
            )
        elif email_type == "unsubscribes":
            # For unsubscribes, look for "Unsubscribed" or "Abuse" in "Status" column, emails in "Email" column
            email_reason_pairs_unsubscribed = extract_emails_from_csv(
                csv_file,
                email_column='Email',
                status_column='Status',
                target_status='Unsubscribed',
                reason='unsubscribed'
            )
            email_reason_pairs_abuse = extract_emails_from_csv(
                csv_file,
                email_column='Email',
                status_column='Status',
                target_status='Abuse',
                reason='abuse'
            )
            email_reason_pairs = email_reason_pairs_unsubscribed + email_reason_pairs_abuse
        else:
            # Default: extract all emails
            email_reason_pairs = extract_emails_from_csv(csv_file)

        all_email_reason_pairs.extend(email_reason_pairs)
        processed_files += 1
        print(f"  Found {len(email_reason_pairs)} {email_type} in {csv_file.name}")

    # Remove duplicates while preserving email-reason pairs
    unique_emails = {}
    for email, reason in all_email_reason_pairs:
        if email not in unique_emails:
            unique_emails[email] = reason

    # Create "ext" folder in the same directory as the source files
    ext_folder = directory / "ext"
    ext_folder.mkdir(exist_ok=True)

    # Generate output filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{email_type}_emails_{timestamp}.csv"
    output_path = ext_folder / output_filename

    # Write results to output file
    with open(output_path, 'w', newline='', encoding='utf-8') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(['email', 'reason'])  # Header with reason column
        for email in sorted(unique_emails.keys()):
            writer.writerow([email, unique_emails[email]])

    print(f"\nSummary:")
    print(f"  Processed files: {processed_files}")
    print(f"  Total unique {email_type}: {len(unique_emails)}")
    print(f"  Output saved to: {output_path}")

    return output_path

def main():
    parser = argparse.ArgumentParser(description='Extract emails from Elastic Email CSV exports')
    parser.add_argument('--hardbounces-dir',
                       default=r"H:\Master Bounces and Unsubs\Bounces\hardbounces",
                       help='Directory containing hard bounce CSV files')
    parser.add_argument('--unsubscribes-dir',
                       default=r"H:\Master Bounces and Unsubs\Bounces\unsubscribed",
                       help='Directory containing unsubscribe CSV files')

    args = parser.parse_args()

    # Process hard bounces
    print("=" * 50)
    print("EXTRACTING HARD BOUNCE EMAILS")
    print("=" * 50)
    hardbounce_output = process_directory(args.hardbounces_dir, "hardbounces")

    print("\n" + "=" * 50)
    print("EXTRACTING UNSUBSCRIBE EMAILS")
    print("=" * 50)
    unsubscribe_output = process_directory(args.unsubscribes_dir, "unsubscribes")

    print(f"\n" + "=" * 50)
    print("EXTRACTION COMPLETE")
    print("=" * 50)
    if hardbounce_output:
        print(f"Hard bounce emails saved to: {hardbounce_output}")
    if unsubscribe_output:
        print(f"Unsubscribe emails saved to: {unsubscribe_output}")

if __name__ == "__main__":
    main()
