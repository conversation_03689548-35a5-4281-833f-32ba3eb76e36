import os
import glob
import pandas as pd
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

def create_gradient_progress():
    """Create a progress bar with gradient colors"""
    return Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
        "[progress.percentage]{task.percentage:>3.0f}%",
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=Console()
    )

def concatenate_csv_files():
    """
    Concatenate multiple CSV files from a specified directory
    """
    console = Console()
    
    # Display header
    header_text = Text("CSV Files Concatenator", style="bold magenta")
    console.print(Panel(header_text, expand=False))
    
    # Get directory path from user
    directory_path = input("Enter the directory path containing CSV files: ").strip('"\'')
    
    # Check if directory exists
    if not os.path.exists(directory_path):
        console.print(f"[red]Error: Directory not found: {directory_path}[/red]")
        return
    
    # Change to the directory
    original_dir = os.getcwd()
    os.chdir(directory_path)
    
    try:
        # Find all CSV files
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            console.print("[red]No CSV files found in the directory.[/red]")
            return
        
        console.print(f"[green]Found {len(csv_files)} CSV files:[/green]")
        for file in csv_files:
            console.print(f"  • {file}")
        
        # Ask for output filename prefix
        output_prefix = input("\nEnter output filename prefix (default: 'concatenated'): ").strip()
        if not output_prefix:
            output_prefix = "concatenated"
        
        # Initialize list to store dataframes
        all_dfs = []
        total_rows = 0
        
        # Create progress bar
        with create_gradient_progress() as progress:
            task = progress.add_task("Reading CSV files...", total=len(csv_files))
            
            for file in csv_files:
                try:
                    # Read CSV file
                    df = pd.read_csv(file, encoding='utf-8-sig', on_bad_lines='skip')
                    
                    # Standardize common column names
                    column_mapping = {
                        'Email ID': 'Email',
                        'email': 'Email',
                        'Author Name': 'Name',
                        'name': 'Name'
                    }
                    
                    for old_col, new_col in column_mapping.items():
                        if old_col in df.columns and new_col not in df.columns:
                            df.rename(columns={old_col: new_col}, inplace=True)
                    
                    all_dfs.append(df)
                    total_rows += len(df)
                    
                    progress.console.print(f"  ✓ Read {file}: {len(df)} rows")
                    
                except Exception as e:
                    progress.console.print(f"  ✗ Error reading {file}: {str(e)}")
                
                progress.advance(task)
        
        if not all_dfs:
            console.print("[red]No data could be read from any CSV files.[/red]")
            return
        
        # Concatenate all dataframes
        console.print("\n[yellow]Concatenating data...[/yellow]")
        combined_df = pd.concat(all_dfs, ignore_index=True)
        
        # Create merged directory if it doesn't exist
        merged_dir = "merged"
        os.makedirs(merged_dir, exist_ok=True)

        # Create output filename
        output_filename = os.path.join(merged_dir, f"{output_prefix}.csv")

        # Save the concatenated file
        console.print(f"\n[yellow]Saving concatenated file to merged folder...[/yellow]")
        combined_df.to_csv(output_filename, index=False, encoding='utf-8-sig')
        
        # Display summary
        summary_text = f"""
[bold green]Concatenation Complete![/bold green]

[bold]Summary:[/bold]
• Files processed: {len(csv_files)}
• Total rows: {len(combined_df)}
• Output file: {output_filename}

[bold]Columns in output:[/bold]
{', '.join(combined_df.columns.tolist())}
        """
        
        console.print(Panel(summary_text, title="Results", expand=False))
        
    except Exception as e:
        console.print(f"[red]An error occurred: {str(e)}[/red]")
    
    finally:
        # Change back to original directory
        os.chdir(original_dir)

if __name__ == "__main__":
    concatenate_csv_files()
