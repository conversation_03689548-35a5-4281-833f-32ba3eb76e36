import pandas as pd
import os
import re

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

path = input("Enter Path to Save: ")

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    desired_segment = match.group(1)
    print(desired_segment)
else:
    print("Desired segment not found")
    desired_segment = input("Enter a valid segment manually: ")  # Prompt user for input if regex fails

csn = desired_segment  # Use the extracted or manually entered segment

cluster_file = input("Enter Path of Cluster: ") 
newdata_file = input("Enter Path of Newdata: ") 
df_cluster = pd.read_csv(cluster_file)
df_newdata = pd.read_csv(newdata_file)

# Ensure 'Email' column exists in both DataFrames
if 'Email' not in df_cluster.columns or 'Email' not in df_newdata.columns:
    raise KeyError("Both input files must contain an 'Email' column.")

df_new_uniq = df_newdata[~(df_newdata.Email.isin(df_cluster.Email))]

column_list = ['Name', 'Email'] #["Author Name", "Email", "Article Title", "Affiliation"] 

# Ensure the output directory exists
if not os.path.exists(path):
    os.makedirs(path)

os.chdir(path)
df_new_uniq.to_csv(csn + "_new-unique.csv", columns=column_list, encoding='utf-8-sig', index=False)

print(f"Number of unique entries: {len(df_new_uniq)}")
