import os
import time
import glob
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configuration (move these to a config file or environment variables)
LOGIN_URL = "https://103-211-218-158.cprapid.com:2087/"
USERNAME = "root"
PASSWORD = "Magnus@12527"
DOWNLOAD_DIR = r"H:\Master Bounces and Unsubs\Postpanel Unsubs\mw"
DOWNLOAD_TIMEOUT = 100
WAIT_TIMEOUT = 10

def setup_driver():
    """Sets up the Chrome driver with the necessary options."""
    options = Options()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    options.add_argument("--headless")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    prefs = {
        "download.default_directory": DOWNLOAD_DIR,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
    }
    options.add_experimental_option("prefs", prefs)
    return webdriver.Chrome(options=options)

def delete_existing_csv_files(directory):
    """Deletes all existing CSV files in the specified directory."""
    for filename in glob.glob(os.path.join(directory, "*.csv")):
        os.remove(filename)

def handle_certificate_warning(driver):
    """Handles the certificate warning if it appears."""
    try:
        driver.find_element(By.XPATH, '//*[@id="details-button"]').click()
        driver.find_element(By.XPATH, '//*[@id="proceed-link"]').click()
        print("Certificate warning handled.")
    except NoSuchElementException:
        print("No certificate warning found.")
    except Exception as e:
        print(f"Error handling certificate warning: {e}")
        raise

def login(driver, url, username, password):
    """Logs into the website."""
    try:
        driver.get(url)
        handle_certificate_warning(driver)
        driver.find_element(By.ID, "user").send_keys(username)
        driver.find_element(By.ID, "pass").send_keys(password)
        driver.find_element(By.XPATH, "//button[@id='login_submit']").click()
        print("Logged in successfully.")
    except NoSuchElementException:
        print("Login failed: Could not find login elements.")
        raise
    except Exception as e:
        print(f"Login failed: {e}")
        raise

def navigate_to_list_accounts(driver):
    """Navigates to the List Accounts page."""
    try:
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, "//span[@class='cp-app__details-title'][normalize-space()='List Accounts']"))
        )
        driver.find_element(By.XPATH, "//span[@class='cp-app__details-title'][normalize-space()='List Accounts']").click()
        print("Navigated to List Accounts page.")
    except TimeoutException:
        print("Navigation failed: List Accounts link not found.")
        raise
    except Exception as e:
        print(f"Navigation failed: {e}")
        raise

def navigate_to_phpmyadmin(driver):
    """Navigates to phpMyAdmin."""
    try:
        driver.find_element(By.XPATH, "//input[@type='image']").click()
        driver.switch_to.window(driver.window_handles[1])
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, "//span[normalize-space()='phpMyAdmin']"))
        )
        driver.find_element(By.XPATH, "//span[normalize-space()='phpMyAdmin']").click()
        print("Navigated to phpMyAdmin.")
    except TimeoutException:
        print("Navigation failed: phpMyAdmin link not found.")
        raise
    except Exception as e:
        print(f"Navigation failed: {e}")
        raise

def navigate_to_emailinvitez_emailinvitez_db(driver):
    """Navigates to the emailinvitez_emailinvitez_db database."""
    try:
        driver.switch_to.window(driver.window_handles[2])
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, "//a[normalize-space()='emailinvitez_emailinvitez_db']"))
        )
        driver.find_element(By.XPATH, "//a[normalize-space()='emailinvitez_emailinvitez_db']").click()
        print("Navigated to emailinvitez_emailinvitez_db database.")
    except TimeoutException:
        print("Navigation failed: emailinvitez_emailinvitez_db database not found.")
        raise
    except Exception as e:
        print(f"Navigation failed: {e}")
        raise

def download_unsubscribers_csv(driver):
    """Downloads the unsubscribers CSV file."""
    try:
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="filterText"]'))
        )
        driver.find_element(By.XPATH, '//*[@id="filterText"]').send_keys('mw_list_subscriber')
        driver.find_element(By.XPATH, '//*[@id="row_tbl_116"]/th/a').click()
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="topmenu"]/li[6]/a'))
        )
        driver.find_element(By.XPATH, '//*[@id="topmenu"]/li[6]/a').click()
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="plugins"]/option[2]'))
        )
        driver.find_element(By.XPATH, '//*[@id="plugins"]/option[2]').click()
        driver.find_element(By.XPATH, '//*[@id="buttonGo"]').click()
        print("Download initiated.")
    except TimeoutException:
        print("Download failed: Download button not found.")
        raise
    except Exception as e:
        print(f"Download failed: {e}")
        raise

def wait_for_download_completion(timeout):
    """Waits for a CSV file to be downloaded in the specified directory."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if any(filename.endswith(".csv") for filename in os.listdir(DOWNLOAD_DIR)):
            print("Download completed.")
            return True
        time.sleep(1)
    print("Download timed out.")
    return False

def main():
    """Main function to run the script."""
    try:
        delete_existing_csv_files(DOWNLOAD_DIR)
        driver = setup_driver()
        driver.maximize_window()
        login(driver, LOGIN_URL, USERNAME, PASSWORD)
        navigate_to_list_accounts(driver)
        navigate_to_phpmyadmin(driver)
        navigate_to_emailinvitez_emailinvitez_db(driver)
        download_unsubscribers_csv(driver)
        wait_for_download_completion(DOWNLOAD_TIMEOUT)
        print('Downloaded Successfully! Wait for another Download...')
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
