import os
import pandas as pd
import openpyxl
import re

def extract_segment_from_path(path):
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        return match.group(1)
    else:
        print("Desired segment not found")
        return None

def rename_csv_to_xlsx(path):
    for filename in os.listdir(path):
        if filename.endswith('.csv'):
            old_path = os.path.join(path, filename)
            new_path = os.path.join(path, filename[:-4] + '.xlsx')  # Remove '.csv' and add '.xlsx'
            os.rename(old_path, new_path)

def process_files(path, sheetname, csn, output_folder):
    files_xls = [f for f in os.listdir(path) if f.endswith('xlsx')]
    df = pd.DataFrame()
    
    for f in files_xls:
        data = pd.read_excel(os.path.join(path, f), sheet_name=sheetname, engine='openpyxl')
        df = pd.concat([df, data], ignore_index=True)
    
    print(df)
    
    os.makedirs(output_folder, exist_ok=True)
    output_file_path = os.path.join(output_folder, f'{csn}_{sheetname}.csv')
    df.to_csv(output_file_path, index=False)

    return output_file_path

def main():
    path = input("Enter loc: ")
    os.chdir(path)

    csn = extract_segment_from_path(path)
    if not csn:
        return

    rename_csv_to_xlsx(path)

    sheetnames = ['Valid+BasicCheck+DEA', 'CatchAll_AcceptAll', 'Invalid']
    output_folders = ['valid', 'valid', 'invalid']
    
    output_paths = []
    for sheetname, output_folder in zip(sheetnames, output_folders):
        output_path = process_files(path, sheetname, csn, output_folder)
        if sheetname == 'Invalid':
            output_paths.append(output_path)

    # Concatenate all invalids with Master Hardbounces
    if output_paths:
        df_hb_all = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv")
        df_invalid = pd.read_csv(output_paths[-1])
        df_concat_allinvalids = pd.concat([df_invalid, df_hb_all], ignore_index=True)
        df_concat_allinvalids['Email'].to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv", mode='w+', index=False)

if __name__ == "__main__":
    main()
