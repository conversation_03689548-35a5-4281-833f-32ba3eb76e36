from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
import time
import csv
from datetime import datetime

def check_mautic_login(site, username, password, headless=False):
    """Check login for a Mautic site."""
    options = webdriver.ChromeOptions()
    if headless:
        options.add_argument("--headless=new")
    options.add_argument("--window-size=1920,1080")
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    
    driver = webdriver.Chrome(options=options)
    driver.maximize_window()  # Maximize window for better visibility
    
    # Extract site name from URL for display purposes
    site_name = site["url"].split("//")[1].split(".")[0]
    
    result = {
        "site_name": site_name,
        "url": site["url"],
        "status": "FAILED",
        "error": None,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        print(f"Checking {site_name}...")
        login_url = f"{site['url']}/s/login"
        driver.get(login_url)
        
        # Wait for login form to load - using the correct IDs
        try:
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))  # Changed from _username to username
            )
            password_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "password"))  # Changed from _password to password
            )
        except TimeoutException:
            result["error"] = "Login page not loaded or elements not found"
            return result
        
        # Clear fields first (in case there's any pre-filled data)
        username_field.clear()
        password_field.clear()
        
        # Try multiple methods to enter credentials
        try:
            # Method 1: Standard send_keys
            username_field.send_keys(username)
            password_field.send_keys(password)
            
            # Check if values were entered
            if not username_field.get_attribute('value'):
                # Method 2: JavaScript executor
                print("Using JavaScript to enter credentials...")
                driver.execute_script(f"document.getElementById('username').value = '{username}';")  # Updated ID
                driver.execute_script(f"document.getElementById('password').value = '{password}';")  # Updated ID
            
            # Method 3: Action chains if needed
            if not username_field.get_attribute('value'):
                print("Using action chains to enter credentials...")
                actions = ActionChains(driver)
                actions.move_to_element(username_field)
                actions.click()
                actions.send_keys(username)
                actions.move_to_element(password_field)
                actions.click()
                actions.send_keys(password)
                actions.perform()
        except Exception as e:
            print(f"Error entering credentials: {e}")
            # Continue anyway to try the login
        
        # Find and click login button
        try:
            login_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button.btn.btn-lg.btn-primary.btn-block[type='submit']"))
            )
            login_button.click()
        except Exception as e:
            print(f"Error clicking login button: {e}")
            # Try JavaScript click as fallback
            driver.execute_script("document.querySelector('button.btn.btn-lg.btn-primary.btn-block[type=\"submit\"]').click();")
        
        # Wait for redirect after login
        time.sleep(5)  # Increased wait time
        
        # Check for successful login indicators
        if "/s/login" not in driver.current_url:
            # Additional check for dashboard elements
            try:
                WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".mautic-brand, .app-sidebar"))
                )
                result["status"] = "SUCCESS"
            except TimeoutException:
                result["error"] = "Dashboard elements not found after login"
        else:
            # Check for error messages
            try:
                error_msg = driver.find_element(By.CSS_SELECTOR, ".alert-danger, .help-block").text
                result["error"] = f"Login failed: {error_msg}"
            except NoSuchElementException:
                result["error"] = "Login failed, remained on login page"
        
    except Exception as e:
        result["error"] = str(e)
    finally:
        driver.quit()
        
    return result

if __name__ == "__main__":
    # List of Mautic sites to check
    mautic_sites = [
        {"url": "https://mail1.sciconferncegroup.com/mautic"},
        {"url": "https://mail1.sciconferencecommittee.com/mautic"},
        {"url": "https://mail1.worldconnect-summit.com/mautic"},
        {"url": "https://mail1.sciconfconvention.com/mautic"},
        {"url": "https://mail1.scientificabstract.com/mautic"},
        {"url": "https://mail1.scimedabstract.com/mautic"},
        {"url": "https://mail1.alerts-notify.com/mautic"},
        {"url": "https://mail1.sciconferenceagenda.com/mautic"},
        {"url": "https://mail1.world-researchmeet.com/mautic"},
        {"url": "https://mail1.sci-medconfcongress.com/mautic"},
        {"url": "https://mail1.scimedconferencesummit.com/mautic"},
        {"url": "https://mail1.scispeakermeetup.com/mautic"}
    ]
    
    # Credentials provided
    username = "magnusgroup"
    password = "magnusgroup@!@#$%"
    
    # Output file for results
    output_file = f"mautic_login_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # Run the check in visible mode
    print("Starting Mautic login status check in visible mode...")
    
    results = []
    failed_sites = []
    for site in mautic_sites:
        result = check_mautic_login(site, username, password, headless=False)
        results.append(result)
        
        # Print result
        status_str = f"✅ SUCCESS" if result["status"] == "SUCCESS" else f"❌ FAILED: {result['error']}"
        print(f"{result['site_name']}: {status_str}")
        
        # Track failed sites
        if result["status"] == "FAILED":
            failed_sites.append(result)
        
        time.sleep(2)  # Pause between sites
    
    # Save results to CSV
    with open(output_file, 'w', newline='') as csvfile:
        fieldnames = ['site_name', 'url', 'status', 'error', 'timestamp']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    
    print(f"\nResults saved to {output_file}")
    
    # Print summary
    success_count = sum(1 for r in results if r["status"] == "SUCCESS")
    print(f"\nSUMMARY: {success_count}/{len(mautic_sites)} sites login successful")
    
    # Print failed sites with full URLs and errors
    if failed_sites:
        print("\nFAILED SITES DETAILS:")
        print("-" * 80)
        for site in failed_sites:
            login_url = f"{site['url']}/s/login"
            print(f"Site: {site['site_name']}")
            print(f"Login URL: {login_url}")
            print(f"Error: {site['error']}")
            print("-" * 80)










