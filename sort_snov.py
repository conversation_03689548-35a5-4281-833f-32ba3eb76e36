import os
import sys
import pandas as pd
from pathlib import Path
import shutil  # For creating backup files

def clean_name(name):
    """
    Clean name by removing text after the first comma.

    Args:
        name: The name to clean

    Returns:
        The cleaned name
    """
    try:
        # Convert to string and take only the part before the first comma
        return str(name).split(',')[0].strip()
    except:
        # Return the original name if there's an error
        return name

def update_master_hardbounces(invalid_file_path, master_file_path):
    """
    Update the master hardbounces file with emails from the invalid_snov.csv file.

    Args:
        invalid_file_path: Path to the invalid_snov.csv file
        master_file_path: Path to the master hardbounces file
    """
    print("\nUpdating master hardbounces file...")

    try:
        # Check if invalid file exists
        if not os.path.exists(invalid_file_path):
            print(f"Error: Invalid file not found at {invalid_file_path}")
            return

        # Check if master file exists
        if not os.path.exists(master_file_path):
            print(f"Error: Master hardbounces file not found at {master_file_path}")
            return

        # Read the invalid emails file
        df_invalid = pd.read_csv(invalid_file_path)

        if 'Email' not in df_invalid.columns:
            print("Error: Invalid file does not contain 'Email' column")
            return

        # Read the master hardbounces file
        df_master = pd.read_csv(master_file_path)

        # Create a backup of the master file
        backup_file = f"{master_file_path}.backup"
        shutil.copy2(master_file_path, backup_file)
        print(f"Created backup of master hardbounces file at {backup_file}")

        # Get only the Email column from invalid file
        invalid_emails = df_invalid[['Email']]

        # Ensure master file has Email column
        if 'Email' not in df_master.columns:
            df_master['Email'] = ''
            print("Warning: Added missing Email column to master hardbounces file")

        # Get current count of emails in master file
        original_count = len(df_master)

        # Combine invalid emails with master file
        df_combined = pd.concat([invalid_emails, df_master[['Email']]], ignore_index=True)

        # Remove duplicates
        df_combined.drop_duplicates(subset=['Email'], inplace=True)

        # Calculate how many new emails were added
        new_emails_count = len(df_combined) - original_count

        # Save the updated master file
        df_combined.to_csv(master_file_path, index=False, encoding='utf-8-sig')

        print(f"Successfully updated master hardbounces file with {new_emails_count} new invalid emails")
        print(f"Total emails in master hardbounces file: {len(df_combined)}")

    except Exception as e:
        print(f"Error updating master hardbounces file: {str(e)}")

def filter_excel_files(input_dir=None, output_filename=None):
    # If no input directory is provided, use current directory
    if not input_dir:
        print("No directory specified. Using current directory.")
        input_dir = os.getcwd()

    # Validate the input directory
    if not os.path.isdir(input_dir):
        print(f"Error: '{input_dir}' is not a valid directory.")
        return

    # Create a "sorted" directory if it doesn't exist
    sorted_dir = os.path.join(input_dir, "sorted")
    if not os.path.exists(sorted_dir):
        os.makedirs(sorted_dir)
        print(f"Created directory: {sorted_dir}")

    # Create an "invalid" directory if it doesn't exist
    invalid_dir = os.path.join(input_dir, "invalid")
    if not os.path.exists(invalid_dir):
        os.makedirs(invalid_dir)
        print(f"Created directory: {invalid_dir}")

    # Create a DataFrame to store all invalid email entries
    all_invalid_data = pd.DataFrame()

    # Find all Excel files in the directory
    excel_files = list(Path(input_dir).glob("*.xlsx"))

    if not excel_files:
        print(f"No Excel files found in {input_dir}")
        return

    print(f"Found {len(excel_files)} Excel files.")

    # Process each Excel file individually
    total_processed = 0
    total_invalid = 0

    for file_path in excel_files:
        try:
            print(f"Processing {file_path.name}...")

            # Read the Excel file
            df = pd.read_excel(file_path)

            # Check if required columns exist
            required_columns = ["Email", "Email status", "Full name"]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                print(f"Warning: File {file_path.name} is missing these required columns: {missing_columns}")
                print("Skipping this file.")
                continue

            # Filter rows where "Email status" is "not valid"
            invalid_df = df[df["Email status"] == "not valid"]

            # Add invalid entries to the combined invalid DataFrame
            if not invalid_df.empty:
                # Keep only "Email" and "Full name" columns for invalid entries
                invalid_entries = invalid_df[["Full name", "Email"]]
                invalid_entries = invalid_entries.rename(columns={"Full name": "Name"})
                # Clean the names by removing text after the first comma
                invalid_entries["Name"] = invalid_entries["Name"].apply(clean_name)
                all_invalid_data = pd.concat([all_invalid_data, invalid_entries], ignore_index=True)
                total_invalid += len(invalid_df)

            # Filter out rows where "Email status" is "not valid"
            filtered_df = df[df["Email status"] != "not valid"]

            # Keep only "Email" and "Full name" columns and rename "Full name" to "Name"
            filtered_df = filtered_df[["Full name", "Email"]]
            filtered_df = filtered_df.rename(columns={"Full name": "Name"})
            # Clean the names by removing text after the first comma
            filtered_df["Name"] = filtered_df["Name"].apply(clean_name)

            # Get the filename without extension
            file_name = os.path.splitext(file_path.name)[0]
            output_filename = f"{file_name}_sorted.xlsx"
            individual_output_file = os.path.join(sorted_dir, output_filename)

            # Save the filtered data to a new Excel file
            if not filtered_df.empty:
                try:
                    filtered_df.to_excel(individual_output_file, index=False, encoding='utf-8-sig')
                    print(f"Successfully saved {len(filtered_df)} valid email entries to {individual_output_file}")
                    total_processed += len(filtered_df)
                except Exception as e:
                    print(f"Error saving output file for {file_path.name}: {str(e)}")
            else:
                print(f"No valid data found in {file_path.name} to save.")

        except Exception as e:
            print(f"Error processing {file_path.name}: {str(e)}")

    # Save all invalid entries to a CSV file
    invalid_file = os.path.join(invalid_dir, "invalid_snov.csv")
    if not all_invalid_data.empty:
        try:
            all_invalid_data.to_csv(invalid_file, index=False, encoding='utf-8-sig')
            print(f"\nSuccessfully saved {total_invalid} invalid email entries to {invalid_file}")

            # Always update master hardbounces file (hard-coded)
            master_file_path = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\Master_Hardbounces.csv"
            update_master_hardbounces(invalid_file, master_file_path)

        except Exception as e:
            print(f"Error saving invalid entries: {str(e)}")
    else:
        print("\nNo invalid email entries found.")

    print(f"\nTotal processed: {total_processed} valid email entries across all files")

if __name__ == "__main__":
    print("Email Validator - Filter out invalid emails from Excel files")
    print("=" * 60)

    # Parse command line arguments
    input_directory = None
    output_filename = None

    # Check if directory is provided as command line argument
    if len(sys.argv) > 1:
        input_directory = sys.argv[1]
        print(f"Using directory: {input_directory}")

        # Check if output filename is provided
        if len(sys.argv) > 2:
            output_filename = sys.argv[2]
            print(f"Using output filename: {output_filename}")
    else:
        # If no arguments, prompt for directory
        input_directory = input("Enter the directory path containing Excel files: ").strip()
        if not input_directory:
            input_directory = None  # Will use current directory

        # Prompt for output filename
        output_filename = input("Enter the name for the output Excel file (without extension): ").strip()

    filter_excel_files(input_directory, output_filename)
    print("\nDone!")
