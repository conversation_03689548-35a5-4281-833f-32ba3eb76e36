import os
import sys
import warnings
from selenium import webdriver
from selenium.webdriver.common.by import By
import time
import contextlib

# Suppress all warnings
warnings.filterwarnings("ignore")

# Suppress Chrome/Selenium warnings by redirecting stderr temporarily
@contextlib.contextmanager
def suppress_stderr():
    with open(os.devnull, "w") as devnull:
        old_stderr = sys.stderr
        sys.stderr = devnull
        try:
            yield
        finally:
            sys.stderr = old_stderr

timestr = time.strftime("%d%m%Y")

# Selenium setup with comprehensive Chrome options to suppress warnings
options = webdriver.ChromeOptions()

# Suppress console logging messages
options.add_experimental_option("excludeSwitches", ["enable-logging"])
options.add_experimental_option('useAutomationExtension', False)

# Add logging preferences to capture browser logs and suppress absl warnings
options.add_argument('--log-level=3')  # Set log level to errors only
options.add_argument('--silent')  # Suppress most output
options.add_argument('--disable-logging')  # Disable Chrome logging
options.add_argument('--disable-log-file')  # Don't create log files

# Headless mode and other performance settings
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--window-size=1920,1080")
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

# Disable extensions and other features that might cause errors
options.add_argument("--disable-extensions")
options.add_argument("--disable-notifications")
options.add_argument("--disable-background-timer-throttling")
options.add_argument("--disable-backgrounding-occluded-windows")
options.add_argument("--disable-renderer-backgrounding")
options.add_argument("--disable-features=TranslateUI")
options.add_argument("--disable-ipc-flooding-protection")

# Disable GPU-related features that cause SharedImageManager errors
options.add_argument("--disable-software-rasterizer")
options.add_argument("--disable-background-networking")
options.add_argument("--disable-sync")
options.add_argument("--disable-default-apps")
options.add_argument("--disable-web-security")

# Disable voice/speech features that cause transcription warnings
options.add_argument("--disable-speech-api")
options.add_argument("--disable-speech-synthesis-api")
options.add_argument("--disable-features=VoiceInteraction")
options.add_argument("--disable-features=VoiceSearchHotword")

# Additional flags to suppress various Chrome warnings and features
options.add_argument("--disable-component-extensions-with-background-pages")
options.add_argument("--disable-background-mode")
options.add_argument("--disable-client-side-phishing-detection")
options.add_argument("--disable-hang-monitor")
options.add_argument("--disable-prompt-on-repost")
options.add_argument("--disable-domain-reliability")
options.add_argument("--disable-component-update")
options.add_argument("--disable-features=MediaRouter")
options.add_argument("--disable-features=VizDisplayCompositor")

# Comprehensive suppression for absl and voice warnings
options.add_argument("--disable-features=VoiceInteraction,VoiceSearchHotword,MediaFoundationVideoCapture")
options.add_argument("--disable-features=AudioServiceOutOfProcess")
options.add_argument("--disable-features=AudioServiceSandbox")
options.add_argument("--disable-blink-features=AutomationControlled")
options.add_argument("--disable-infobars")
options.add_argument("--disable-dev-tools")
options.add_argument("--disable-plugins")
options.add_argument("--disable-plugins-discovery")
options.add_argument("--disable-preconnect")
options.add_argument("--disable-translate")
options.add_argument("--disable-ipc-flooding-protection")
options.add_argument("--no-first-run")
options.add_argument("--no-service-autorun")
options.add_argument("--password-store=basic")
options.add_argument("--use-mock-keychain")
options.add_argument("--disable-features=Translate")

# Environment variable to suppress absl warnings
os.environ['GLOG_minloglevel'] = '3'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# Upload preferences
prefs = {
    "upload.default_directory": r"H:\Master Bounces and Unsubs\Replied Ext\Prev_Rep_bounces_csv",
    "upload.prompt_for_download": False,
    "upload.directory_upgrade": True,
    "browser.download.show_plugins_in_list": False,
    "browser.download.folderList": 2,
    "browser.download.manager.showWhenStarting": False
}
options.add_experimental_option('prefs', prefs)

# Initialize Chrome browser with stderr suppression
print("Setting up Chrome browser...")
with suppress_stderr():
    driver = webdriver.Chrome(options=options)
driver.set_window_size(1920, 1080)  # Ensure proper rendering

# Login to the website
print("Logging in to DBMS...")
driver.get("http://swa.dbms.org.in/")
driver.find_element(By.ID, "username").send_keys("<EMAIL>")
driver.find_element(By.ID, "password").send_keys("Magnus@123")
time.sleep(3)
driver.find_element(By.ID, "btnSubmit").click()
time.sleep(3)

print("Starting upload process...")

try:
    # Upload 1: PP_Global_Unsubscribers.csv to MGConferences_unsubscribers
    print("Uploading PP_Global_Unsubscribers.csv...")
    driver.get("http://swa.dbms.org.in/data_filter_sets")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
    button = driver.find_element(By.ID, 'up_file')

    # Define the file path for MGConferences_unsubscribers
    file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\PP_Global_Unsubscribers.csv")

    # Direct file upload using send_keys
    button.send_keys(file_path)
    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[MGConferences_unsubscribers] PP_Global_Unsubscribers.csv Upload Completed!')

    # Upload 2: Master_Hardbounces.csv to Hard_bouncers
    print("Uploading Master_Hardbounces.csv...")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=182&datafiltername=Hard_bouncers")
    button = driver.find_element(By.ID, 'up_file')

    # Define the file path for Hard_bouncers
    file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\Master_Hardbounces.csv")

    # Direct file upload using send_keys
    button.send_keys(file_path)
    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[Hard_bouncers] Master_Hardbounces.csv Upload Completed!')

    # Upload 3: Master_Unsubscribes.csv to MGConferences_unsubscribers
    print("Uploading Master_Unsubscribes.csv...")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
    button = driver.find_element(By.ID, 'up_file')

    # Define the file path for MGConferences_unsubscribers (second upload)
    file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\Master_Unsubscribes.csv")

    # Direct file upload using send_keys
    button.send_keys(file_path)
    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[MGConferences_unsubscribers] Master_Unsubscribes.csv Upload Completed!')

    # Upload 4: Conferences_unsubscribes.csv to MGConferences_unsubscribers
    print("Uploading Conferences_unsubscribes.csv...")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
    button = driver.find_element(By.ID, 'up_file')

    # Define the file path for MGConferences_unsubscribers (third upload)
    file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_unsubs\merged\Conferences_unsubscribes.csv")

    # Direct file upload using send_keys
    button.send_keys(file_path)
    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[MGConferences_unsubscribers] Conferences_unsubscribes.csv Upload Completed!')

    # Upload 5: Journals_unsubscribes.csv to MMCJournals_unsubscribers
    print("Uploading Journals_unsubscribes.csv to MMCJournals_unsubscribers...")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=18&datafiltername=MMCJournals_unsubscribers")
    button = driver.find_element(By.ID, 'up_file')

    # Define the file path for MMCJournals_unsubscribers
    file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe\ee_unsubs\merged\Journals_unsubscribes.csv")

    # Direct file upload using send_keys
    button.send_keys(file_path)
    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[MMCJournals_unsubscribers] Journals_unsubscribes.csv Upload Completed!')

    # Upload 6: Journals_unsubscribes.csv to MTJournals_unsubscribers
    print("Uploading Journals_unsubscribes.csv to MTJournals_unsubscribers...")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=14&datafiltername=MTJournals_unsubscribers")
    button = driver.find_element(By.ID, 'up_file')

    # Define the file path for MTJournals_unsubscribers
    file_path = os.path.abspath(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe\ee_unsubs\merged\Journals_unsubscribes.csv")

    # Direct file upload using send_keys
    button.send_keys(file_path)
    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[MTJournals_unsubscribers] Journals_unsubscribes.csv Upload Completed!')

    print("All uploads completed successfully!")

except Exception as e:
    print(f"Error during upload process: {str(e)}")

finally:
    # Close the browser
    print("Closing browser...")
    driver.quit()
    print("Process completed.")
