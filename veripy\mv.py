import os
import glob
import pandas as pd
import re
from tqdm import tqdm

# Function to extract segment from path
def extract_segment_from_path(path):
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        return match.group(1)
    else:
        print("Desired segment not found")
        return None

def process_files(path, csn):
    os.chdir(path)

    csv_files = glob.glob('*.csv')
    if not csv_files:
        print("No CSV files found in the directory.")
        return

    # Initialize progress bar
    total_steps = len(csv_files) + 2  # Reading files + processing invalid + processing valid
    progress_bar = tqdm(total=total_steps, desc="Processing", unit="step", ncols=80)

    # Step 1: Read and concatenate CSV files
    df_hbs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)
    progress_bar.update(len(csv_files))  # Update progress for reading files

    # Step 2: Process invalid results
    invalid_options = ['unknown', 'invalid', 'disposable']
    df_invalid = df_hbs[df_hbs['result'].isin(invalid_options)]
    os.makedirs("invalid", exist_ok=True)
    df_invalid['Email'].to_csv(f'invalid/{csn}_invalid.csv', index=False)
    progress_bar.update(1)  # Update progress for processing invalid results

    # Step 3: Process valid results
    valid_options = ['ok', 'catch_all']
    df_valid = df_hbs[df_hbs['result'].isin(valid_options)]
    os.makedirs("valid", exist_ok=True)

    if "Author Name" in df_valid.columns:
        df_valid.rename(columns={"Author Name": "Name"}, inplace=True)

    df_valid.to_csv(f'valid/{csn}_valid.csv', columns=['Name', 'Email'], index=False)
    progress_bar.update(1)  # Update progress for processing valid results

    #Joining invalids in Hardbounces
    df_hb_all = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv")
    df_concat_allinvalids = pd.concat([df_invalid, df_hb_all], axis=0)
    df_concat_allinvalids.Email.to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv", mode='w', index=False)
    
    # Close progress bar
    progress_bar.close()

    # Print lengths of invalid and valid dataframes
    print(f"Invalid records: {len(df_invalid)}")
    print(f"Valid records: {len(df_valid)}")

if __name__ == "__main__":
    path = input("Enter loc: ")
    csn = extract_segment_from_path(path)
    if csn:
        process_files(path, csn)
    else:
        print("Path segment extraction failed. Please check the path.")