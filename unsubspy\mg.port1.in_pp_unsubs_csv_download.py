import os
import time
import glob
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configuration (move these to a config file or environment variables)
LOGIN_URL = "https://mg.port1.in/"
USERNAME = "admin"
PASSWORD = "Admin@890"
DOWNLOAD_DIR = r"H:\Master Bounces and Unsubs\Postpanel Unsubs\port1"
DOWNLOAD_TIMEOUT = 10
WAIT_TIMEOUT = 10

def setup_driver():
    """Sets up the Chrome driver with the necessary options."""
    options = Options()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    options.add_argument("--headless")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    prefs = {
        "download.default_directory": DOWNLOAD_DIR,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
    }
    options.add_experimental_option("prefs", prefs)
    return webdriver.Chrome(options=options)

def delete_existing_csv_files(directory):
    """Deletes all existing CSV files in the specified directory."""
    for filename in glob.glob(os.path.join(directory, "*.csv")):
        os.remove(filename)

def login(driver, url, username, password):
    """Logs into the website."""
    try:
        driver.get(url)
        driver.find_element(By.ID, "name").send_keys(username)
        driver.find_element(By.ID, "password").send_keys(password)
        driver.find_element(By.XPATH, "//button[@id='login']").click()
        print("Logged in successfully.")
    except NoSuchElementException:
        print("Login failed: Could not find login elements.")
        raise
    except Exception as e:
        print(f"Login failed: {e}")
        raise

def navigate_to_unsubscribers(driver):
    """Navigates to the Unsubscribers page."""
    try:
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.LINK_TEXT, "Unsubscribers"))
        )
        driver.find_element(By.LINK_TEXT, "Unsubscribers").click()
        print("Navigated to Unsubscribers page.")
    except TimeoutException:
        print("Navigation failed: Unsubscribers link not found.")
        raise
    except Exception as e:
        print(f"Navigation failed: {e}")
        raise

def download_unsubscribers_csv(driver):
    """Downloads the unsubscribers CSV file."""
    try:
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, "//div[@class='panel-heading']//button[2]"))
        )
        driver.find_element(By.XPATH, "//div[@class='panel-heading']//button[2]").click()
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.presence_of_element_located((By.XPATH, "//input[@id='global_un_s']"))
        )
        driver.find_element(By.XPATH, "//input[@id='global_un_s']").click()
        download_button = driver.find_element(By.XPATH, "//button[normalize-space()='Download']")
        # Scroll to the element if it is not visible
        driver.execute_script("arguments[0].scrollIntoView(true);", download_button)
        download_button.click()
        print("Download initiated.")
    except TimeoutException:
        print("Download failed: Download button not found.")
        raise
    except Exception as e:
        print(f"Download failed: {e}")
        raise

def wait_for_download_completion(directory, timeout):
    """Waits for a CSV file to be downloaded in the specified directory."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if any(filename.endswith(".csv") for filename in os.listdir(directory)):
            print("Download completed.")
            return True
        time.sleep(1)
    print("Download timed out.")
    return False

def main():
    """Main function to run the script."""
    try:
        delete_existing_csv_files(DOWNLOAD_DIR)
        driver = setup_driver()
        driver.maximize_window()
        login(driver, LOGIN_URL, USERNAME, PASSWORD)
        navigate_to_unsubscribers(driver)
        download_unsubscribers_csv(driver)
        wait_for_download_completion(DOWNLOAD_DIR, DOWNLOAD_TIMEOUT)
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
