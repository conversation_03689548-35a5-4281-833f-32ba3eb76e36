import pandas as pd
import os
import glob

def merge_excel_worksheets_simple():
    """
    Simple version to merge worksheets from multiple Excel workbooks.
    """
    
    # Source directory containing Excel workbooks
    source_dir = r"H:\mailwizz\mathews mailing\conferences\For Kutools"
    
    # Output directory for merged files
    output_dir = os.path.join(source_dir, "merged")
    
    # Expected worksheet names
    worksheet_names = [
        "Valid+BasicCheck+DEA",
        "CatchAll_AcceptAll", 
        "Invalid",
        "Invalid Typo Bad",
        "Role"
    ]
    
    print(f"Source directory: {source_dir}")
    print(f"Output directory: {output_dir}")
    
    # Check if source directory exists
    if not os.path.exists(source_dir):
        print(f"Error: Source directory not found: {source_dir}")
        return
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all Excel files in the source directory
    excel_files = glob.glob(os.path.join(source_dir, "*.xlsx"))
    
    if not excel_files:
        print(f"No Excel files found in {source_dir}")
        return
    
    print(f"Found {len(excel_files)} Excel files to process")
    
    # Dictionary to store merged data for each worksheet type
    merged_data = {sheet_name: [] for sheet_name in worksheet_names}
    
    # Process each Excel file
    for i, excel_file in enumerate(excel_files, 1):
        file_name = os.path.basename(excel_file)
        print(f"Processing {i}/{len(excel_files)}: {file_name}")
        
        try:
            # Read all sheets from the Excel file
            excel_data = pd.read_excel(excel_file, sheet_name=None)
            
            # Process each expected worksheet
            for sheet_name in worksheet_names:
                if sheet_name in excel_data:
                    df = excel_data[sheet_name]
                    
                    # Check if the required columns exist
                    if 'Author Name' in df.columns and 'Email' in df.columns:
                        # Select only the required columns
                        df_filtered = df[['Author Name', 'Email']].copy()

                        # Remove empty rows
                        df_filtered = df_filtered.dropna(subset=['Email'])

                        # Add to merged data
                        merged_data[sheet_name].append(df_filtered)
                        
                        print(f"  ✓ {sheet_name}: {len(df_filtered)} rows")
                    else:
                        print(f"  ⚠ {sheet_name}: Missing required columns")
                else:
                    print(f"  ⚠ {sheet_name}: Sheet not found")
            
        except Exception as e:
            print(f"  ✗ Error processing {file_name}: {str(e)}")
    
    # Save merged data for each worksheet type
    print("\nSaving merged files...")

    for sheet_name, data_list in merged_data.items():
        if data_list:
            # Combine all dataframes for this sheet type
            combined_df = pd.concat(data_list, ignore_index=True)

            # Remove duplicates based on Email column
            initial_count = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['Email'], keep='first')
            final_count = len(combined_df)
            duplicates_removed = initial_count - final_count

            # Save to Excel file with plain text headers
            output_file = os.path.join(output_dir, f"{sheet_name}.xlsx")

            # Use xlsxwriter engine for better formatting control
            with pd.ExcelWriter(output_file, engine='xlsxwriter') as writer:
                combined_df.to_excel(writer, sheet_name='Sheet1', index=False)

                # Get the workbook and worksheet objects
                workbook = writer.book
                worksheet = writer.sheets['Sheet1']

                # Define header format (plain text, left-aligned)
                header_format = workbook.add_format({
                    'bold': False,
                    'align': 'left',
                    'valign': 'top'
                })

                # Apply header format to the first row
                for col_num, value in enumerate(combined_df.columns.values):
                    worksheet.write(0, col_num, value, header_format)

            print(f"✓ Saved {sheet_name}.xlsx: {final_count} unique records")
            if duplicates_removed > 0:
                print(f"  Removed {duplicates_removed} duplicate emails")
        else:
            print(f"✗ No data found for {sheet_name}")

    print("\n" + "="*50)
    print("MERGE PROCESS COMPLETED!")
    print("="*50)

if __name__ == "__main__":
    print("Excel Worksheet Merger")
    print("Merging worksheets from multiple Excel workbooks")
    print("-" * 50)
    
    merge_excel_worksheets_simple()
    
    print("\nProcess completed! Check the 'merged' directory for output files.")
