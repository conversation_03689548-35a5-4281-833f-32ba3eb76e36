import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager  # Import ChromeDriverManager

# --- Configuration ---
DOWNLOAD_DIR = r"H:\Master Bounces and Unsubs\Postpanel Unsubs"
LOGIN_URL = "https://admin.magnusgroup.biz/admin-login.php"
UNSUBSCRIBES_URL = 'https://admin.magnusgroup.biz/unsubscribes_list.php'
USERNAME = "<EMAIL>"
PASSWORD = "Magnus@123"
DOWNLOAD_TIMEOUT = 120  # Increased timeout for download completion
WAIT_TIMEOUT = 10 #timeout for wait

# --- Functions ---

def setup_driver(download_dir):
    """Sets up the Chrome WebDriver with desired options."""
    options = webdriver.ChromeOptions()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    #options.add_argument("--headless=new")  # Modern headless mode
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    prefs = {
        "download.default_directory": download_dir,
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
    }
    options.add_experimental_option('prefs', prefs)
    # Use ChromeDriverManager to automatically manage the ChromeDriver
    service = webdriver.chrome.service.Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    driver.maximize_window()
    return driver

def login(driver, url, username, password):
    """Logs into the admin panel."""
    driver.get(url)
    driver.find_element(By.ID, "name").send_keys(username)
    driver.find_element(By.ID, "password").send_keys(password)
    driver.find_element(By.XPATH, "//button[@id='login']").click()

def download_unsubscribes(driver, url, timeout):
    """Downloads the unsubscribes list."""
    driver.get(url)
    
    # Wait for the download button to be clickable
    wait = WebDriverWait(driver, WAIT_TIMEOUT)
    download_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[normalize-space()='Download Unsubscribes']")))
    download_button.click()

    # Wait for the dropdown to be clickable
    dropdown_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[@class='filter-option pull-left']")))
    dropdown_button.click()

    # Wait for the "All" option to be clickable
    all_option = wait.until(EC.element_to_be_clickable((By.XPATH, "//span[normalize-space()='All']")))
    all_option.click()

    # Wait for the "Works" label to be clickable
    works_label = wait.until(EC.element_to_be_clickable((By.XPATH, "//label[normalize-space()='Works:']")))
    works_label.click()

    # Wait for the global_uns checkbox to be clickable
    global_uns_checkbox = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@id='global_uns']")))
    global_uns_checkbox.click()

    # Wait for the submit button to be clickable
    submit_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@id='submit_key']")))
    submit_button.click()

    # Wait for the download to complete (check for a new file)
    print("Waiting for download to complete...")
    start_time = time.time()
    while True:
        if time.time() - start_time > timeout:
            raise TimeoutException("Download timed out!")
        
        csv_files = [f for f in os.listdir(DOWNLOAD_DIR) if f.endswith('.csv')]
        if csv_files:
            print("Download completed.")
            break
        time.sleep(1)

def clear_existing_csvs(directory):
    """Deletes all existing CSV files in the specified directory."""
    os.chdir(directory)
    for file in os.listdir(directory):
        if file.endswith(".csv"):
            os.remove(file)
    print("Existing CSV files deleted.")

def main():
    """Main function to orchestrate the process."""
    clear_existing_csvs(DOWNLOAD_DIR)
    driver = setup_driver(DOWNLOAD_DIR)
    try:
        login(driver, LOGIN_URL, USERNAME, PASSWORD)
        download_unsubscribes(driver, UNSUBSCRIBES_URL, DOWNLOAD_TIMEOUT)
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        driver.quit()

if __name__ == "__main__":
    main()
