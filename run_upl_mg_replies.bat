@echo off
setlocal enabledelayedexpansion

:: Set console colors and title
title Magnus Group Replies Upload Workflow
color 0A

echo ===============================================
echo    MAGNUS GROUP REPLIES UPLOAD WORKFLOW
echo ===============================================
echo Started at: %date% %time%
echo.
echo This batch file will execute the following processes in order:
echo 1. Generate Replied Bounces Report (rep.bat)
echo 2. Auto Upload to Magnus Group (autoup.bat)
echo.
echo ===============================================
echo.

:: Initialize status tracking
set "TOTAL_STEPS=2"
set "SUCCESS_COUNT=0"
set "FAILED_COUNT=0"
set "error_occurred=false"
set "STEP_1_STATUS=PENDING"
set "STEP_2_STATUS=PENDING"

:: Step 1: Generate Replied Bounces Report
echo [1/2] Step 1: Running Replied Bounces Report (rep.bat)...
echo ----------------------------------------
call rep.bat
if !errorlevel! neq 0 (
    echo ERROR: rep.bat failed with error code !errorlevel!
    set "STEP_1_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: rep.bat completed successfully
    set "STEP_1_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.

:: Step 2: Auto Upload to Magnus Group
echo [2/2] Step 2: Running Auto Upload (autoup.bat)...
echo ----------------------------------------
call autoup.bat
if !errorlevel! neq 0 (
    echo ERROR: autoup.bat failed with error code !errorlevel!
    set "STEP_2_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: autoup.bat completed successfully
    set "STEP_2_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.

:: Display completion summary
if "!error_occurred!"=="false" (
    echo ===============================================
    echo    WORKFLOW COMPLETED SUCCESSFULLY!
    echo ===============================================
    echo.
    echo All 2 steps completed successfully at %date% %time%
    echo.
    echo Summary:
    echo [OK] Generate Replied Bounces Report
    echo [OK] Auto Upload to Magnus Group
    echo.
    echo Statistics:
    echo - Total Steps: !TOTAL_STEPS!
    echo - Successful: !SUCCESS_COUNT!
    echo - Failed: !FAILED_COUNT!
    echo.
) else (
    echo ===============================================
    echo    WORKFLOW COMPLETED WITH ERRORS!
    echo ===============================================
    echo.
    echo Workflow completed at %date% %time%
    echo.
    echo Summary:
    if "!STEP_1_STATUS!"=="SUCCESS" (echo [OK] Generate Replied Bounces Report) else (echo [FAILED] Generate Replied Bounces Report)
    if "!STEP_2_STATUS!"=="SUCCESS" (echo [OK] Auto Upload to Magnus Group) else (echo [FAILED] Auto Upload to Magnus Group)
    echo.
    echo Statistics:
    echo - Total Steps: !TOTAL_STEPS!
    echo - Successful: !SUCCESS_COUNT!
    echo - Failed: !FAILED_COUNT!
    echo.
    echo Please check the error messages above and fix any issues.
    echo.
)

echo Press any key to exit...
pause > nul