import pandas as pd
import glob
import os
import warnings
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)
pd.io.formats.excel.ExcelFormatter.header_style = None


# Change this to the directory where your CSV files are located
csv_file_directory = input('path_to_your_csv_files')

for csvfile in glob.glob(os.path.join(csv_file_directory, '*.csv')):
    df = pd.read_csv(csvfile)
    df.to_excel(csvfile[:-4] + '.xlsx', index=False)
