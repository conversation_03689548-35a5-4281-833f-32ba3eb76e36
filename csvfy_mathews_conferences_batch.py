#!/usr/bin/env python3
"""
Mathews Conferences Batch CSV Processing Script

This script processes multiple CSV directories for Mathews conference data in batch mode,
extracting conference segment names, filtering data using specific Mathews directory structure,
and preparing output files automatically from a list of paths in a text file.
"""

import os
import glob
import pandas as pd
import numpy as np
import re
import random
import warnings
import argparse
import sys
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# Suppress SettingWithCopyWarning
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

class MathewsConferenceBatchProcessor:
    def __init__(self):
        self.console = Console()
        
        # Base paths for Mathews structure
        self.master_dir = r"H:\Master Bounces and Unsubs"
        self.mathews_dir = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs"
        self.master_bounces_dir = os.path.join(self.master_dir, "Master Bounces and Unsubs")
        
        # Track original working directory
        self.original_cwd = os.getcwd()
        
    def create_progress_bar(self):
        """Create a progress bar with gradient colors"""
        return Progress(
            TextColumn("[bold blue]{task.description}"),
            BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
            "[progress.percentage]{task.percentage:>3.0f}%",
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console
        )
    
    def display_header(self, title):
        """Display a formatted header"""
        header_text = Text(title, style="bold magenta")
        self.console.print(Panel(header_text, expand=False))
    
    def extract_conference_name(self, path: str) -> str:
        """
        Extract conference segment name from a path using regex.
        
        Args:
            path: Path string to extract from
            
        Returns:
            Conference name or None if not found (batch mode - no manual input)
        """
        # Define a regex pattern to match the desired segment, including spaces
        pattern = r"\\([\w\s-]+\d{4})\\?"
        
        # Search for the pattern in the path
        match = re.search(pattern, path)
        
        if match:
            csn = match.group(1)
            self.console.print(f"[green]Conference Segment Name (CSN): {csn}[/green]")
            return csn
        else:
            self.console.print(f"[yellow]Desired segment not found in path: {path}[/yellow]")
            # In batch mode, we don't prompt. Return None to skip.
            return None
    
    def load_filter_files(self, csn: str) -> tuple:
        """
        Load all filter files for the specified conference.
        
        Args:
            csn: Conference segment name
            
        Returns:
            Tuple of DataFrames (hard_bounces, unsubscribers_combined, replied_bounces)
        """
        self.console.print(f"[yellow]Loading filter files for {csn}...[/yellow]")
        
        try:
            # Load Master Hard Bounces (unchanged)
            hb_path = os.path.join(self.master_bounces_dir, "Master_Hardbounces.csv")
            if os.path.exists(hb_path):
                df_hb = pd.read_csv(hb_path, on_bad_lines='skip')
                self.console.print(f"[green]✓ Loaded Master Hard Bounces: {len(df_hb)} records[/green]")
            else:
                df_hb = pd.DataFrame(columns=['Email'])
                self.console.print("[yellow]⚠ Master Hard Bounces file not found[/yellow]")
            
            # Load Global Unsubscribers from both CMS and Postpanel
            global_unsubs_files = [
                os.path.join(self.mathews_dir, "cms", "merged", "separated", "Global_Unsubscriber.csv"),
                os.path.join(self.mathews_dir, "postpanel", "merged", "separated", "Global_Unsubscriber.csv")
            ]
            
            global_unsubs_dfs = []
            for file_path in global_unsubs_files:
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path, on_bad_lines='skip')
                    global_unsubs_dfs.append(df)
                    self.console.print(f"[green]✓ Loaded Global Unsubscribers: {os.path.basename(file_path)} - {len(df)} records[/green]")
                else:
                    self.console.print(f"[yellow]⚠ Global Unsubscribers file not found: {os.path.basename(file_path)}[/yellow]")
            
            # Load Conference-specific Unsubscribers from both CMS and Postpanel
            conf_unsubs_files = [
                os.path.join(self.mathews_dir, "cms", "merged", "separated", f"{csn}_Unsubscribes.csv"),
                os.path.join(self.mathews_dir, "postpanel", "merged", "separated", f"{csn}_Unsubscribes.csv")
            ]
            
            conf_unsubs_dfs = []
            for file_path in conf_unsubs_files:
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path, on_bad_lines='skip')
                    conf_unsubs_dfs.append(df)
                    self.console.print(f"[green]✓ Loaded Conference Unsubscribers: {os.path.basename(file_path)} - {len(df)} records[/green]")
                else:
                    self.console.print(f"[yellow]⚠ Conference Unsubscribers file not found: {os.path.basename(file_path)}[/yellow]")
            
            # Combine all unsubscribers
            all_unsubs_dfs = global_unsubs_dfs + conf_unsubs_dfs
            if all_unsubs_dfs:
                df_unsubs_combined = pd.concat(all_unsubs_dfs, ignore_index=True)
                df_unsubs_combined['Email'] = df_unsubs_combined['Email'].str.lower()
                df_unsubs_combined.drop_duplicates(subset='Email', inplace=True)
                self.console.print(f"[green]✓ Combined Unsubscribers: {len(df_unsubs_combined)} unique records[/green]")
            else:
                df_unsubs_combined = pd.DataFrame(columns=['Email'])
                self.console.print("[yellow]⚠ No unsubscriber files found[/yellow]")
            
            # Load Replied Bounces
            replied_path = os.path.join(self.mathews_dir, "replied bounces", "separated", f"{csn}_replied_bouncers.csv")
            if os.path.exists(replied_path):
                df_replied = pd.read_csv(replied_path, on_bad_lines='skip')
                self.console.print(f"[green]✓ Loaded Replied Bounces: {len(df_replied)} records[/green]")
            else:
                df_replied = pd.DataFrame(columns=['Email'])
                self.console.print(f"[yellow]⚠ Replied Bounces file not found: {os.path.basename(replied_path)}[/yellow]")
            
            return df_hb, df_unsubs_combined, df_replied
            
        except Exception as e:
            self.console.print(f"[red]Error loading filter files: {str(e)}[/red]")
            return pd.DataFrame(columns=['Email']), pd.DataFrame(columns=['Email']), pd.DataFrame(columns=['Email'])
    
    def clean_name(self, name: str) -> str:
        """
        Clean name by removing text after the first comma.
        
        Args:
            name: Name string to clean
            
        Returns:
            Cleaned name string
        """
        return str(name).split(',')[0].strip()
    
    def process_conference_data(self, path: str, csn: str) -> pd.DataFrame:
        """
        Process conference data from CSV files.
        
        Args:
            path: Path to the directory containing CSV files
            csn: Conference segment name
            
        Returns:
            Processed DataFrame
        """
        self.console.print(f"[yellow]Processing conference data for {csn}...[/yellow]")
        
        try:
            os.chdir(path)
            
            # Create processing directory
            process_dir = os.path.join(path, "processing")
            os.makedirs(process_dir, exist_ok=True)
            
            # List all CSV files
            csv_files = glob.glob('*.csv')
            if not csv_files:
                self.console.print(f"[red]No CSV files found in {path}[/red]")
                return pd.DataFrame()
            
            # Create progress bar for reading files
            with self.create_progress_bar() as progress:
                read_task = progress.add_task("Reading CSV files...", total=len(csv_files))
                
                # Read and concatenate all CSV files
                dfs = []
                for f in csv_files:
                    df = pd.read_csv(f, on_bad_lines='skip', low_memory=False, encoding='utf-8-sig')
                    dfs.append(df)
                    progress.advance(read_task)
                
                # Concatenate all dataframes
                df_concat = pd.concat(dfs, ignore_index=True)
                self.console.print(f"[green]Successfully read {len(df_concat)} records from {len(csv_files)} files[/green]")
            
            # Standardize column names
            df_concat.rename(columns={
                'Author Name': 'Name', 
                'Name ': 'Name',
                'col1': 'Name',
                'col2': 'Article Title', 
                'email': 'Email',
                'name': 'Name'
            }, inplace=True)
            
            # Clean names by removing text after comma
            if 'Name' in df_concat.columns:
                df_concat['Name'] = df_concat['Name'].apply(self.clean_name)
            
            # Remove rows with empty Email
            df_concat.dropna(subset='Email', inplace=True)
            
            # Save unfiltered data
            unfiltered_path = os.path.join(process_dir, f"{csn}-merged_unfiltered.csv")
            df_concat.to_csv(unfiltered_path, mode='w+', encoding='utf-8-sig', index=False)
            
            # Load filter files
            df_hb, df_unsubs_combined, df_replied = self.load_filter_files(csn)
            
            # Read back the unfiltered data
            df_concat = pd.read_csv(unfiltered_path, low_memory=False)
            
            # Apply filters step by step
            self.console.print("[yellow]Applying filters...[/yellow]")

            original_count = len(df_concat)

            # Filter out hard bounces
            hard_bounce_emails = df_concat[df_concat['Email'].isin(df_hb['Email'])]
            df_hb_filtered = df_concat[~(df_concat['Email'].isin(df_hb['Email']))]
            hb_filtered_count = original_count - len(df_hb_filtered)

            # Filter out unsubscribers
            unsubscriber_emails = df_hb_filtered[df_hb_filtered['Email'].isin(df_unsubs_combined['Email'])]
            df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered['Email'].isin(df_unsubs_combined['Email']))]
            unsubs_filtered_count = len(df_hb_filtered) - len(df_unsubs_filtered)

            # Filter out replied bounces
            replied_bounce_emails = df_unsubs_filtered[df_unsubs_filtered['Email'].isin(df_replied['Email'])]
            df_final = df_unsubs_filtered[~(df_unsubs_filtered['Email'].isin(df_replied['Email']))]
            replied_filtered_count = len(df_unsubs_filtered) - len(df_final)

            # Display detailed filtering results
            self.console.print(f"[green]Filtering Results for {csn}:[/green]")
            self.console.print(f"  • Original records: {original_count:,}")

            # Show hard bounces details
            self.console.print(f"  • Hard bounces filtered: {hb_filtered_count:,}")
            if hb_filtered_count > 0:
                self.console.print(f"    [red]Hard bounce emails removed:[/red]")
                for email in hard_bounce_emails['Email'].tolist():
                    self.console.print(f"      - {email}")

            # Show unsubscribers details
            self.console.print(f"  • Unsubscribers filtered: {unsubs_filtered_count:,}")
            if unsubs_filtered_count > 0:
                self.console.print(f"    [yellow]Unsubscriber emails removed:[/yellow]")
                for email in unsubscriber_emails['Email'].tolist():
                    self.console.print(f"      - {email}")

            # Show replied bounces details
            self.console.print(f"  • Replied bounces filtered: {replied_filtered_count:,}")
            if replied_filtered_count > 0:
                self.console.print(f"    [blue]Replied bounce emails removed:[/blue]")
                for email in replied_bounce_emails['Email'].tolist():
                    self.console.print(f"      - {email}")

            self.console.print(f"  • Final records: {len(df_final):,}")
            
            # Remove Article Title column if it exists
            if 'Article Title' in df_final.columns:
                df_final.drop(['Article Title'], axis=1, inplace=True)
            elif 'Article Title' not in df_final.columns:
                df_final['Article Title'] = 0
                df_final.drop(['Article Title'], axis=1, inplace=True)
            
            # Extract only Name and Email columns
            if 'Name' in df_final.columns:
                df_final = df_final[["Name", "Email"]].copy()
            else:
                df_final = df_final[["Email"]].copy()
                df_final['Name'] = 'Colleague'
            
            # Clean data
            df_clean = df_final.replace(r'^\s*$', np.nan, regex=True)
            df_clean = df_clean.fillna('Colleague')
            
            # Remove duplicates
            result = df_clean.drop_duplicates(subset='Email')
            
            # Clean up temporary files
            if os.path.exists(unfiltered_path):
                os.remove(unfiltered_path)
            if os.path.exists(process_dir):
                os.rmdir(process_dir)
            
            self.console.print(f"[green]Processing completed for {csn}: {len(result)} final records[/green]")
            return result
            
        except Exception as e:
            self.console.print(f"[red]Error processing conference data for {csn}: {str(e)}[/red]")
            return pd.DataFrame()

    def generate_subject_lines(self, df: pd.DataFrame, csn: str) -> pd.DataFrame:
        """
        Generate random subject lines for each record.

        Args:
            df: DataFrame with records
            csn: Conference segment name

        Returns:
            DataFrame with added subject lines
        """
        self.console.print(f"[yellow]Generating subject lines for {csn}...[/yellow]")

        # List of subject line templates for Mathews conferences
        subj_list = [
            'Invitation to Submit Abstract for Oral Presentation',
            'Invitation to Submit Abstracts for [CCT_CSNAME]',
            'Call for Papers: 20-Minute Oral Presentation at [CCT_CSNAME]',
            'Submit Your Abstract for [CCT_CSNAME]',
            'Oral Presentation Slots Available at [CCT_CSNAME]',
            'Join Us as a Presenter at [CCT_CSNAME]',
            'Abstract Submission for Oral Presentations at [CCT_CSNAME] is OPEN!',
            'Your Expertise Wanted: Call for 20-Minutes Oral Presentation',
            '[CCT_CSNAME]: Now Accepting Abstract Submissions for Oral Presentations',
            'Share Your Research at [CCT_CSNAME]',
            'Invitation to Submit Abstract for 20-Minute Oral Presentation',
            'Present Your Findings at [CCT_CSNAME]',
            'Call for Oral Presentation Abstracts at [CCT_CSNAME]',
            '[CCT_CSNAME]: Call for 20-Minute Oral Presentation Abstracts',
            'Call for 20-Minute Oral Presentation Abstracts Now Open!',
            'Be Part of the Program: Submit Your Abstract Now!',
            'Call for Abstracts: [CCT_CSNAME]',
            'Submit your Research Abstract for the [CCT_CSNAME]',
            'Abstract Submission Open: [CCT_CSNAME]',
            'Submit Your Abstracts for the [CCT_CSNAME]',
            'Invitation to Speak at the [CCT_CSNAME]',
            'Be Our Guest Speaker at the [CCT_CSNAME]',
            'Call for Speakers: [CCT_CSNAME]',
            'Discovering the Future of Technology at [CCT_CSNAME]',
            'The Ultimate Networking Opportunity: [CCT_CSNAME]',
            "Don't Miss Out on [CCT_CSNAME]: Exploring the Latest Trends and Technologies",
            'Join the Conversations at [CCT_CSNAME]: A Dynamic Forum for Ideas and Inspiration'
        ]

        # Replace placeholder with actual conference name
        formatted_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

        # Assign random subject lines
        df["Subject"] = pd.Series(
            random.choices(formatted_subj_list, k=len(df)),
            index=df.index
        )

        self.console.print(f"[green]Generated subject lines for {len(df)} records[/green]")
        return df

    def save_output_files(self, df: pd.DataFrame, csn: str, n_temps: int = 1, create_splits: bool = False, include_subject: bool = True) -> int:
        """
        Save output files, splitting the data if needed.

        Args:
            df: DataFrame with records
            csn: Conference segment name
            n_temps: Number of templates/chunks to split into
            create_splits: Whether to create splits of 10000 rows each in the splits folder
            include_subject: Whether to include the Subject column in the output

        Returns:
            int: Number of files created
        """
        self.console.print(f"[yellow]Saving output files for {csn}...[/yellow]")

        try:
            # Create output directory
            output_dir = os.path.join(os.getcwd(), "output")
            os.makedirs(output_dir, exist_ok=True)

            # Remove Subject column if not needed
            if not include_subject and 'Subject' in df.columns:
                self.console.print("[blue]Removing Subject column from output...[/blue]")
                df = df.drop(columns=['Subject'])

            # Define splits directory path
            splits_dir = os.path.join(output_dir, "splits")

            # Create splits directory only if we're using splits
            if create_splits:
                os.makedirs(splits_dir, exist_ok=True)

            # Get the number of rows in the dataframe
            n_rows = len(df)

            # Maximum rows per file (10,000 as per user preference)
            max_rows_per_file = 10000

            # Track total number of files created
            total_files = 0

            # Handle based on whether we're creating splits or using templates
            if create_splits:
                # Calculate number of splits needed
                num_splits = (n_rows + max_rows_per_file - 1) // max_rows_per_file

                # Create progress bar for splits
                with self.create_progress_bar() as progress:
                    splits_task = progress.add_task(f"Creating split files for {csn}...", total=num_splits)

                    # Process each split
                    for i in range(num_splits):
                        start_idx = i * max_rows_per_file
                        end_idx = min((i + 1) * max_rows_per_file, n_rows)

                        # Extract chunk
                        chunk = df.iloc[start_idx:end_idx]

                        # Save to splits directory
                        output_path = os.path.join(splits_dir, f"{csn}_split_{i+1:03d}.csv")
                        chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                        total_files += 1
                        progress.advance(splits_task)

                self.console.print(f"[green]Created {num_splits} split files with {max_rows_per_file} rows each[/green]")

            else:
                # Calculate the size of each chunk based on templates
                chunk_size = n_rows // n_temps if n_temps > 0 else n_rows

                # Create progress bar for templates
                with self.create_progress_bar() as progress:
                    template_task = progress.add_task(f"Creating template files for {csn}...", total=n_temps)

                    # Split the dataframe into chunks and save them as separate csv files
                    for i in range(n_temps):
                        start = i * chunk_size
                        end = (i + 1) * chunk_size if i < n_temps - 1 else n_rows
                        chunk = df[start:end]

                        # Save directly to output directory
                        output_path = os.path.join(output_dir, f"{csn}-{i+1}_Mathews.csv")
                        chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

                        total_files += 1
                        progress.advance(template_task)

                self.console.print(f"[green]Created {n_temps} template files[/green]")

            # Show what columns were included
            columns_info = ", ".join(df.columns.tolist())
            self.console.print(f"[blue]Columns included: {columns_info}[/blue]")

            return total_files

        except Exception as e:
            self.console.print(f"[red]Error saving output files for {csn}: {str(e)}[/red]")
            return 0

    def read_paths_from_txt(self, txt_path: str) -> list:
        """
        Read directory paths from a plain text file (one path per line).

        Args:
            txt_path: Path to the text file containing directory paths

        Returns:
            List of cleaned directory paths
        """
        paths = []
        try:
            with open(txt_path, 'r', encoding='utf-8') as f:
                raw_lines = [line.strip() for line in f if line.strip()]  # Read lines, strip whitespace, ignore empty lines
            # Strip potential surrounding quotes (double or single) from each path
            paths = [line.strip('\'"') for line in raw_lines]
            if not paths:
                self.console.print(f"[yellow]Warning: No paths found in text file: {txt_path}[/yellow]")
            else:
                self.console.print(f"[green]Read {len(paths)} paths from {txt_path}[/green]")
        except FileNotFoundError:
            self.console.print(f"[red]Error: Input text file not found: {txt_path}[/red]")
            sys.exit(1)
        except Exception as e:
            self.console.print(f"[red]Error reading text file {txt_path}: {e}[/red]")
            sys.exit(1)
        return paths

    def run_batch_processing(self, input_txt: str, include_subject: bool = True, create_splits: bool = False, n_temps: int = 1):
        """
        Main method to run the batch processing workflow.

        Args:
            input_txt: Path to text file containing directory paths
            include_subject: Whether to include subject lines
            create_splits: Whether to create 10K-row splits
            n_temps: Number of template files to create
        """
        try:
            # Display header
            self.display_header("Mathews Conferences Batch CSV Processing Script")

            self.console.print(f"[blue]Starting batch processing using paths from: {input_txt}[/blue]")

            # Read paths from the input TXT file
            conference_paths_to_process = self.read_paths_from_txt(input_txt)
            if not conference_paths_to_process:
                self.console.print("[red]No valid paths found in the TXT file. Exiting.[/red]")
                sys.exit(1)

            processed_count = 0
            skipped_count = 0
            total_paths = len(conference_paths_to_process)

            # Iterate through the paths read from the text file
            for idx, conference_path in enumerate(conference_paths_to_process):
                item_name = os.path.basename(conference_path.rstrip('/\\'))  # Get dir name for logging
                self.console.print(f"\n[bold blue]--- Processing path {idx+1}/{total_paths}: {conference_path} ---[/bold blue]")

                if os.path.isdir(conference_path):
                    try:
                        # Extract conference segment name (using the full path)
                        # Ensure the path format is suitable for regex (might need adjustment if paths are Linux-style)
                        csn = self.extract_conference_name(conference_path + os.sep)  # Add separator for regex robustness
                        if csn is None:
                            self.console.print(f"[yellow]Could not extract CSN for path: {conference_path}. Skipping.[/yellow]")
                            skipped_count += 1
                            continue  # Skip to the next path

                        self.console.print(f"[blue]Processing conference: {csn}[/blue]")

                        # Process conference data - this changes CWD to conference_path
                        self.console.print(f"[yellow]Processing conference data for {csn}...[/yellow]")
                        df = self.process_conference_data(conference_path, csn)
                        # CWD is now conference_path

                        if not df.empty:
                            # Generate subject lines if requested
                            if include_subject:
                                self.console.print("[blue]Including subject lines in the output...[/blue]")
                                df = self.generate_subject_lines(df, csn)
                            else:
                                self.console.print("[blue]Subject lines will not be included.[/blue]")

                            # Handle splits directory removal if not creating splits
                            if not create_splits:
                                # Note: os.getcwd() is conference_path here
                                splits_dir = os.path.join(os.getcwd(), "output", "splits")
                                if os.path.exists(splits_dir):
                                    try:
                                        # Attempt to remove files and then the directory
                                        for file in os.listdir(splits_dir):
                                            file_path = os.path.join(splits_dir, file)
                                            if os.path.isfile(file_path):
                                                os.remove(file_path)
                                        os.rmdir(splits_dir)
                                        self.console.print(f"[blue]Removed existing splits directory: {splits_dir}[/blue]")
                                    except Exception as e:
                                        self.console.print(f"[yellow]Could not remove splits directory {splits_dir}: {str(e)}[/yellow]")

                            # Save output files (relative to current CWD: conference_path)
                            self.console.print("[yellow]Saving output files...[/yellow]")
                            total_files = self.save_output_files(df, csn, n_temps, create_splits, include_subject)

                            if total_files > 0:
                                file_type = "split" if create_splits else "template"
                                self.console.print(f"[green]Successfully saved {len(df)} records to {total_files} {file_type} files for {csn}[/green]")
                                self.console.print(f"[green]Total records processed for {csn}: {len(df)}[/green]")
                                processed_count += 1
                            else:
                                self.console.print(f"[yellow]No output files saved for {csn}.[/yellow]")
                        else:
                            self.console.print(f"[yellow]No data processed for {csn} after filtering.[/yellow]")
                            skipped_count += 1

                        # Change back to original CWD before next iteration
                        os.chdir(self.original_cwd)

                    except Exception as e:
                        self.console.print(f"[red]Error processing directory {item_name}: {str(e)}[/red]")
                        skipped_count += 1
                        # Ensure we change back CWD even if error occurs mid-processing
                        os.chdir(self.original_cwd)
                        continue  # Continue to the next path
                else:
                    self.console.print(f"[yellow]Path is not a valid directory: {conference_path}. Skipping.[/yellow]")
                    skipped_count += 1
                    # Ensure we are in original CWD before next iteration
                    os.chdir(self.original_cwd)

            # Display batch summary
            self.console.print(f"\n[bold blue]--- Batch Processing Summary ---[/bold blue]")
            self.console.print(f"[green]Successfully processed directories: {processed_count}[/green]")
            self.console.print(f"[yellow]Skipped/Errored directories: {skipped_count}[/yellow]" if skipped_count > 0 else f"[blue]Skipped/Errored directories: {skipped_count}[/blue]")
            self.console.print("[green]Batch processing finished.[/green]")

        except Exception as e:
            self.console.print(f"[red]Error in batch processing: {str(e)}[/red]")
            self.console.print("[red]Batch process terminated with errors.[/red]")
            # Ensure we return to original directory
            os.chdir(self.original_cwd)


def main():
    """Main execution function with command line argument parsing"""
    parser = argparse.ArgumentParser(description="Batch CSV Processing Script for Mathews Conferences from a list of paths in a TXT file.")
    parser.add_argument("input_txt", help="Path to the TXT file containing conference directory paths (one path per line).")

    # Argument to disable subject lines; default is to include them.
    parser.add_argument("--no-subject", dest='include_subject', action="store_false",
                        help="Do NOT include subject lines in the output files (default is to include them).")
    parser.set_defaults(include_subject=True)  # Explicitly set default for clarity

    parser.add_argument("--create_splits", action="store_true", default=False,
                        help="Split output into files of 10000 rows each in a 'splits' subdirectory.")
    parser.add_argument("--n_temps", type=int, default=1,
                        help="Number of templates/chunks to create if not using --create_splits (default: 1).")

    if len(sys.argv) == 1:
        parser.print_help(sys.stderr)
        sys.exit(1)

    args = parser.parse_args()

    # Validate n_temps (still relevant if not using splits)
    if not args.create_splits and args.n_temps <= 0:
        print("Error: --n_temps must be a positive integer.")
        sys.exit(1)

    # Create processor and run batch processing
    processor = MathewsConferenceBatchProcessor()
    processor.run_batch_processing(
        input_txt=args.input_txt,
        include_subject=args.include_subject,
        create_splits=args.create_splits,
        n_temps=args.n_temps
    )


if __name__ == "__main__":
    main()
