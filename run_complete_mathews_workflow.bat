@echo off
echo ========================================
echo Complete Mathews Workflow Process
echo ========================================
echo This will run the complete Mathews processing workflow:
echo 1. <PERSON> Unsubscribes Workflow
echo 2. <PERSON> Replied Bounces Processing
echo ========================================
echo.

:: Change to the working directory
cd /d "C:\Users\<USER>\OneDrive\My Files"

echo Phase 1: Running Mathews Unsubscribes Workflow
echo ===============================================
echo Executing run_mathews_unsubs_workflow.bat...
echo.

call "run_mathews_unsubs_workflow.bat"

:: Check if the first batch file executed successfully
if %errorlevel% neq 0 (
    echo.
    echo ERROR: run_mathews_unsubs_workflow.bat failed with error code %errorlevel%
    echo Complete workflow stopped. Please check the error and try again.
    pause
    exit /b %errorlevel%
)

echo.
echo Phase 1 completed successfully!
echo.
echo ========================================
echo.

echo Phase 2: Running Mathews Replied Bounces Process
echo ================================================
echo Executing run_mathews_process.bat...
echo.

call "run_mathews_process.bat"

:: Check if the second batch file executed successfully
if %errorlevel% neq 0 (
    echo.
    echo ERROR: run_mathews_process.bat failed with error code %errorlevel%
    echo Unsubscribes workflow completed but replied bounces process failed.
    echo Please check the error and try again.
    pause
    exit /b %errorlevel%
)

echo.
echo ========================================
echo Complete Mathews Workflow Finished!
echo ========================================
echo All processes have completed successfully:
echo.
echo Phase 1 - Mathews Unsubscribes Workflow:
echo   ✓ Unsubscribes processing completed
echo.
echo Phase 2 - Mathews Replied Bounces Process:
echo   ✓ Replied bounces separated
echo   ✓ Files uploaded to DBMS
echo.
echo The complete Mathews workflow is now finished!
echo.
echo Press any key to exit...
pause > nul
