Starting master-s_2.0.py at 25-08-2025 11:44:12.19 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.15s/it]
Processing: 100%|##########| 1/1 [00:18<00:00, 18.15s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:00<00:00, 32.97it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.99s/it]
Processing: 100%|##########| 1/1 [00:13<00:00, 13.99s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:25<00:00, 25.63s/it]
Processing: 100%|##########| 1/1 [00:25<00:00, 25.63s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.02it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.02it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,23,32,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:03<00:00,  3.68s/it]
Processing: 100%|##########| 1/1 [00:03<00:00,  3.68s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 11.01it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:48<00:00, 48.90s/it]
Processing: 100%|##########| 1/1 [00:48<00:00, 48.90s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:43<00:00, 43.21s/it]
Finishing: 100%|##########| 1/1 [00:43<00:00, 43.21s/it]
SUCCESS: master-s_2.0.py completed successfully at 25-08-2025 11:46:47.47 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 25-08-2025 11:47:07.78 
