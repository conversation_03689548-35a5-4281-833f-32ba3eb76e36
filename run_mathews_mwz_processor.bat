@echo off
echo ========================================
echo Mathews Mailwizz Unsubscribers Processor
echo ========================================
echo.

:: Change to the working directory
cd /d "C:\Users\<USER>\OneDrive\My Files"

echo Step 1: Clearing compiled unsubs directory
echo ----------------------------------------
echo Removing existing files from compiled unsubs folder...

:: Clear the compiled unsubs directory
rmdir /s /q "H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs\compiled unsubs" 2>nul
if exist "H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs\compiled unsubs" (
    echo Warning: Could not completely clear the directory
) else (
    echo Successfully cleared compiled unsubs directory
)

echo.
echo Step 2: Running Mathews Mailwizz Processor
echo ----------------------------------------
echo Processing mailwizz unsubs directories...
echo.

python "mathews_mwz_unsubs_processor.py"

:: Check if the script executed successfully
if %errorlevel% neq 0 (
    echo.
    echo ERROR: mathews_mwz_unsubs_processor.py failed with error code %errorlevel%
    echo Please check the error and try again.
    pause
    exit /b %errorlevel%
)

echo.
echo ========================================
echo Processing completed successfully!
echo ========================================
echo.
pause
