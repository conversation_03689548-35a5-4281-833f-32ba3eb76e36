import os
import glob
import pandas as pd

# Set the input directory
input_dir = r"H:\<PERSON> Bounces and Unsubs\Journal Unsubs"

# Set the output file path
output_file = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\Master_Journal_Unsubscribes.csv"

# Get a list of all CSV files in the input directory
csv_files = glob.glob(os.path.join(input_dir, "*.csv"))

# Create an empty list to store the dataframes
df_list = []

# Loop through each CSV file and read the "Email" column
for file in csv_files:
    try:
        df = pd.read_csv(file, usecols=["Email"])
        df_list.append(df)
    except Exception as e:
        print(f"Error reading file {file}: {e}")

# Concatenate all the dataframes into a single dataframe
if df_list:
    merged_df = pd.concat(df_list, ignore_index=True)

    # Remove duplicate emails
    merged_df.drop_duplicates(subset="Email", inplace=True)

    # Save the merged dataframe to the output file
    merged_df.to_csv(output_file, index=False)

    print(f"Successfully merged {len(csv_files)} files into {output_file}")
else:
    print("No CSV files found in the input directory.")
