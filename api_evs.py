import requests
import pandas as pd
import datetime
import os

# Your list of Elastic Email API keys
API_KEYS = [
    '7647B4A56B3439F5A6ED6D5646FB00A1F5733846569D122F95E01ABED9E3653FC12FDBABABA808B165AB349151CEAD80',
    '9533BE840D9D6BF215187FAA98D77B23080244DF81C2CC5A0A50C8135D8D918F11968AED02D9981F967019E17068D5E3',
    '1911812CC8542766228A8655EA1A426C86570FF562AE87D3382F9A7B97589F6766026595A5AC8333F6B7FB36C7BDD763',
    '76F2A4D21353BD06AF8C1079A030DACE302044961511C1D946AF0214081A87E29D83C58AC7B7B2BEE4C8325CD29B04FB',
    'EA09FF35D3256A937FE431832B37F14EE9E192923F181659E31B593CC51B948DA0A50B2318D8F51690AE1D1B527FD188',
    '6D6DDC309722909571BB2894A28B09583FD0281909F0F769416F7A55FB7FF154CFC2C56C94DEAD3C106A712C912EB36F',
    '198DAA3DF93243D84DCB4CC636F0FAE96D462E3F96A85AC64A2E0BB2CE0A86F25E9A594E3D975EA388E4D0C5AADE12FC',
    '25A5ADBB06E0FE930A13911EDCD94A35DBC957DE39E0CB4B069AD0DC93D84B75941DF85440D40E66253DC9AEBDF0CE08'
    # Add more keys as needed
]

# Load emails from CSV file
df = pd.read_csv(input('path_to_your_csvfile: '))  # Replace with your actual file path
emails = df[['Name', 'Email']].to_dict('records')  # Create a list of dictionaries with Name and Email

# Function to verify a single email
def verify_email(email, api_key):
    url = f'https://api.elasticemail.com/v2/email/verify?apikey={api_key}&email={email}'
    response = requests.get(url)
    return response.json()

# Rotate through API keys and collect results
verification_results = []
for i, record in enumerate(emails):
    api_key = API_KEYS[i % len(API_KEYS)]  # Rotate through the list of API keys
    result = verify_email(record['Email'], api_key)
    result_status = result.get('data', {}).get('result')  # Extract 'result' from the response
    reason_status = result.get('data', {}).get('reason')  # Extract 'reason' from the response
    verification_results.append({
        'Name': record['Name'],  # Include the Name field
        'Email': record['Email'],
        'Result': result_status,
        'Reason': reason_status
    })
    
# Create a DataFrame to display results
result_df = pd.DataFrame(verification_results)

now = datetime.datetime.now()
timestamp = now.strftime('%Y%m%d%H%M%S')

path = 'H:/Master Bounces and Unsubs/New Folder/evs_results'
os.chdir(path)

subject_name = input("Subject name: ")
os.makedirs((subject_name), exist_ok=True)

path1 = 'H:/Master Bounces and Unsubs/New Folder/evs_results/'+subject_name
os.chdir(path1)

template_type = input('Template type: ')
os.makedirs((template_type), exist_ok=True)

path2 = 'H:/Master Bounces and Unsubs/New Folder/evs_results/'+subject_name+'/'+template_type+'/'
os.chdir(path2)

# Create a DataFrame to display results
result_df.to_csv(subject_name+'_'+template_type+'_'+timestamp+'.csv', index=False)
print(result_df)