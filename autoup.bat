@ECHO OFF
setlocal enabledelayedexpansion

echo Starting auto_rep_upload.py at %date% %time%

set "LOG_FILE=C:\Users\<USER>\OneDrive\My Files\autoup_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Starting auto_rep_upload.py at %date% %time% > "%LOG_FILE%"

cd C:\Users\<USER>\OneDrive\My Files || (
    echo ERROR: Could not change to directory >> "%LOG_FILE%"
    echo ERROR: Could not change to directory
    exit /b 1
)

echo Running auto_rep_upload.py...
echo Running auto_rep_upload.py... >> "%LOG_FILE%"

python auto_rep_upload.py 2>> "%LOG_FILE%"

if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    echo ERROR: <PERSON><PERSON><PERSON> failed with error code !errorlevel!
) else (
    echo SUCCESS: <PERSON><PERSON><PERSON> completed successfully at %date% %time% >> "%LOG_FILE%"
    echo SUCCESS: <PERSON><PERSON><PERSON> completed successfully
)

echo Log file saved to: %LOG_FILE%

endlocal