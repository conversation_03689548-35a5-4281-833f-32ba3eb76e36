import os
import pandas as pd
from pathlib import Path
from rich.console import <PERSON>sol<PERSON>
from rich.progress import Progress, SpinnerColumn, TextColumn
import shutil

class FilePreprocessor:
    def __init__(self):
        self.console = Console()
        
    def standardize_column_names(self, df):
        """Standardize column names for Name and Email variations."""
        name_variations = ['name', 'names', 'Names', 'NAME', 'NAMES']
        email_variations = ['email', 'EMAIL', 'emails', 'EMAILS', 'Emails']
        
        rename_dict = {}
        
        for col in df.columns:
            if col.lower() in [var.lower() for var in name_variations]:
                rename_dict[col] = 'Name'
            elif col.lower() in [var.lower() for var in email_variations]:
                rename_dict[col] = 'Email'
        
        if rename_dict:
            df = df.rename(columns=rename_dict)
        
        return df, rename_dict

    def create_backup(self, file_path):
        """Create a backup of the original file."""
        backup_dir = file_path.parent / 'backups'
        backup_dir.mkdir(exist_ok=True)
        
        backup_path = backup_dir / f"{file_path.stem}_original{file_path.suffix}"
        if not backup_path.exists():
            shutil.copy2(file_path, backup_path)
            return True
        return False

    def process_excel_files(self, directory):
        """Process all Excel files in the specified directory."""
        excel_files = list(Path(directory).glob("*.xlsx"))
        if not excel_files:
            self.console.print("[yellow]No Excel files found in the directory.[/yellow]")
            return
        
        stats = {
            'processed': 0,
            'modified': 0,
            'skipped': 0,
            'errors': 0
        }
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task(f"Processing {len(excel_files)} Excel files...", total=len(excel_files))
            
            for file_path in excel_files:
                progress.update(task, description=f"Processing {file_path.name}")
                
                try:
                    # Read Excel file
                    df = pd.read_excel(file_path)
                    original_columns = list(df.columns)
                    
                    # Standardize column names
                    df, changes = self.standardize_column_names(df)
                    
                    # If changes were made, save the file
                    if changes:
                        # Create backup
                        if self.create_backup(file_path):
                            self.console.print(f"[blue]Created backup for: {file_path.name}[/blue]")
                        
                        # Save with standardized column names
                        df.to_excel(file_path, index=False)
                        
                        # Print changes made
                        self.console.print(f"\n[green]Processed: {file_path.name}[/green]")
                        self.console.print("Changes made:")
                        for old_col, new_col in changes.items():
                            self.console.print(f"  - '{old_col}' → '{new_col}'")
                        
                        stats['modified'] += 1
                    else:
                        self.console.print(f"\n[blue]No changes needed for: {file_path.name}[/blue]")
                        stats['skipped'] += 1
                    
                    stats['processed'] += 1
                
                except Exception as e:
                    self.console.print(f"\n[red]Error processing {file_path.name}: {str(e)}[/red]")
                    stats['errors'] += 1
                
                progress.advance(task)
        
        return stats

    def print_summary(self, stats):
        """Print summary of processing results."""
        self.console.print("\n" + "=" * 50)
        self.console.print("[bold]Processing Summary[/bold]")
        self.console.print("=" * 50)
        self.console.print(f"Total files processed: {stats['processed']}")
        self.console.print(f"Files modified: {stats['modified']}")
        self.console.print(f"Files skipped (no changes needed): {stats['skipped']}")
        self.console.print(f"Errors encountered: {stats['errors']}")
        self.console.print("=" * 50)

def main():
    preprocessor = FilePreprocessor()
    console = Console()
    
    console.print("\n[bold]Excel File Preprocessor[/bold]")
    console.print("This script will prepare Excel files by standardizing column names and creating backups.\n")
    
    # Get directory path
    while True:
        directory = input("Enter the directory path containing Excel files: ").strip()
        if os.path.isdir(directory):
            break
        console.print("[red]Invalid directory path. Please try again.[/red]")
    
    # Process files
    stats = preprocessor.process_excel_files(directory)
    
    # Print summary
    preprocessor.print_summary(stats)

if __name__ == "__main__":
    main()