@ECHO OFF
echo Cleaning CSV files from bounce and unsubscribe directories...

REM Delete CSVs from Bounces directory
echo Cleaning Bounces directory...
del /s /q "H:\Master Bounces and Unsubs\Bounces\*.csv" 2>nul
if %errorlevel% equ 0 (
    echo Successfully cleaned Bounces directory.
) else (
    echo Warning: Could not clean Bounces directory. Error code: %errorlevel%
)

REM Delete CSVs from Postpanel Unsubs directory
echo Cleaning Postpanel Unsubs directory...
del /s /q "H:\Master Bounces and Unsubs\Postpanel Unsubs\*.csv" 2>nul
if %errorlevel% equ 0 (
    echo Successfully cleaned Postpanel Unsubs directory.
) else (
    echo Warning: Could not clean Postpanel Unsubs directory. Error code: %errorlevel%
)

echo Cleanup complete.
pause