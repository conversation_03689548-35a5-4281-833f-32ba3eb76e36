import timeit
import time
import os
import glob
import pandas as pd
import re
import numpy as np
import win32com.client
import pandas.io.formats.excel
import os
import shutil
import pandas.io.formats
pd.io.formats.excel.ExcelFormatter.header_style = None
from tqdm import tqdm
for i in tqdm(range(1), desc="Processing: ", colour='green'):
    from datetime import datetime
    # current dateTime
    now = datetime.now()
    # convert to string
    date_str = now.strftime("%d-%m-%Y")

    os.chdir(r"H:\Master Bounces and Unsubs\Replied bounces")
    xls_files = glob.glob('*.{}'.format('xlsx'))

    filename = "Replied Bounces "+date_str+'.xlsx'
    df1 = pd.read_excel(filename, usecols=["Conference Short Name", "Email"] )
    df2 = pd.read_excel(filename, usecols=["Conference Short Name", "Alternate Email"])
    df2.rename(columns = {'Alternate Email':'Email'}, inplace = True)
    df3 = pd.concat([df1, df2])
    df4 = df3.apply(lambda x: x.str.replace('mailto:', ''))
    df5 = df4.apply(lambda x: x.str.replace('mailto', ''))
    df6 = df5.apply(lambda x: x.str.replace(':', ','))
    df7 = df6.apply(lambda x: x.str.replace(';', ','))
    df7[['Email']] = df7[['Email']].apply(lambda x: x.str.replace(' ', ''))
    df7[['Email1', 'Email2', 'Email3']] = df7['Email'].str.split(',', expand=True) # 'Email4'
    df7.drop('Email', axis=1, inplace=True)
    #df7.rename(columns={'Email1': 'Email', 'Email2': 'Email', 'Email3': 'Email'}, inplace=True)
    df8 = df7.loc[:, ['Conference Short Name', 'Email1']]
    df9 = df7.loc[:, ['Conference Short Name', 'Email2']]
    df10 = df7.loc[:, ['Conference Short Name', 'Email3']]
    #df11 = df7.loc[:, ['Conference Short Name', 'Email4']]
    df8.rename(columns= {'Email1': 'Email'}, inplace=True)
    df9.rename(columns= {'Email2': 'Email'}, inplace=True)
    df10.rename(columns= {'Email3': 'Email'}, inplace=True)
    #df11.rename(columns= {'Email4': 'Email'}, inplace=True)
    df12 = pd.concat([df8, df9, df10]) # df11

    def trim_all_columns(df):
        """
        Trim whitespace from ends of each value across all series in dataframe
        """
        trim_strings = lambda x: x.strip('.,;() ') if isinstance(x, str) else x
        return df.applymap(trim_strings)
    # simple example of trimming whitespace from data elements
    #df4 = pd.DataFrame(['Conference Short Name', 'Email'])
    df13 = trim_all_columns(df12)

    def find_email(text):
        email = re.findall(r'^[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*@[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*$',str(text)) #[A-Za-z0-9\.\-+_]+@[A-Za-z0-9\.\-+_]+\.[a-zA-Z]+
        return ",".join(email)

    df13['Email'] = df13["Email"].apply(lambda x: find_email(x))
    df13[['Email']] = df13[['Email']].applymap(lambda x:x.lower())
    #df9 = df8.drop('Conference Full Name', axis=1)

    df14 = df13.drop_duplicates(subset="Email")
    #filename = input("Enter File Name: ")+'.xlsx'
    df = df14

    os.chdir(r"H:/Master Bounces and Unsubs/Replied Ext/")

    list = set(df['Conference Short Name'].tolist())

    for i in iter(list):
        d = {'Email': []}
        dfe = pd.DataFrame(data=d)
        dfe.to_csv('Prev_Rep_bounces_csv/'+i+'_replied_bouncers.csv', index=False) #In case of new event addition

print("Cleared")



import timeit
import time
import os
import glob
import pandas as pd
import re
import numpy as np
import win32com.client
import os
import shutil
import pandas.io.formats
pd.io.formats.excel.ExcelFormatter.header_style = None
from tqdm import tqdm
for i in tqdm(range(1), desc="Processing: ", colour='green'):
    from datetime import datetime
    # current dateTime
    now = datetime.now()
    # convert to string
    date_str = now.strftime("%d-%m-%Y")

    os.chdir(r"H:\Master Bounces and Unsubs\Replied bounces")
    xls_files = glob.glob('*.{}'.format('xlsx'))

    filename = "Replied Bounces "+date_str+'.xlsx'
    df1 = pd.read_excel(filename, usecols=["Conference Short Name", "Email"] )
    df2 = pd.read_excel(filename, usecols=["Conference Short Name", "Alternate Email"])
    df2.rename(columns = {'Alternate Email':'Email'}, inplace = True)
    df3 = pd.concat([df1, df2])
    df4 = df3.apply(lambda x: x.str.replace('mailto:', ''))
    df5 = df4.apply(lambda x: x.str.replace('mailto', ''))
    df6 = df5.apply(lambda x: x.str.replace(':', ','))
    df7 = df6.apply(lambda x: x.str.replace(';', ','))
    df7[['Email']] = df7[['Email']].apply(lambda x: x.str.replace(' ', ''))
    #df7[['Email']] = df7[['Email']].apply(lambda x: x.str.replace('U.S. Food & Drug Administration, Center for Drug Evaluation and Research  Office of New Drugs, Office of Oncologic Diseases, Division of Oncology', ''))
    #regex = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$" #newline
    #df7 = df7[df7['Email'].str.match(regex)] #newline
    df7[['Email1', 'Email2', 'Email3']] = df7['Email'].str.split(',', expand=True) #, 'Email3','Email4'
    df7.drop('Email', axis=1, inplace=True)
    df8 = df7.loc[:, ['Conference Short Name', 'Email1']]
    df9 = df7.loc[:, ['Conference Short Name', 'Email2']]
    df10 = df7.loc[:, ['Conference Short Name', 'Email3']]
    #df11 = df7.loc[:, ['Conference Short Name', 'Email4']]
    df8.rename(columns= {'Email1': 'Email'}, inplace=True)
    df9.rename(columns= {'Email2': 'Email'}, inplace=True)
    df10.rename(columns= {'Email3': 'Email'}, inplace=True)
    #df11.rename(columns= {'Email4': 'Email'}, inplace=True)
    df12 = pd.concat([df8, df9, df10]) # df11

    def trim_all_columns(df):
        """
        Trim whitespace from ends of each value across all series in dataframe
        """
        trim_strings = lambda x: x.strip('.,;() ') if isinstance(x, str) else x
        return df.applymap(trim_strings)
    # simple example of trimming whitespace from data elements
    #df4 = pd.DataFrame(['Conference Short Name', 'Email'])
    df13 = trim_all_columns(df12)

    def find_email(text):
        email = re.findall(r'^[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*@[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*$',str(text)) #[A-Za-z0-9\.\-+_]+@[A-Za-z0-9\.\-+_]+\.[a-zA-Z]+
        return ",".join(email)

    df13['Email'] = df13["Email"].apply(lambda x: find_email(x))
    df13[['Email']] = df13[['Email']].applymap(lambda x:x.lower())
    #df9 = df8.drop('Conference Full Name', axis=1)

    #df14 = df13.drop_duplicates(subset="Email") #changed
    #filename = input("Enter File Name: ")+'.xlsx'"""
    df = df13 #changed

    os.chdir(r"H:/Master Bounces and Unsubs/Replied Ext/")

    list = set(df['Conference Short Name'].tolist())
    #list = "GPB-2024","IOGP-2023","Infection-2024","Materials-2024","AGRI-2023","GAB-2023","WCID-2023","IDC-2024","Heart Congress-2024","NWC-2023","WOC-2023","Catalysis-2023","INBC-2023","Pharma-2024","IVC-2023","ECBB-2024","Diabetes-2023","MAT-2023","ICDO-2024","IPHC-2024","NURSING-2024","Chemistry-2024","CCT-2024","GCSA-2023","World Nano-2024","Magnus Group-2016","Traditional Med-2024","Geology-2023","Cardio-2023","Neurology-2024","Dental-2024","IOC-2023","Gynec-2024","Nursing Science-2023","Gastro-2024","EGCCC-2023","Precision Med-2023","COPD-2024","Green Chemistry-2024","ELOS-2023","WAC-2024","Magnus Conferences-2016","ICTM-2024","FAT-2023","PDDS-2023","GPMB-2023","CAT-2023","Orphan Drugs-2023","Ortho-2023","Nutri-2023","NUTRITION-2023","EPN-2023","ICC-2024","Probiotics-2023","ICIA-2023","VET-2023","TERMC-2024","Nanotechnology-2023","GCPR-2023","Euro Proteomics-2023","Toxicology-2023","Dementia-2023","London Nursing-2024","Virology-2023","CCET-2024","Hematology-2023","Stem Cells-2023","Bioanalytica-2023","American Dental-2023","Pharmaceutics-2023","IPMC-2023","CCET-2023","Biofuels-2023","ICCM-2023","ICAM-2023","MCADD-2023","Recycling-2023"

    # Using for loop
    for i in iter(list):
        event = i
        #Event
        event1 = [event]
        event = str(event)
        df_event = df[df['Conference Short Name'].isin(event1)]
        dfd_event = df_event.drop(["Conference Short Name"], axis=1)
        dfd_event.drop_duplicates(subset="Email", keep='first', inplace=True)
        dfd_event.replace(r'^\s*$', np.nan, regex=True, inplace=True)
        dfd_event = dfd_event.dropna()
        dfd_event[['Email']] = dfd_event[['Email']].applymap(lambda x:x.lower())
        os.chdir(r'H:\Master Bounces and Unsubs\Replied Ext')
        dfd_event.to_csv("Rep_bounces_csv/"+event+"_replied_bouncers.csv", index=False)
        dfd_event.to_excel("Rep_bounces_xlsx/"+event+"_replied_bouncers.xlsx", index=False)
        df_prev_event = pd.read_csv("Prev_Rep_bounces_csv/"+event+"_replied_bouncers.csv")
        df_latest_event = pd.read_csv("Rep_bounces_csv/"+event+"_replied_bouncers.csv")
        df_new_event = df_latest_event[~df_latest_event.Email.isin(df_prev_event.Email)]
        df_new_event.to_csv("Todays_Rep_bounces/csv/"+event+"_replied_bouncers.csv", index=False)
        df_new_event.to_excel("Todays_Rep_bounces/xlsx/"+event+"_replied_bouncers.xlsx", index=False)
        pass

    #mailwizz_blocklist
    df_mwbl = df13
    df_mwbl1 = df_mwbl.dropna()
    df_mwbl1.rename(columns = {'Conference Short Name': 'Reason'}, inplace=True)
    df_mwbl1.dropna(subset='Email', inplace=True)
    df_mwbl1.to_csv('Todays_Rep_bounces/Blocklist_mailwizz.csv', mode='w+', index=False)


    # import the required module
    import os
    folder_path='Todays_Rep_bounces/csv/'
    for root, _, files in os.walk(folder_path):
        for f in files:
            complete_path=os.path.join(root,f)
            try:
                # set the size of the files you want to delete
                if os.path.getsize(complete_path) == 7:
                    complete_path #print(complete_path)
                    # function to delete the files
                    os.remove(complete_path)
            except FileNotFoundError:
                print("This file does not exist"+ complete_path)
    # import the required module
    import os
    folder_path='Todays_Rep_bounces/xlsx/'
    for root, _, files in os.walk(folder_path):
        for f in files:
            complete_path=os.path.join(root,f)
            try:
                # set the size of the files you want to delete
                if os.path.getsize(complete_path) <= 5272:
                    complete_path #print(complete_path)
                    # function to delete the files
                    os.remove(complete_path)
            except FileNotFoundError:
                print("This file does not exist"+ complete_path)

    import os, glob
    for filename in glob.glob("H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces/csv/*2016*"):
        os.remove(filename) 

    import os, glob
    for filename in glob.glob("H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces/xlsx/*2016*"):
        os.remove(filename) 

    import os, glob
    for filename in glob.glob("H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces/csv/London*"):
        os.remove(filename) 

    import os, glob
    for filename in glob.glob("H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces/xlsx/London*"):
        os.remove(filename) 

    import os
    import zipfile

    #zipping files with .csv and .xlsx
    fantasy_zip = zipfile.ZipFile('H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces.zip', 'w')

    for folder, subfolders, files in os.walk('H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces'):

        for file in files:
            if file.endswith(('.csv','.xlsx')):
                fantasy_zip.write(os.path.join(folder, file), os.path.relpath(os.path.join(folder,file), 'H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces'), compress_type = zipfile.ZIP_DEFLATED)
    fantasy_zip.close()


    from datetime import date
    current_date = date.today()
    str_current_date = str(current_date)
    path = "H:/Master Bounces and Unsubs/Replied Ext/Todays_Rep_bounces"
    file_name = "Todays_Rep_bounces "+str_current_date+".zip"
    os.rename('Todays_Rep_bounces.zip', file_name)
    
    '''import email, smtplib, ssl

    from email import encoders
    from email.mime.base import MIMEBase
    from email.mime.multipart import MIMEMultipart
    from email.mime.text import MIMEText

    subject = file_name+" & Mailwizz Blocklist"
    body = "Please find attached"
    sender_email = "<EMAIL>"
    receiver_email = ""
    password = "Micah@2022"

    # Create a multipart message and set headers
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = receiver_email
    message["Subject"] = subject
    message["Bcc"] = sender_email  # Recommended for mass emails

    # Add body to email
    message.attach(MIMEText(body, "plain"))

    filename = file_name  # In same directory as script

    # Open PDF file in binary mode
    with open(filename, "rb") as attachment:
        # Add file as application/octet-stream
        # Email client can usually download this automatically as attachment
        part = MIMEBase("application", "octet-stream")
        part.set_payload(attachment.read())

    # Encode file in ASCII characters to send by email    
    encoders.encode_base64(part)

    # Add header as key/value pair to attachment part
    part.add_header(
        "Content-Disposition",
        f"attachment; filename= {filename}",
    )

    # Add attachment to message and convert message to string
    message.attach(part)
    text = message.as_string()

    # Log in to server using secure context and send email
    context = ssl.create_default_context()
    with smtplib.SMTP_SSL("smtppro.zoho.in", 465, context=context) as server:
        server.login(sender_email, password)
        server.sendmail(sender_email, receiver_email, text)

        subject = file_name+" & Mailwizz Blocklist"
    body = "Please find attached"
    sender_email = "<EMAIL>"
    receiver_email = ""
    password = "Micah@2022"

    # Create a multipart message and set headers
    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = receiver_email
    message["Subject"] = subject
    message["Bcc"] = sender_email  # Recommended for mass emails

    # Add body to email
    message.attach(MIMEText(body, "plain"))

    filename = file_name  # In same directory as script

    # Open PDF file in binary mode
    with open(filename, "rb") as attachment:
        # Add file as application/octet-stream
        # Email client can usually download this automatically as attachment
        part = MIMEBase("application", "octet-stream")
        part.set_payload(attachment.read())

    # Encode file in ASCII characters to send by email    
    encoders.encode_base64(part)

    # Add header as key/value pair to attachment part
    part.add_header(
        "Content-Disposition",
        f"attachment; filename= {filename}",
    )

    # Add attachment to message and convert message to string
    message.attach(part)
    text = message.as_string()

    # Log in to server using secure context and send email
    context = ssl.create_default_context()
    with smtplib.SMTP_SSL("smtppro.zoho.in", 465, context=context) as server:
        server.login(sender_email, password)
        server.sendmail(sender_email, receiver_email, text)
'''
import os
import shutil
shutil.rmtree("Prev_Rep_bounces_csv")
shutil.rmtree("Prev_Rep_bounces_xlsx")
os.chdir(r'H:/Master Bounces and Unsubs/Replied Ext')
os.rename('Rep_bounces_csv', 'Prev_Rep_bounces_csv')
os.rename('Rep_bounces_xlsx', 'Prev_Rep_bounces_xlsx')
os.mkdir('Rep_bounces_csv')
os.mkdir('Rep_bounces_xlsx')
pass
print("Completed")

