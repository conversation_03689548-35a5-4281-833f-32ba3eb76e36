@echo off
setlocal enabledelayedexpansion

:: Set console colors and title
title Daily Routine Workflow - Unsubscribe Data Processing
color 0A

echo ===============================================
echo    DAILY ROUTINE WORKFLOW
echo    UNSUBSCRIBE DATA PROCESSING
echo ===============================================
echo Started at: %date% %time%
echo.
echo This batch file will execute the following scripts in order:
echo 1. Backup Master CSV Files
echo 2. CMS Unsubs CSV Download
echo 3. MG Port1 PP Unsubs CSV Download
echo 4. Magnus Group Port1 PP Unsubs CSV Download
echo 5. Magnus SMTPS PP Unsubs CSV Download
echo 6. Email Invite Net Unsubs Download
echo 7. Email Invite Z Unsubs Download
echo 8. Find New Records Processing
echo 9. Daily Unsubs Upload to DBMS
echo.
echo ===============================================
echo.

REM Clean up existing files before starting
echo Deleting all files in H:\Master Bounces and Unsubs\Postpanel Unsubs...
del /s /q "H:\Master Bounces and Unsubs\Postpanel Unsubs\*.*"
echo Cleanup completed.
echo.

REM Set the scripts directory - using %~dp0 to get the directory where this batch file is located
set "SCRIPTS_DIR=%~dp0"

REM Create a log file
set "LOG_FILE=%SCRIPTS_DIR%script_log_%date:~-4,4%%date:~-7,2%%date:~-10,2%.txt"
echo Script execution started at %date% %time% > "%LOG_FILE%"

REM Count total scripts and track success/failure
set "TOTAL_SCRIPTS=9"
set "CURRENT_SCRIPT=1"
set "SUCCESS_COUNT=0"
set "FAILED_COUNT=0"
set "error_occurred=false"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: backup_master_csv.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: backup_master_csv.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%backup_master_csv.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_1_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_1_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: cms_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: cms_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%cms_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_2_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_2_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: mg.port1.in_pp_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: mg.port1.in_pp_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%mg.port1.in_pp_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_3_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_3_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnusgroup.port1.in_pp_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnusgroup.port1.in_pp_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%magnusgroup.port1.in_pp_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_4_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_4_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnus.smtps.in_pp_unsubs_csv_download.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: magnus.smtps.in_pp_unsubs_csv_download.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%magnus.smtps.in_pp_unsubs_csv_download.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_5_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_5_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 1unsubs_from_emailinvite.net_2.0.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 1unsubs_from_emailinvite.net_2.0.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%1unsubs_from_emailinvite.net_2.0.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_6_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_6_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 2unsubs_from_emailinvitez.com.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: 2unsubs_from_emailinvitez.com.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%2unsubs_from_emailinvitez.com.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_7_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_7_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: find_new_records.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: find_new_records.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%find_new_records.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_8_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_8_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"
set /a "CURRENT_SCRIPT+=1"

echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: daily_unsubs_upload2dbms.py
echo [%CURRENT_SCRIPT%/%TOTAL_SCRIPTS%] Running: daily_unsubs_upload2dbms.py >> "%LOG_FILE%"
python "%SCRIPTS_DIR%daily_unsubs_upload2dbms.py"
if !errorlevel! neq 0 (
    echo ERROR: Script failed with error code !errorlevel!
    echo ERROR: Script failed with error code !errorlevel! >> "%LOG_FILE%"
    set "SCRIPT_9_STATUS=FAILED"
    set /a "FAILED_COUNT+=1"
    set "error_occurred=true"
) else (
    echo SUCCESS: Script completed successfully
    echo SUCCESS: Script completed successfully >> "%LOG_FILE%"
    set "SCRIPT_9_STATUS=SUCCESS"
    set /a "SUCCESS_COUNT+=1"
)
echo.
echo. >> "%LOG_FILE%"

:: Display completion summary
if "!error_occurred!"=="false" (
    echo ===============================================
    echo    DAILY ROUTINE WORKFLOW COMPLETED SUCCESSFULLY!
    echo ===============================================
    echo.
    echo All 9 scripts completed successfully at %date% %time%
    echo.
    echo Summary:
    echo [OK] Backup Master CSV Files
    echo [OK] CMS Unsubs CSV Download
    echo [OK] MG Port1 PP Unsubs CSV Download
    echo [OK] Magnus Group Port1 PP Unsubs CSV Download
    echo [OK] Magnus SMTPS PP Unsubs CSV Download
    echo [OK] Email Invite Net Unsubs Download
    echo [OK] Email Invite Z Unsubs Download
    echo [OK] Find New Records Processing
    echo [OK] Daily Unsubs Upload to DBMS
    echo.
    echo Statistics:
    echo - Total Scripts: !TOTAL_SCRIPTS!
    echo - Successful: !SUCCESS_COUNT!
    echo - Failed: !FAILED_COUNT!
    echo.
) else (
    echo ===============================================
    echo    DAILY ROUTINE WORKFLOW COMPLETED WITH ERRORS!
    echo ===============================================
    echo.
    echo Workflow completed at %date% %time%
    echo.
    echo Summary:
    if "!SCRIPT_1_STATUS!"=="SUCCESS" (echo [OK] Backup Master CSV Files) else (echo [FAILED] Backup Master CSV Files)
    if "!SCRIPT_2_STATUS!"=="SUCCESS" (echo [OK] CMS Unsubs CSV Download) else (echo [FAILED] CMS Unsubs CSV Download)
    if "!SCRIPT_3_STATUS!"=="SUCCESS" (echo [OK] MG Port1 PP Unsubs CSV Download) else (echo [FAILED] MG Port1 PP Unsubs CSV Download)
    if "!SCRIPT_4_STATUS!"=="SUCCESS" (echo [OK] Magnus Group Port1 PP Unsubs CSV Download) else (echo [FAILED] Magnus Group Port1 PP Unsubs CSV Download)
    if "!SCRIPT_5_STATUS!"=="SUCCESS" (echo [OK] Magnus SMTPS PP Unsubs CSV Download) else (echo [FAILED] Magnus SMTPS PP Unsubs CSV Download)
    if "!SCRIPT_6_STATUS!"=="SUCCESS" (echo [OK] Email Invite Net Unsubs Download) else (echo [FAILED] Email Invite Net Unsubs Download)
    if "!SCRIPT_7_STATUS!"=="SUCCESS" (echo [OK] Email Invite Z Unsubs Download) else (echo [FAILED] Email Invite Z Unsubs Download)
    if "!SCRIPT_8_STATUS!"=="SUCCESS" (echo [OK] Find New Records Processing) else (echo [FAILED] Find New Records Processing)
    if "!SCRIPT_9_STATUS!"=="SUCCESS" (echo [OK] Daily Unsubs Upload to DBMS) else (echo [FAILED] Daily Unsubs Upload to DBMS)
    echo.
    echo Statistics:
    echo - Total Scripts: !TOTAL_SCRIPTS!
    echo - Successful: !SUCCESS_COUNT!
    echo - Failed: !FAILED_COUNT!
    echo.
    echo Please check the error messages above and fix any issues.
    echo.
)

echo All scripts completed at %date% %time% >> "%LOG_FILE%"
echo Log file saved to: %LOG_FILE%
echo.
echo Press any key to exit...
pause > nul
