import os
import glob
import win32com.client

xl = win32com.client.gencache.EnsureDispatch('Excel.Application')

fpath = input("Enter path: ")

os.chdir(fpath)

for f in glob.glob('./*.xlsx'):
    fullname = os.path.abspath(f)
    xl.Workbooks.Open(fullname)
    xl.ActiveWorkbook.SaveAs(Filename=fullname.replace('.xlsx','.csv'),
                             FileFormat=win32com.client.constants.xlCSVMSDOS,
                             CreateBackup=False)
    xl.ActiveWorkbook.Close(SaveChanges=False)