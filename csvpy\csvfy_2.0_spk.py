import os
import glob
import pandas as pd
import numpy as np
import re
import random
from tqdm import tqdm
import warnings
from pandas.core.common import SettingWithCopyWarning

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)


def extract_csn(path):
    """Extracts the CSN from the path using regex."""
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        return match.group(1)
    else:
        print("CSN not found in path.")
        return None


def process_unsubscribers(mw_path, port1_path, output_path, csn):
    """Processes unsubscriber data from multiple sources."""
    print("Processing Unsubscribers...")

    # Process mw unsubscribers
    os.chdir(mw_path)
    csv_files = glob.glob("*.csv")
    df_concat = pd.concat(
        [
            pd.read_csv(f, on_bad_lines="skip", usecols=["email", "status", "date_added"])
            for f in csv_files
        ],
        ignore_index=True,
    )
    unsub_df = df_concat[df_concat["status"] == "unsubscribed"]
    unsub_df = unsub_df.drop_duplicates(subset="email")
    unsub_df.rename(
        columns={"email": "Email", "status": "Conference Name", "date_added": "DateTime Info"},
        inplace=True,
    )
    unsub_df["Conference Name"] = unsub_df["Conference Name"].str.replace(
        "unsubscribed", "Global Unsubscriber"
    )
    unsub_df.to_csv(os.path.join(output_path, "mw_unsubscribers.csv"), index=False)

    # Process port1 unsubscribers
    os.chdir(port1_path)
    csv_files = glob.glob("*.csv")
    unsub_mgport = pd.concat(
        [pd.read_csv(f, on_bad_lines="skip", encoding="latin") for f in csv_files],
        ignore_index=True,
    )
    unsub_mgport.rename(
        columns={"Email ID": "Email", "To": "Conference Name", "Date Info": "DateTime Info"},
        inplace=True,
    )
    unsub_mgport["Conference Name"] = unsub_mgport["Conference Name"].replace(
        to_replace=r".*\(.+?\)", value="Global Unsubscriber", regex=True
    )
    unsub_mgport["Conference Name"] = unsub_mgport["Conference Name"].fillna("Global Unsubscriber")
    unsub_mgport.drop_duplicates(subset="Email", inplace=True)
    unsub_mgport.to_csv(os.path.join(output_path, "unsubcriber_sheet.csv"), index=False)

    # Combine and filter global unsubscribers
    os.chdir(output_path)
    csv_files = glob.glob("*.csv")
    glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)
    glob_unsubs["Conference Name"] = glob_unsubs["Conference Name"].str.replace(" - ", "-")
    glob_unsubs.rename(columns={"Email": "Email"}, inplace=True)
    glob_unsubs.drop(["DateTime Info"], axis=1, inplace=True)
    options = ["Global Unsubscriber", csn]
    unsub_df3 = glob_unsubs[glob_unsubs["Conference Name"].isin(options)]
    unsub_df3.drop_duplicates(subset="Email", inplace=True)
    unsub_df3.drop(["Conference Name"], axis=1, inplace=True)
    unsub_df3["Email"] = unsub_df3["Email"].str.lower()
    unsub_df3.to_csv(
        "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv",
        index=False,
    )
    print("Unsubscriber processing complete.")


def process_main_data(path, csn, hb_path, unsub_path, rep_path, pp_gl_unsubs_path):
    """Processes the main data, filters, and prepares it for output."""
    print("Processing Main Data...")
    os.chdir(path)
    os.makedirs("process", exist_ok=True)
    csv_files = glob.glob("*.csv")
    df_concat = pd.concat(
        [pd.read_csv(f, on_bad_lines="skip", low_memory=False) for f in csv_files],
        ignore_index=True,
    )
    df_concat.rename(columns={"Author Name": "Name", "Name ": "Name"}, inplace=True)
    df_concat.dropna(subset="Email", inplace=True)
    df_concat.to_csv(
        os.path.join("./process/", f"{csn}-process_unfiltered.csv"),
        encoding="utf-8-sig",
        index=False,
    )

    df_hb = pd.read_csv(hb_path, on_bad_lines="skip")
    df_unsubs = pd.read_csv(unsub_path)
    df_rep = pd.read_csv(rep_path)
    df_pp_gl_unsubs = pd.read_csv(pp_gl_unsubs_path)
    df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
    df_concat_unsubs["Email"] = df_concat_unsubs["Email"].str.lower()

    df_concat = pd.read_csv(
        os.path.join(path, "./process/", f"{csn}-process_unfiltered.csv"), low_memory=False
    )
    df_hb_filtered = df_concat[~df_concat.Email.isin(df_hb.Email)]
    df_unsubs_filtered = df_hb_filtered[~df_hb_filtered.Email.isin(df_concat_unsubs.Email)]
    df = df_unsubs_filtered[~df_unsubs_filtered.Email.isin(df_rep.Email)]

    if "Article Title" not in df.columns:
        df["Article Title"] = 0
    df.drop(["Article Title"], axis=1, inplace=True)
    df.to_csv(os.path.join("./process/", f"{csn}-process_deduped.csv"), encoding="utf-8-sig", index=False)

    df = pd.read_csv(os.path.join("./process/", f"{csn}-process_deduped.csv"), usecols=["Name", "Email"])
    df = df.replace(r"^\s*$", np.nan, regex=True).fillna("Colleague")
    df = df.drop_duplicates(subset="Email")
    df.to_csv(os.path.join("./process/", f"{csn}-process.csv"), encoding="utf-8-sig", index=False)
    print("Main data processing complete.")
    return df


def generate_output_files(df, csn):
    """Generates the final output CSV files."""
    print("Generating Output Files...")
    subj_list = [
        "Invitation to Submit Abstract for Oral Presentation",
        "Invitation to Submit Abstracts for [CCT_CSNAME]",
        "Call for Papers: 20-Minute Oral Presentation at [CCT_CSNAME]",
        "Submit Your Abstract for [CCT_CSNAME]",
        "Oral Presentation Slots Available at [CCT_CSNAME]",
        "Join Us as a Presenter at [CCT_CSNAME]",
        "Abstract Submission for Oral Presentations at [CCT_CSNAME] is OPEN!",
        "Your Expertise Wanted: Call for 20-Minutes Oral Presentation",
        "[CCT_CSNAME]: Now Accepting Abstract Submissions for Oral Presentations",
        "Share Your Research at [CCT_CSNAME]",
        "Invitation to Submit Abstract for 20-Minute Oral Presentation",
        "Present Your Findings at [CCT_CSNAME]",
        "Call for Oral Presentation Abstracts at [CCT_CSNAME]",
        "[CCT_CSNAME]: Call for 20-Minute Oral Presentation Abstracts",
        "Call for 20-Minute Oral Presentation Abstracts Now Open!",
        "Be Part of the Program: Submit Your Abstract Now!",
        "Call for Abstracts: [CCT_CSNAME]",
        "Submit your Research Abstract for the [CCT_CSNAME]",
        "Abstract Submission Open: [CCT_CSNAME]",
        "Submit Your Abstracts for the [CCT_CSNAME]",
        "Invitation to Speak at the [CCT_CSNAME]",
        "Be Our Guest Speaker at the [CCT_CSNAME]",
        "Call for Speakers: [CCT_CSNAME]",
        "Discovering the Future of Technology at [CCT_CSNAME]",
        "The Ultimate Networking Opportunity: [CCT_CSNAME]",
        "Don't Miss Out on [CCT_CSNAME]: Exploring the Latest Trends and Technologies",
        "Join the Conversations at [CCT_CSNAME]: A Dynamic Forum for Ideas and Inspiration",
    ]

    def replace_placeholder(subjects, placeholder_value):
        return [subject.replace("[CCT_CSNAME]", placeholder_value) for subject in subjects]

    dynamic_subj_list = replace_placeholder(subj_list, csn)
    df["Subject"] = pd.Series(random.choices(dynamic_subj_list, k=len(df)), index=df.index)

    os.makedirs("output", exist_ok=True)

    n_rows = len(df)
    n_temps = 1
    chunk_size = n_rows // n_temps

    for i in tqdm(range(n_temps), desc="Processing: ", colour="green"):
        start = i * chunk_size
        end = (i + 1) * chunk_size if i < n_temps - 1 else n_rows
        chunk = df[start:end]
        chunk.to_csv(
            f"./output/{csn}-{i+1}_Speaker.csv", encoding="utf-8-sig", index=False
        )
    print("Output files generated.")


def cleanup(path, csn):
    """Cleans up temporary files and directories."""
    print("Cleaning up...")
    process_dir = os.path.join(path, "process")
    if os.path.exists(process_dir):
        try:
            os.remove(os.path.join(process_dir, f"{csn}-process_deduped.csv"))
            os.remove(os.path.join(process_dir, f"{csn}-process_unfiltered.csv"))
            os.remove(os.path.join(process_dir, f"{csn}-process.csv"))
            os.rmdir(process_dir)
            print("Cleanup complete.")
        except Exception as e:
            print(f"Error during cleanup: {e}")
    else:
        print("No process directory to clean up.")


def main():
    """Main function to orchestrate the data processing."""
    path = input("Loc: ")
    csn = extract_csn(path)
    if csn is None:
        return

    # Define paths for unsubscriber processing
    mw_path = r"H:/Master Bounces and Unsubs/Postpanel Unsubs/mw"
    port1_path = r"H:/Master Bounces and Unsubs/Postpanel Unsubs/port1"
    unsub_output_path = r"H:/Master Bounces and Unsubs/Postpanel Unsubs"

    # Define paths for main data processing
    hb_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
    unsub_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Unsubscribes.csv"
    rep_path = f"H:/Master Bounces and Unsubs/Replied Ext/Prev_Rep_bounces_csv/{csn}_replied_bouncers.csv"
    pp_gl_unsubs_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv"

    process_unsubscribers(mw_path, port1_path, unsub_output_path, csn)
    df = process_main_data(path, csn, hb_path, unsub_path, rep_path, pp_gl_unsubs_path)
    generate_output_files(df, csn)
    cleanup(path, csn)
    print("Completed!")


if __name__ == "__main__":
    main()
