import os
import glob
import pandas as pd
import re

def combine_csv_files(path):
    """
    Combines multiple CSV files in a directory into a single CSV and Excel file.

    Args:
        path (str): The path to the directory containing the CSV files.
    """

    os.chdir(path)

    # Extract the desired segment (e.g., conference name) from the path
    match = re.search(r"\\([\w\s-]+\d{4})\\?", path)
    if match:
        csn = match.group(1)
        print(f"Conference Segment Name (CSN): {csn}")
    else:
        print("Desired segment not found in path.")
        return

    # Find all CSV files in the directory
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print("No CSV files found in the directory.")
        return

    # Concatenate all CSV files into a single DataFrame
    try:
        df = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False) for f in csv_files], ignore_index=True)
    except pd.errors.EmptyDataError:
        print("One or more CSV files are empty.")
        return
    except Exception as e:
        print(f"An error occurred while reading CSV files: {e}")
        return

    # Rename columns for consistency
    df.rename(columns={'name': 'Author Name', 'Name': 'Author Name', 'email': 'Email', 'col1': 'Author Name'}, inplace=True)

    # Add missing columns if they don't exist
    if 'Article Title' not in df.columns:
        df['Article Title'] = ''
    if 'Affiliation' not in df.columns:
        df['Affiliation'] = ''

    # Remove rows with missing 'Author Name' and duplicate emails
    df.dropna(subset=["Author Name"], inplace=True)
    df.drop_duplicates(subset=["Email"], inplace=True)

    # Create the output directory if it doesn't exist
    output_dir = "merged_files"
    os.makedirs(output_dir, exist_ok=True)

    # Define output filenames
    csv_filename = os.path.join(output_dir, f"{csn}_CSV.csv")
    excel_filename = os.path.join(output_dir, f"{csn}_XLSX.xlsx")

    # Save the DataFrame to CSV and Excel
    try:
        df.to_csv(csv_filename, encoding='utf-8-sig', index=False)
        df.to_excel(excel_filename, index=False)
        print(f"Successfully combined data and saved to:\n- {csv_filename}\n- {excel_filename}")
    except Exception as e:
        print(f"An error occurred while saving files: {e}")


if __name__ == "__main__":
    path = input("Enter path of files for combining: ")
    combine_csv_files(path)
