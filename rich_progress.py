"""
Rich Progress Bar Module

This module provides gradient progress bars using the Rich library.
It can be imported by other scripts to add beautiful progress bars.
"""

import os
import time
import sys

# Try to import rich for gradient progress bars, fall back to tqdm if not available
try:
    from rich.progress import Progress, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn, SpinnerColumn
    from rich.console import Console
    RICH_AVAILABLE = True
except ImportError:
    from tqdm import tqdm
    RICH_AVAILABLE = False
    print("Rich library not found. Using standard tqdm progress bars.")
    print("To install rich, run: pip install rich")

def wait_for_download_with_progress(download_dir, timeout=100, color_scheme="blue"):
    """
    Waits for the download to complete by checking the download directory and shows a progress bar.

    Args:
        download_dir: Directory where the file is being downloaded.
        timeout: Maximum time to wait for the download to complete (in seconds).
        color_scheme: Color scheme for the progress bar ("blue", "green", "purple", "orange")
    """
    # Define color schemes
    color_schemes = {
        "blue": {"complete": "#22BBFF", "finished": "#00AAFF", "text": "cyan"},
        "green": {"complete": "#22DD22", "finished": "#00AA00", "text": "green"},
        "purple": {"complete": "#AA55FF", "finished": "#8844FF", "text": "magenta"},
        "orange": {"complete": "#FFAA22", "finished": "#FF8800", "text": "yellow"}
    }

    # Use blue as default if specified color scheme doesn't exist
    if color_scheme not in color_schemes:
        color_scheme = "blue"

    colors = color_schemes[color_scheme]

    # Get the initial set of files in the directory
    initial_files = set(os.listdir(download_dir))
    end_time = time.time() + timeout

    if RICH_AVAILABLE:
        # Use Rich for gradient progress bar
        console = Console()
        console.print(f"[bold {colors['text']}]Waiting for download to complete...[/bold {colors['text']}]")

        with Progress(
            SpinnerColumn(),
            TextColumn(f"[bold {colors['text']}]{{task.description}}[/bold {colors['text']}]"),
            BarColumn(complete_style=colors["complete"], finished_style=colors["finished"]),
            TaskProgressColumn(),
            TimeRemainingColumn(),
        ) as progress:
            download_task = progress.add_task(f"[{colors['text']}]Downloading...[/{colors['text']}]", total=100)

            while time.time() < end_time:
                files = os.listdir(download_dir)
                # Check for any partially downloaded file (e.g., .crdownload or .tmp extensions)
                downloading_files = [f for f in files if f.endswith('.crdownload') or f.endswith('.tmp')]
                if downloading_files:
                    file_path = os.path.join(download_dir, downloading_files[0])
                    if os.path.exists(file_path):
                        # Get the current size of the file
                        file_size = os.path.getsize(file_path)
                        # Simulate progress (you can adjust this logic based on expected file size if known)
                        file_progress = min(100, int((file_size / (file_size + 1)) * 100))  # Avoid division by zero
                        progress.update(download_task, completed=file_progress)
                    time.sleep(0.5)  # Check more frequently for smoother progress
                else:
                    # Check for newly added .csv files
                    current_files = set(files)
                    new_files = current_files - initial_files
                    completed_files = [f for f in new_files if f.endswith('.csv')]
                    if completed_files:
                        progress.update(download_task, completed=100)
                        console.print(f"[bold green]Download completed! New file(s): {', '.join(completed_files)}[/bold green]")
                        return completed_files

            # If we get here, the download timed out
            console.print("[bold red]Download timed out![/bold red]")
            raise TimeoutError("Download did not complete within the specified timeout.")
    else:
        # Fallback to tqdm if Rich is not available
        progress_bar = tqdm(total=100, desc="Downloading", unit="%", ncols=80)  # Initialize progress bar

        while time.time() < end_time:
            files = os.listdir(download_dir)
            # Check for any partially downloaded file (e.g., .crdownload or .tmp extensions)
            downloading_files = [f for f in files if f.endswith('.crdownload') or f.endswith('.tmp')]
            if downloading_files:
                file_path = os.path.join(download_dir, downloading_files[0])
                if os.path.exists(file_path):
                    # Get the current size of the file
                    file_size = os.path.getsize(file_path)
                    # Simulate progress (you can adjust this logic based on expected file size if known)
                    progress = min(100, int((file_size / (file_size + 1)) * 100))  # Avoid division by zero
                    progress_bar.n = progress
                    progress_bar.refresh()
                time.sleep(1)  # Wait for 1 second before checking again
            else:
                # Check for newly added .csv files
                current_files = set(files)
                new_files = current_files - initial_files
                completed_files = [f for f in new_files if f.endswith('.csv')]
                if completed_files:
                    progress_bar.n = 100  # Set progress bar to 100%
                    progress_bar.refresh()
                    progress_bar.close()
                    print(f"Download completed! New file(s): {', '.join(completed_files)}")
                    return completed_files
        progress_bar.close()
        raise TimeoutError("Download did not complete within the specified timeout.")

def get_console():
    """Get a Rich console if available, otherwise return None."""
    if RICH_AVAILABLE:
        return Console()
    return None

def print_status(message, style="info"):
    """
    Print a status message with appropriate styling if Rich is available.

    Args:
        message: The message to print
        style: The style of the message ("info", "success", "error", "warning", "header")
    """
    styles = {
        "info": "cyan",
        "success": "green",
        "error": "red",
        "warning": "yellow",
        "header": "magenta"
    }

    if RICH_AVAILABLE:
        console = Console()
        if style == "header":
            console.print(f"[bold {styles[style]}]{message}[/bold {styles[style]}]")
        elif style == "error" or style == "warning":
            console.print(f"[bold {styles[style]}]{message}[/bold {styles[style]}]")
        else:
            console.print(f"[{styles[style]}]{message}[/{styles[style]}]")
    else:
        print(message)


def create_progress_bar(total, description="Processing", color_scheme="blue"):
    """
    Create a progress bar for iterating through items.

    Args:
        total: Total number of items to process
        description: Description of the progress bar
        color_scheme: Color scheme for the progress bar ("blue", "green", "purple", "orange")

    Returns:
        A progress bar object and update function
    """
    # Define color schemes
    color_schemes = {
        "blue": {"complete": "#22BBFF", "finished": "#00AAFF", "text": "cyan"},
        "green": {"complete": "#22DD22", "finished": "#00AA00", "text": "green"},
        "purple": {"complete": "#AA55FF", "finished": "#8844FF", "text": "magenta"},
        "orange": {"complete": "#FFAA22", "finished": "#FF8800", "text": "yellow"}
    }

    # Use blue as default if specified color scheme doesn't exist
    if color_scheme not in color_schemes:
        color_scheme = "blue"

    colors = color_schemes[color_scheme]

    if RICH_AVAILABLE:
        # Create a Rich progress bar
        progress = Progress(
            SpinnerColumn(),
            TextColumn(f"[bold {colors['text']}]{{task.description}}[/bold {colors['text']}]"),
            BarColumn(complete_style=colors["complete"], finished_style=colors["finished"]),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("•"),
            TextColumn(f"[bold {colors['text']}]{{task.completed}}/{{task.total}}[/bold {colors['text']}]"),
            TimeRemainingColumn(),
        )

        # Start the progress
        progress.start()
        color_text = colors['text']
        task_id = progress.add_task(f"[{color_text}]{description}[/{color_text}]", total=total)

        # Define update function
        def update_progress(advance=1, description=None):
            if description:
                color_text = colors['text']
                progress.update(task_id, description=f"[{color_text}]{description}[/{color_text}]", advance=advance)
            else:
                progress.update(task_id, advance=advance)

        # Return progress and update function
        return progress, update_progress
    else:
        # Create a tqdm progress bar
        progress_bar = tqdm(total=total, desc=description, unit="items", ncols=80)

        # Define update function
        def update_progress(advance=1, description=None):
            if description:
                progress_bar.set_description(description)
            progress_bar.update(advance)

        # Return progress bar and update function
        return progress_bar, update_progress
