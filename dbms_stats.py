import pandas as pd
import requests

# URL of the page containing the tables
url = 'http://s1.dbms.org.in:8889/dbms/stats/'

# Send a GET request to fetch the page content
response = requests.get(url)

# Use Pandas to read the HTML content and extract tables
tables = pd.read_html(response.text)

# Select the second table (index 1)
table_1 = tables[1]

table_1.to_excel("magnus_conferences_clusters.xlsx", index=False)

# Print the table
print(table_1)

import pandas as pd
import requests

# URL of the page containing the tables
url = 'http://s1.dbms.org.in:8889/dbms/stats/'

# Send a GET request to fetch the page content
response = requests.get(url)

# Use Pandas to read the HTML content and extract tables
tables = pd.read_html(response.text)

# Select the second table (index 1)
table_4 = tables[4]

table_4.to_excel("mathews_clusters.xlsx", index=False)

# Print the table
print(table_4)

import pandas as pd
import requests

# URL of the page containing the tables
url = 'http://s1.dbms.org.in:8889/dbms/stats/'

# Send a GET request to fetch the page content
response = requests.get(url)

# Use Pandas to read the HTML content and extract tables
tables = pd.read_html(response.text)

# Select the second table (index 1)
table_5 = tables[5]

table_5.to_excel("magnus_sources_clusters.xlsx", index=False)

# Print the table
print(table_5)
