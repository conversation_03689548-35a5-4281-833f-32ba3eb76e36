import warnings
import os
import pandas as pd
import re
import numpy as np
from tqdm import tqdm

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

for i in tqdm(range(1), desc="Processing: "):
    import os
    os.chdir(r"H:\Master Bounces and Unsubs\Bounces")

    import zipfile

    #zipping files with .csv and .xlsx
    fantasy_zip = zipfile.ZipFile('H:/Master Bounces and Unsubs/Bounces/Bounces collection.zip', 'w')
    
    for folder, subfolders, files in os.walk('H:/Master Bounces and Unsubs/Bounces/'):
    
        for file in files:
            if folder.endswith(('.exe')):
                fantasy_zip.write(os.path.join(folder, file), os.path.relpath(os.path.join(folder,file), 'H:\Master Bounces and Unsubs\Bounces'), compress_type = zipfile.ZIP_DEFLATED)
    fantasy_zip.close()

    from datetime import date
    current_date = date.today()
    str_current_date = str(current_date)
    file_name = "Bounces collection "+str_current_date+".zip"
    os.rename('Bounces collection.zip', file_name)
    pass

for i in tqdm(range(1), desc="Starting: "):
    os.chdir(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe") #JOURNALS

    import glob 
    import pandas as pd 
    #list all csv files only
    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files
    import pandas as pd

    os.chdir(r"H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe")

    #appending and saving to csv
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode') for f in csv_files], ignore_index=True)
    df_concat[['to', 'eventtype', 'messagecategory']].to_csv("ee_allb.csv", index=False)
    import pandas as pd

    #read csv file 
    df = pd.read_csv("ee_allb.csv", usecols= ['to', 'eventtype', 'messagecategory'])

    #rename column
    df.rename(columns={'to': 'Email'}, inplace=True)

    #applying filter 
    unsub_options = ["AbuseReport", "Unsubscribed"]
    unsub_df = df[df['eventtype'].isin(unsub_options)]

    #applying filter
    sb_options = ["Bounced"]
    sb_df = df[df['eventtype'].isin(sb_options)]

    #applying filter
    hb_options = ["NoMailbox"]
    hb_df = df[df['messagecategory'].isin(hb_options)]

    #dropping columns
    sb_dfd = sb_df.drop(["eventtype", "messagecategory"], axis=1)
    hb_dfd = hb_df.drop(["eventtype", "messagecategory"], axis=1)
    unsubs_dfd = unsub_df.drop(["eventtype", "messagecategory"], axis=1)

    sb_dfdd = sb_dfd[~(sb_dfd.Email.isin(hb_dfd.Email))]

    sb_dfdd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
    hb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)

    #saving to csv
    sb_dfdd.to_csv("ee_sb_j.csv", index=False)
    unsubs_dfd.to_csv("ee_unsubs_j.csv", index=False)
    hb_dfd.to_csv("ee_hb_j.csv", index=False)
    pass

for i in tqdm(range(1), desc="Processing: "):
    #import modules
    import os

    os.chdir(r"H:\Master Bounces and Unsubs\Bounces\ee.exe") #CONFERENCES

    import glob 
    import pandas as pd 
    #list all csv files only
    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files
    import pandas as pd

    #appending and saving to csv
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode') for f in csv_files ], ignore_index=True)
    df_concat[['to', 'eventtype', 'messagecategory']].to_csv("ee_allb.csv", index=False)
    import pandas as pd

    #read csv file 
    df = pd.read_csv("ee_allb.csv", usecols= ['to', 'eventtype', 'messagecategory'])

    #rename column
    df.rename(columns={'to': 'Email'}, inplace=True)

    #applying filter 
    unsub_options = ["AbuseReport", "Unsubscribed"]
    unsub_df = df[df['eventtype'].isin(unsub_options)]

    #applying filter
    sb_options = ["Bounced"]
    sb_df = df[df['eventtype'].isin(sb_options)]

    #applying filter
    hb_options = ["NoMailbox"]
    hb_df = df[df['messagecategory'].isin(hb_options)]

    #dropping columns
    sb_dfd = sb_df.drop(["eventtype", "messagecategory"], axis=1)
    hb_dfd = hb_df.drop(["eventtype", "messagecategory"], axis=1)
    unsubs_dfd = unsub_df.drop(["eventtype", "messagecategory"], axis=1)

    sb_dfdd = sb_dfd[~(sb_dfd.Email.isin(hb_dfd.Email))]

    sb_dfdd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
    hb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)

    #saving to csv
    sb_dfdd.to_csv("ee_sb.csv", index=False)
    unsubs_dfd.to_csv("ee_unsubs.csv", index=False)
    hb_dfd.to_csv("ee_hb.csv", index=False)
    pass

for i in tqdm(range(1), desc="Processing: "):
    #import sys
    #import os
    #os.system('cmd /c "del /f *.csv"')

    #import modules
    import os

    os.chdir(r"H:\Master Bounces and Unsubs\Bounces\sg.exe")

    import glob
    import pandas as pd
    #list all csv files only
    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files
    #appending and saving to csv
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip') for f in csv_files], ignore_index=True)
    df_concat.to_csv("allsg_exp.csv", index=False)
    import pandas as pd

    #reading csv
    df = pd.read_csv("allsg_exp.csv", usecols = ["event", "email", "reason"])

    #renaming column
    df.rename(columns={'email': 'Email'}, inplace=True)

    #applying filter
    unsub_filtr = ["spamreport", "unsubscribed", "complaint"]
    df_unsubs = df[df['event'].isin(unsub_filtr)]

    #dropping columns
    df_unsubsdc = df_unsubs.drop(["event","reason"], axis=1)

    #saving to csv
    df_unsubsdc.to_csv("sg_unsubs.csv", index=False)

    #applying filter
    allb_filtr = ["bounce"]
    df_allb = df[df['event'].isin(allb_filtr)]

    #applying filter
    df_hb = df_allb[df_allb["reason"].str.contains("User unknown") |
                    df_allb["reason"].str.contains("service disabled") |
                    df_allb["reason"].str.contains("does not exist") |
                    df_allb["reason"].str.contains("Address unknown") |
                    df_allb["reason"].str.contains("User not found") |
                    df_allb["reason"].str.contains("no mailbox") |
                    df_allb["reason"].str.contains("no mail-box") |
                    df_allb["reason"].str.contains("no mail") |
                    df_allb["reason"].str.contains("unrecognized address") |
                    df_allb["reason"].str.contains("mailbox is unavailable") |
                    df_allb["reason"].str.contains("mailbox unavailable")]

    #dropping columns
    df_hbdc = df_hb.drop(["event", "reason"], axis=1)

    #saving to csv
    df_hbdc.to_csv("sg_hb.csv", index=False)

    #applying filter
    df_sb = df_allb[~(df_allb.Email.isin(df_hb.Email))]

    #dropping columns
    df_sbdc = df_sb.drop(["event", "reason"], axis=1)

    #drop duplicates from column
    df_sbdcd = df_sbdc.drop_duplicates(subset="Email", keep="first", inplace=False)

    #saving to csv
    df_sbdcd.to_csv("sg_sb.csv", index=False)
    pass

for i in tqdm(range(1), desc="Processing: "):
    #import modules
    import os

    os.chdir(r"H:\Master Bounces and Unsubs\Bounces\sib.exe")

    import glob
    import pandas as pd
    #list all csv files only
    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files
    #appending and saving to csv
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip') for f in csv_files], ignore_index=True)
    df_concat.to_csv("sib_exp.csv", index=False)
    import pandas as pd

    #reading csv
    df = pd.read_csv("sib_exp.csv", usecols= ['st_text', 'email'])

    #rename column
    df.rename(columns={'email': 'Email'}, inplace=True)

    #applying filter
    hb_options = ["Hard bounce", "Invalid"]
    hb_df = df[df['st_text'].isin(hb_options)]
    hbd_df = hb_df.drop(['st_text'], axis=1)

    #applying filter
    sb_options = ["Soft bounce", "Deferred", "Blocked"]
    sb_df = df[df['st_text'].isin(sb_options)]
    sbd_df = sb_df.drop(['st_text'], axis=1)

    #applying filter
    unsub_options = ["Complaint", "Unsubscribed"]
    unsub_df = df[df['st_text'].isin(unsub_options)]
    unsubd_df = unsub_df.drop(['st_text'], axis=1)

    #dropping duplicates in the column 'Email'
    hbd_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
    sbd_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
    unsubd_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)

    #saving to csv
    hbd_df.to_csv("sib_hb.csv", index=False)
    sbd_df.to_csv("sib_sb.csv", index=False)
    unsubd_df.to_csv("sib_unsubs.csv", index=False)
    pass

for i in tqdm(range(1), desc="Processing: "):
    #import modules
    import os

    os.chdir(r"H:\Master Bounces and Unsubs\Bounces\sp.exe")

    import glob
    import pandas as pd
    #list all csv files only
    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files
    #appending and saving to csv
    df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
    df_concat.to_csv("allsp_exp.csv", index=False)
    import pandas as pd

    #reading csv
    df = pd.read_csv("allsp_exp.csv", usecols = ["reason", "type", "raw_rcpt_to"])

    #renaming column
    df.rename(columns={'raw_rcpt_to': 'Email'}, inplace=True)

    #applying filter
    unsub_filtr = ["list_unsubscribe", "spam_complaint"]
    df_unsubs = df[df['type'].isin(unsub_filtr)]

    #dropping columns
    df_unsubsdc = df_unsubs.drop(["type","reason"], axis=1)

    #saving to csv
    df_unsubsdc.to_csv("sp_unsubs.csv", index=False)

    #applying filter
    allb_filtr = ["bounce"]
    df_allb = df[df['type'].isin(allb_filtr)]

    #applying filter
    df_hb = df_allb[df_allb["reason"].str.contains("does not exist") | 
                    df_allb["reason"].str.contains("Does Not Exist") |
                    df_allb["reason"].str.contains("mailbox unavailable") |
                    df_allb["reason"].str.contains("Mailbox unavailable") |
                    df_allb["reason"].str.contains("mailbox is unavailable") |
                    df_allb["reason"].str.contains("no mail-box") |
                    df_allb["reason"].str.contains("Recipient not found") |
                    df_allb["reason"].str.contains("email account that you tried to reach is disabled") |
                    df_allb["reason"].str.contains("no mailbox") |
                    df_allb["reason"].str.contains("No mailbox")]

    #dropping columns
    df_hbdc = df_hb.drop(["type", "reason"], axis=1)

    #saving to csv
    df_hbdc.to_csv("sp_hb.csv", index=False)

    #applying filter
    df_sb = df_allb[~(df_allb.Email.isin(df_hb.Email))]

    #dropping columns
    df_sbdc = df_sb.drop(["type", "reason"], axis=1)

    #drop duplicates from column
    df_sbdcd = df_sbdc.drop_duplicates(subset="Email", keep="first", inplace=False)

    #saving to csv
    df_sbdcd.to_csv("sp_sb.csv", index=False)
    pass


#Concatenate HB/SB/UNSUBS

for i in tqdm(range(1), desc="Processing: "):
    import os
    os.chdir(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs")
    import pandas as pd

    #reading files
    df_ee_hb = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/ee.exe/ee_hb.csv")
    df_sg_hb = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/sg.exe/sg_hb.csv")
    df_sib_hb = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/sib.exe/sib_hb.csv")
    #df_ee_hb_j = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/ee.exe/ee_j.exe/ee_hb_j.csv")
    df_sp_hb = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/sp.exe/sp_hb.csv")

    #append files to single file
    pd.concat([df_ee_hb,df_sg_hb,df_sib_hb,df_sp_hb]).to_csv('H:/Master Bounces and Unsubs/Weekly_hardbounces.csv', index=False) #skipped df_ee_hb_j

    #reading file
    wk_hb = pd.read_csv("H:/Master Bounces and Unsubs/Weekly_hardbounces.csv")

    #drop duplicates
    wk_hb.drop_duplicates(subset="Email", keep="first", inplace=True)
    wk_hb[['Email']] = wk_hb[['Email']].applymap(lambda x:x.lower())

    #saving file
    wk_hb.to_csv("H:/Master Bounces and Unsubs/Weekly_hardbounces.csv", index=None, header=True)
    #df = pd.DataFrame(pd.read_csv("H:/Master Bounces and Unsubs/Weekly_hardbounces.csv"))

    #reading files
    df_ee_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/ee.exe/ee_unsubs.csv")
    df_sg_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/sg.exe/sg_unsubs.csv")
    df_sib_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/sib.exe/sib_unsubs.csv")
    df_sp_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Bounces/sp.exe/sp_unsubs.csv")

    #appending files
    pd.concat([df_ee_unsubs,df_sg_unsubs,df_sib_unsubs,df_sp_unsubs]).to_csv('H:/Master Bounces and Unsubs/Weekly_unsubscribes.csv', index=False)

    #reading file
    wk_unsub = pd.read_csv("H:/Master Bounces and Unsubs/Weekly_unsubscribes.csv")

    #drop duplicates
    wk_unsub.drop_duplicates(subset="Email", keep="first", inplace=True)
    wk_unsub[['Email']] = wk_unsub[['Email']].applymap(lambda x:x.lower())

    #saving file
    wk_unsub.to_csv("H:/Master Bounces and Unsubs/Weekly_unsubscribes.csv", index=None, header=True)
    #df = pd.DataFrame(pd.read_csv("H:/Master Bounces and Unsubs/Weekly_unsubscribes.csv"))
    pass

for i in tqdm(range(1), desc="Processing: "):
    import os
    import shutil
    shutil.copy("H:/Master Bounces and Unsubs/Bounces/ee.exe/ee_j.exe/ee_sb_j.csv", "H:/Master Bounces and Unsubs/Master_bounces_tmp/ee_sb_j.csv")
    shutil.copy("H:/Master Bounces and Unsubs/Bounces/ee.exe/ee_j.exe/ee_unsubs_j.csv", "H:/Master Bounces and Unsubs/Master_bounces_tmp/ee_unsubs_j.csv")
    shutil.copy("H:/Master Bounces and Unsubs/Bounces/sib.exe/sib_sb.csv", "H:/Master Bounces and Unsubs/Master_bounces_tmp/sib_sb.csv")
    shutil.copy("H:/Master Bounces and Unsubs/Bounces/ee.exe/ee_sb.csv", "H:/Master Bounces and Unsubs/Master_bounces_tmp/ee_sb.csv")
    shutil.copy("H:/Master Bounces and Unsubs/Bounces/sg.exe/sg_sb.csv", "H:/Master Bounces and Unsubs/Master_bounces_tmp/sg_sb.csv")
    shutil.copy("H:/Master Bounces and Unsubs/Bounces/sp.exe/sp_sb.csv", "H:/Master Bounces and Unsubs/Master_bounces_tmp/sp_sb.csv")

    import os
    os.chdir(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs")

    import pandas as pd

    #reading and appending files
    df_master_hardbounces = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv")
    df_weekly_hardbounces = pd.read_csv("H:/Master Bounces and Unsubs/Weekly_Hardbounces.csv")
    pd.concat([df_master_hardbounces,df_weekly_hardbounces]).to_csv('Master_Hardbounces.csv', index=False)

    #reading and appending files
    df_master_unsubscribes = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Unsubscribes.csv")
    df_weekly_unsubscribes = pd.read_csv("H:/Master Bounces and Unsubs/Weekly_Unsubscribes.csv")
    pd.concat([df_master_unsubscribes,df_weekly_unsubscribes]).to_csv('Master_Unsubscribes.csv', index=False)

    #reading file
    df = pd.DataFrame(pd.read_csv("Master_Hardbounces.csv", encoding='utf-8'))

    #reading file, dropping duplicates, and saving to csv
    read_file = pd.read_csv("Master_Hardbounces.csv")
    read_file = read_file.drop_duplicates(subset="Email", keep="first", inplace=False)
    #read_filed = read_file.drop('Unnamed: 1', axis=1)
    read_file.to_csv("Master_Hardbounces.csv", index=False, header=True)

    #reading file
    df1 = pd.DataFrame(pd.read_csv("Master_Unsubscribes.csv"))

    #reading file, dropping duplicates, and saving to csv
    read_file1 = pd.read_csv("Master_Unsubscribes.csv")
    read_file1 = read_file1.drop_duplicates(subset="Email", keep="first", inplace=False)
    read_file1.to_csv("Master_Unsubscribes.csv", index=None, header=True)

    import os
    os.chdir(r"H:\Master Bounces and Unsubs\Master_bounces_tmp")

    import pandas as pd

    sib_excel_df = pd.read_csv('Sendinblue_Softbounces.csv')
    sib_csv_df = pd.read_csv('sib_sb.csv')

    sg_excel_df = pd.read_csv('Sendgrid_Softbounces.csv')
    sg_csv_df = pd.read_csv('sg_sb.csv')

    ee_excel_df = pd.read_csv('Elasticemail_Softbounces.csv')
    ee_csv_df = pd.read_csv('ee_sb.csv')

    sp_excel_df = pd.read_csv('Sparkpost_Softbounces.csv')
    sp_csv_df = pd.read_csv('sp_sb.csv')

    sib_concat_df = pd.concat([sib_excel_df, sib_csv_df])
    sib_concat_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
    sib_concat_df.to_csv('Sendinblue_Softbounces.csv', mode='w+', index=False)

    sg_concat_df = pd.concat([sg_excel_df, sg_csv_df])
    sg_concat_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
    sg_concat_df.to_csv('Sendgrid_Softbounces.csv', mode='w+', index=False)

    ee_concat_df = pd.concat([ee_excel_df, ee_csv_df])
    ee_concat_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
    ee_concat_df.to_csv('Elasticemail_Softbounces.csv', mode='w+', index=False)

    sp_concat_df = pd.concat([sp_excel_df, sp_csv_df])
    sp_concat_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
    sp_concat_df.to_csv('Sparkpost_Softbounces.csv', mode='w+', index=False)

    import shutil
    import pandas as pd
    import os
    import pandas.io.formats.excel

    pd.io.formats.excel.ExcelFormatter.header_style = None

    os.chdir(r'H:\Master Bounces and Unsubs\Master_bounces_tmp')
    #shutil.copy("Master_Bounce_and_Blocks.xlsx", "Master Bounces and Unsubs/Master_Bounce_and_Blocks.xlsx")
    os.remove("ee_sb.csv")
    os.remove("sg_sb.csv")
    os.remove("sib_sb.csv")
    os.remove("ee_sb_j.csv")
    os.remove("sp_sb.csv")
    os.rename("ee_unsubs_j.csv", "Weekly_unsubscribes_journal.csv")
    shutil.move("Weekly_unsubscribes_journal.csv", "H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_unsubscribes_journal.csv")

    os.chdir(r"H:\Master Bounces and Unsubs")
    shutil.move("Weekly_hardbounces.csv", "H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_hardbounces.csv")
    shutil.move("Weekly_unsubscribes.csv", "H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_unsubscribes.csv")

    df_unsub_c = pd.read_csv("H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_unsubscribes.csv")
    df_unsub_c.to_excel("H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_unsubscribes.xlsx", index=False)
    df_unsub_j = pd.read_csv("H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_unsubscribes_journal.csv")
    df_unsub_j.to_excel("H:/Master Bounces and Unsubs/Weekly Bounces and Unsubs/Weekly_unsubscribes_journal.xlsx", index=False)
    pass

for i in tqdm(range(1), desc="Finishing: "):
    import os
    import shutil

    source = r"H:\Master Bounces and Unsubs\Master_bounces_tmp"
    destination = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\Master_Softbounces"

    allfiles = os.listdir(source)

    for f in allfiles:

        shutil.copy(source+ r"\\" + f, destination)

    os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw')
    #list all csv files only
    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files

    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)

    df = df_concat

    status = ["unsubscribed"]
    unsub_df = df[df['status'].isin(status)]

    unsub_df.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True)
    unsub_dfg = unsub_df.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber'))

    unsub_dfg.to_csv('H:/Master Bounces and Unsubs/Postpanel Unsubs/mw_unsubscribers.csv', index=False)

    os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs')

    unsub_mgport = pd.read_csv('unsubcriber_sheet.csv')
    unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
    unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
    unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)

    os.chdir(r"H:\Master Bounces and Unsubs\Postpanel Unsubs")

    csv_files = glob.glob('*.{}'.format('csv'))
    csv_files

    glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

    glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))

    glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True)

    glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)

    options = ['Global Unsubscriber']

    unsub_df = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]

    unsub_df.drop_duplicates(subset='Email', inplace=True, ignore_index=True)

    unsub_df.drop(['Conference Name'], axis=1, inplace=True)
    unsub_df[['Email']] = unsub_df[['Email']].applymap(lambda x:x.lower())

    unsub_df.to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv", index=False)

    import os
    os.chdir(r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs")

    import zipfile

    #zipping files with .csv and .xlsx
    fantasy_zip = zipfile.ZipFile('H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master Bounces and Unsubs.zip', 'w')
    
    for folder, subfolders, files in os.walk('H:/Master Bounces and Unsubs/Master Bounces and Unsubs'):
    
        for file in files:
            if file.endswith(('.csv')):
                fantasy_zip.write(os.path.join(folder, file), os.path.relpath(os.path.join(folder,file), 'H:/Master Bounces and Unsubs/Master Bounces and Unsubs'), compress_type = zipfile.ZIP_DEFLATED)
    fantasy_zip.close()

    from datetime import date
    current_date = date.today()
    str_current_date = str(current_date)
    file_name = "Master Bounces and Unsubs "+str_current_date+".zip"
    os.rename('Master Bounces and Unsubs.zip', file_name)
    pass

print("Finished!")