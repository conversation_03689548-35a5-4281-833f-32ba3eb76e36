#!/usr/bin/env python3
"""
Simple New Records Finder Script
Compares CSV files and finds new records
"""

import os
import pandas as pd
import glob

def find_new_records_simple():
    """Simple function to find new records."""
    
    # Define directories
    main_dir = r"H:\<PERSON> Bounces and Unsubs\<PERSON> Bounces and Unsubs"
    backup_dir = os.path.join(main_dir, "CSV_Backup")
    new_records_dir = os.path.join(main_dir, "new_records")
    
    # Check directories
    if not os.path.exists(main_dir):
        print(f"Error: Main directory not found: {main_dir}")
        return False
    
    if not os.path.exists(backup_dir):
        print(f"Error: CSV_Backup directory not found.")
        print("Please run the backup script first.")
        return False
    
    # Create new_records directory
    os.makedirs(new_records_dir, exist_ok=True)
    print(f"Using new_records directory: {new_records_dir}")
    
    # Get CSV files from main directory
    main_csv_files = glob.glob(os.path.join(main_dir, "*.csv"))
    
    if not main_csv_files:
        print("No CSV files found in main directory.")
        return False
    
    print(f"\nProcessing {len(main_csv_files)} CSV files...")
    
    total_new_records = 0
    
    for main_file in main_csv_files:
        file_name = os.path.basename(main_file)
        backup_file = os.path.join(backup_dir, file_name)
        new_records_file = os.path.join(new_records_dir, file_name)
        
        print(f"\nProcessing: {file_name}")
        
        try:
            # Read main file
            df_main = pd.read_csv(main_file, encoding='utf-8-sig')
            print(f"  Main file: {len(df_main)} records")
            
            if os.path.exists(backup_file):
                # Read backup file
                df_backup = pd.read_csv(backup_file, encoding='utf-8-sig')
                print(f"  Backup file: {len(df_backup)} records")
                
                # Check columns match
                if list(df_main.columns) != list(df_backup.columns):
                    print(f"  Warning: Column mismatch - skipping {file_name}")
                    continue
                
                # Clean data for comparison (lowercase, strip spaces)
                for col in df_main.columns:
                    df_main[col] = df_main[col].astype(str).str.lower().str.strip()
                    df_backup[col] = df_backup[col].astype(str).str.lower().str.strip()
                
                # Remove duplicates from main file
                df_main_clean = df_main.drop_duplicates()
                
                # Find new records
                df_merged = df_main_clean.merge(df_backup, how='left', indicator=True)
                df_new = df_merged[df_merged['_merge'] == 'left_only'].drop('_merge', axis=1)
                
                new_count = len(df_new)
                print(f"  New records: {new_count}")
                
                if new_count > 0:
                    df_new.to_csv(new_records_file, index=False, encoding='utf-8-sig')
                    print(f"  ✓ Saved to new_records/{file_name}")
                    total_new_records += new_count
                else:
                    print(f"  No new records found")
            else:
                # No backup file - treat all as new
                print(f"  No backup found - saving all records as new")
                df_main_clean = df_main.drop_duplicates()
                df_main_clean.to_csv(new_records_file, index=False, encoding='utf-8-sig')
                print(f"  ✓ Saved {len(df_main_clean)} records to new_records/{file_name}")
                total_new_records += len(df_main_clean)
                
        except Exception as e:
            print(f"  ✗ Error: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"SUMMARY")
    print(f"{'='*50}")
    print(f"Total new records found: {total_new_records}")
    print(f"Saved to: {new_records_dir}")
    print(f"{'='*50}")
    
    return True

def main():
    """Main function."""
    print("="*50)
    print("NEW RECORDS FINDER")
    print("="*50)
    
    try:
        success = find_new_records_simple()
        
        if success:
            print("\n✓ Processing completed successfully!")
        else:
            print("\n✗ Processing failed!")
            
    except Exception as e:
        print(f"\nError: {str(e)}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
