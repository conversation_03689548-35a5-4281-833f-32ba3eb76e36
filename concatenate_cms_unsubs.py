import os
import glob
import pandas as pd
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
import re

def create_gradient_progress():
    """Create a progress bar with gradient colors"""
    return Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
        "[progress.percentage]{task.percentage:>3.0f}%",
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=Console()
    )

def concatenate_cms_unsubs():
    """
    Concatenate multiple CSV files from CMS directory and split by Conference Name
    """
    console = Console()
    
    # Display header
    header_text = Text("CMS Unsubscribers CSV Concatenator", style="bold magenta")
    console.print(Panel(header_text, expand=False))
    
    # Directory path
    directory_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\cms"
    
    # Check if directory exists
    if not os.path.exists(directory_path):
        console.print(f"[red]Error: Directory not found: {directory_path}[/red]")
        return
    
    # Change to the directory
    original_dir = os.getcwd()
    os.chdir(directory_path)
    
    try:
        # Find all CSV files, excluding already concatenated files
        all_csv_files = glob.glob('*.csv')
        csv_files = [f for f in all_csv_files if not f.startswith('cms_unsubs_concatenated_')]
        
        if not csv_files:
            console.print("[red]No CSV files found in the directory (excluding already concatenated files).[/red]")
            return
        
        console.print(f"[green]Found {len(csv_files)} CSV files:[/green]")
        for file in csv_files:
            console.print(f"  • {file}")
        
        # Initialize list to store dataframes
        all_dfs = []
        total_rows = 0
        
        # Create progress bar
        with create_gradient_progress() as progress:
            task = progress.add_task("Reading CSV files...", total=len(csv_files))
            
            for file in csv_files:
                try:
                    # Read CSV file
                    df = pd.read_csv(file, encoding='utf-8-sig', on_bad_lines='skip')
                    
                    # Verify expected columns exist
                    expected_columns = ['Email', 'Conference Name', 'DateTime Info']
                    if not all(col in df.columns for col in expected_columns):
                        progress.console.print(f"  ⚠ Warning: {file} doesn't have expected columns. Found: {list(df.columns)}")
                        # Try to standardize column names
                        if 'email' in df.columns:
                            df.rename(columns={'email': 'Email'}, inplace=True)
                        if 'conference name' in df.columns:
                            df.rename(columns={'conference name': 'Conference Name'}, inplace=True)
                        if 'datetime info' in df.columns:
                            df.rename(columns={'datetime info': 'DateTime Info'}, inplace=True)
                    
                    all_dfs.append(df)
                    total_rows += len(df)
                    
                    progress.console.print(f"  ✓ Read {file}: {len(df)} rows")
                    
                except Exception as e:
                    progress.console.print(f"  ✗ Error reading {file}: {str(e)}")
                
                progress.advance(task)
        
        if not all_dfs:
            console.print("[red]No data could be read from any CSV files.[/red]")
            return
        
        # Concatenate all dataframes
        console.print("\n[yellow]Concatenating data...[/yellow]")
        combined_df = pd.concat(all_dfs, ignore_index=True)

        # Clean conference names by removing spaces around hyphens
        console.print("\n[yellow]Cleaning conference names (removing spaces around hyphens)...[/yellow]")
        combined_df['Conference Name'] = combined_df['Conference Name'].str.replace(r'\s*-\s*', '-', regex=True)
        
        # Create merged directory if it doesn't exist
        merged_dir = "merged"
        os.makedirs(merged_dir, exist_ok=True)
        
        # Save the main concatenated file
        main_output_filename = os.path.join(merged_dir, "cms_unsubs_concatenated.csv")
        console.print(f"\n[yellow]Saving main concatenated file to merged folder...[/yellow]")
        combined_df.to_csv(main_output_filename, index=False, encoding='utf-8-sig')
        
        # Split data by 'Conference Name' column
        console.print(f"\n[yellow]Splitting data by 'Conference Name' column...[/yellow]")
        
        # Create separated directory inside merged folder for split files
        separated_dir = os.path.join(merged_dir, "separated")
        os.makedirs(separated_dir, exist_ok=True)
        
        unique_conference_names = combined_df['Conference Name'].unique()
        split_files = []
        
        with create_gradient_progress() as progress:
            split_task = progress.add_task("Creating split files...", total=len(unique_conference_names))
            
            for conference_name in unique_conference_names:
                # Filter data for this conference
                filtered_df = combined_df[combined_df['Conference Name'] == conference_name]
                
                # Extract conference code from brackets or use special handling for Global Unsubscriber
                if "Global Unsubscriber" in conference_name:
                    safe_filename = "Global_Unsubscriber"
                else:
                    # Extract text within parentheses
                    match = re.search(r'\(([^)]+)\)', conference_name)
                    if match:
                        conference_code = match.group(1)
                        # Remove spaces around hyphens in conference code
                        conference_code = re.sub(r'\s*-\s*', '-', conference_code)
                        safe_filename = f"{conference_code}_Unsubscribes"
                    else:
                        # Use the conference name directly (already cleaned of spaces around hyphens)
                        # Create a safe filename by keeping spaces and hyphens, only removing invalid characters
                        safe_filename = "".join(c for c in conference_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                        # Keep spaces in the filename, only truncate if too long
                        safe_filename = safe_filename[:50]
                        if not safe_filename.endswith('_Unsubscribes'):
                            safe_filename += "_Unsubscribes"
                
                # Create output filename in separated directory
                split_filename = os.path.join(separated_dir, f"{safe_filename}.csv")
                
                # Save the split file
                filtered_df.to_csv(split_filename, index=False, encoding='utf-8-sig')
                split_files.append((split_filename, len(filtered_df), conference_name))
                
                progress.console.print(f"  ✓ Created {safe_filename}.csv: {len(filtered_df)} rows")
                progress.advance(split_task)
        
        # Display summary
        split_files_summary = "\n".join([f"  • {os.path.basename(filename)}: {row_count} rows ({conf_name})" 
                                       for filename, row_count, conf_name in split_files])
        
        summary_text = f"""
[bold green]Concatenation and Splitting Complete![/bold green]

[bold]Summary:[/bold]
• Files processed: {len(csv_files)}
• Total rows: {len(combined_df)}
• Main output file: merged/{os.path.basename(main_output_filename)}
• Split files created: {len(split_files)} (saved in merged/separated/)

[bold]Split files by 'Conference Name' (in merged/separated/):[/bold]
{split_files_summary}

[bold]Columns in output:[/bold]
{', '.join(combined_df.columns.tolist())}
        """
        
        console.print(Panel(summary_text, title="Results", expand=False))
        
    except Exception as e:
        console.print(f"[red]An error occurred: {str(e)}[/red]")
    
    finally:
        # Change back to original directory
        os.chdir(original_dir)

if __name__ == "__main__":
    concatenate_cms_unsubs()
