import os
import numpy as np
import pandas as pd
os.chdir(r'H:\Master Bounces and Unsubs\Replied Ext')

Filename = input('Enter Filename: ')
fname = Filename+'.xlsx'

df_mwbl = pd.read_excel(fname)
df_mwbl1 = df_mwbl.dropna()
df_mwbl1.rename(columns = {'Conference Short Name': 'Reason'}, inplace = True)
df_mwbl1.to_csv('Blocklist_mw.csv', mode='w+', index=False)


import email, smtplib, ssl

from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

subject = "Mailwizz Blocklist"
body = "Please upload mailwizz blocklist: ./backend/index.php/email-blacklist/index"
sender_email = "<EMAIL>"
receiver_email = "<EMAIL>"
password = "Messi@2017"

# Create a multipart message and set headers
message = MIMEMultipart()
message["From"] = sender_email
message["To"] = receiver_email
message["Subject"] = subject
message["Bcc"] = sender_email  # Recommended for mass emails

# Add body to email
message.attach(MIMEText(body, "plain"))

filename = "Blocklist_mw.csv"  # In same directory as script

# Open PDF file in binary mode
with open(filename, "rb") as attachment:
    # Add file as application/octet-stream
    # Email client can usually download this automatically as attachment
    part = MIMEBase("application", "octet-stream")
    part.set_payload(attachment.read())

# Encode file in ASCII characters to send by email    
encoders.encode_base64(part)

# Add header as key/value pair to attachment part
part.add_header(
    "Content-Disposition",
    f"attachment; filename= {filename}",
)

# Add attachment to message and convert message to string
message.attach(part)
text = message.as_string()

# Log in to server using secure context and send email
context = ssl.create_default_context()
with smtplib.SMTP_SSL("smtppro.zoho.in", 465, context=context) as server:
    server.login(sender_email, password)
    server.sendmail(sender_email, receiver_email, text)

print('Blocklist <NAME_EMAIL>')