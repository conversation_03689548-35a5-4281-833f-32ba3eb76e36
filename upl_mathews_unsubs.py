import os
import time
import pandas as pd
import pyautogui  # Import PyAutoGUI at the top level
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def upload_file_to_website(file_path, website_url, username="admin", password="Admin@890", headless=True):
    """Upload a file to the specified website using Selenium.

    Args:
        file_path (str): Path to the file to upload
        website_url (str): URL of the website to upload to
        username (str): Username for login
        password (str): Password for login
        headless (bool): Whether to run in headless mode

    Returns:
        bool: True if upload was successful, False otherwise
    """
    # Verify file exists
    if not os.path.exists(file_path):
        print(f"Error: File does not exist at {file_path}")
        return False

    # Selenium setup
    options = webdriver.ChromeOptions()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])

    if headless:
        #options.add_argument("--headless")  # Run in headless mode
        options.add_argument("--window-size=1920,1080")  # Set window size for headless mode
        print("Running in headless mode")
    else:
        print("Running in visible mode")

    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")

    try:
        driver = webdriver.Chrome(options=options)
        driver.set_window_size(1920, 1080)  # Ensure proper rendering

        # Navigate to the website
        print(f"Navigating to website: {website_url}")
        driver.get(website_url)

        # Login
        print(f"Logging in with username: {username}")
        driver.find_element(By.ID, "name").send_keys(username)
        driver.find_element(By.ID, "password").send_keys(password)
        driver.find_element(By.XPATH, "//button[@id='login']").click()
        time.sleep(2)

        # Step 1: Navigate to unsubscriber page
        print("Step 1: Navigating to unsubscriber page...")
        try:
            unsubscriber_link = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "unsubscriber"))
            )
            unsubscriber_link.click()
            print("Clicked unsubscriber link")
        except Exception as e:
            print(f"Error finding unsubscriber link by ID: {e}")
            # Try alternative method
            try:
                driver.find_element(By.XPATH, "//a[contains(text(), 'unsubscribers') or contains(@href, 'unsubscribes_list')]").click()
                print("Clicked unsubscriber link using alternative selector")
            except Exception as e2:
                print(f"All attempts to find unsubscriber link failed: {e2}")
                return False

        time.sleep(2)

        # Step 2: Click the "Upload Un-Subscribes" button
        print("Step 2: Clicking 'Upload Un-Subscribes' button...")
        try:
            upload_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "demo-bootbox-prompt"))
            )
            print("Found 'Upload Un-Subscribes' button by ID")
            upload_button.click()
            print("Clicked 'Upload Un-Subscribes' button")
        except Exception as e:
            print(f"Error finding 'Upload Un-Subscribes' button by ID: {e}")
            # Try alternative methods
            try:
                # Try by class and attributes
                button = driver.find_element(By.XPATH, "//button[@class='btn btn-warning btn-br-align'][@data-toggle='modal'][@data-target='#myModal']")
                print("Found 'Upload Un-Subscribes' button by attributes")
                button.click()
            except Exception as e2:
                print(f"Error finding button by attributes: {e2}")
                try:
                    # Try by text content
                    button = driver.find_element(By.XPATH, "//button[contains(text(), 'Upload Un-Subscribes')]")
                    print("Found 'Upload Un-Subscribes' button by text")
                    button.click()
                except Exception as e3:
                    print(f"All attempts to find 'Upload Un-Subscribes' button failed: {e3}")
                    return False

        # Wait for modal dialog to appear
        time.sleep(2)

        # Step 3: Find file upload element
        print("Step 3: Looking for file upload element...")
        try:
            # Wait for the element to be present in the DOM
            upload_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "file"))
            )
            print("Found file input element by ID")
        except TimeoutException:
            print("Timeout waiting for file input by ID, trying alternative selectors")
            try:
                # Try by CSS selector with all attributes
                upload_element = driver.find_element(By.CSS_SELECTOR, "input[type='file'][name='file'][class='form-control'][id='file']")
                print("Found file input element by CSS selector")
            except NoSuchElementException:
                try:
                    # Try by XPath with all attributes
                    upload_element = driver.find_element(By.XPATH, "//input[@type='file' and @name='file' and @class='form-control' and @id='file']")
                    print("Found file input element by XPath")
                except NoSuchElementException:
                    print("All attempts to find file input element failed")
                    return False

        # Verify the element is found and has the correct attributes
        print(f"Element found: {upload_element.tag_name}, ID: {upload_element.get_attribute('id')}, Type: {upload_element.get_attribute('type')}")

        # Make sure the file exists and get its absolute path
        abs_file_path = os.path.abspath(file_path)
        if not os.path.exists(abs_file_path):
            print(f"Error: File does not exist at {abs_file_path}")
            return False

        print(f"Uploading file: {abs_file_path}")

        # Flag to track if file upload was successful
        upload_success = False

        # Method 1: Direct send_keys approach (most reliable for file inputs)
        try:
            # Make element visible and interactable if needed
            driver.execute_script("arguments[0].style.display = 'block'; arguments[0].style.visibility = 'visible';", upload_element)

            # Clear any existing value
            driver.execute_script("arguments[0].value = '';", upload_element)

            # Send the file path directly to the input element
            upload_element.send_keys(abs_file_path)
            print("File upload initiated using send_keys")

            # Verify the file was selected
            selected_file = upload_element.get_attribute('value')
            if selected_file:
                print(f"File selected: {selected_file}")
                upload_success = True
            else:
                print("Warning: No file appears to be selected after send_keys")

            # Give the browser time to process the file
            time.sleep(2)

        except Exception as e:
            print(f"Direct send_keys failed: {e}")

        # If Method 1 failed, try Method 2
        if not upload_success:
            # Method 2: PyAutoGUI approach as fallback
            try:
                # Make sure the element is visible and clickable
                driver.execute_script("arguments[0].style.display = 'block'; arguments[0].style.visibility = 'visible';", upload_element)

                # Click to open file dialog
                driver.execute_script("arguments[0].click();", upload_element)
                print("Clicked file input to open dialog")

                # Wait for dialog to appear
                time.sleep(3)

                # Type path and press Enter
                pyautogui.write(abs_file_path)
                time.sleep(1)
                pyautogui.press('enter')
                print("Entered file path using PyAutoGUI")

                # Verify file selection
                time.sleep(2)
                selected_file = upload_element.get_attribute('value')
                if selected_file:
                    print(f"File selected via PyAutoGUI: {selected_file}")
                    upload_success = True
                else:
                    print("Warning: No file appears to be selected after PyAutoGUI")

            except Exception as gui_error:
                print(f"PyAutoGUI approach failed: {gui_error}")

        # If both methods failed, return False
        if not upload_success:
            print("All file upload methods failed")
            return False

        # Step 4: Submit the form by clicking the Upload File button
        print("Step 4: Clicking 'Upload File' button...")
        try:
            # Try to find the exact button using the attributes provided
            submit_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[@type='button'][@class='btn btn-default btn-br-align'][@onclick='uploadfile()']"))
            )
            print("Found 'Upload File' button by exact attributes")
            submit_button.click()
            print("Clicked 'Upload File' button")
        except Exception as e:
            print(f"Error finding 'Upload File' button with exact attributes: {e}")

            # Try alternative methods
            try:
                # Try by button text
                button = driver.find_element(By.XPATH, "//button[contains(text(), 'Upload File')]")
                print("Found 'Upload File' button by text")
                button.click()
                print("Clicked 'Upload File' button by text")
            except Exception as e2:
                print(f"Error finding button by text: {e2}")

                try:
                    # Try by class and partial attributes
                    button = driver.find_element(By.CSS_SELECTOR, ".btn-default.btn-br-align")
                    print("Found 'Upload File' button by class")
                    button.click()
                    print("Clicked 'Upload File' button by class")
                except Exception as e3:
                    print(f"Error finding button by class: {e3}")

                    # Last resort: try JavaScript to trigger the uploadfile() function directly
                    try:
                        print("Attempting to call uploadfile() function directly")
                        driver.execute_script("uploadfile();")
                        print("Called uploadfile() function via JavaScript")
                    except Exception as e4:
                        print(f"All attempts to click 'Upload File' button failed: {e4}")
                        return False

        # Wait for processing
        print("Waiting for processing to complete...")
        time.sleep(5)

        # Check for success message or element
        try:
            success_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'alert-success')]"))
            )
            print(f"Upload successful! Success message: {success_element.text[:100]}")
            result = True
        except TimeoutException:
            print("No success message found, checking for error messages...")

            # Check for error messages
            try:
                error_element = driver.find_element(By.XPATH, "//div[contains(@class, 'alert-danger')]")
                error_message = error_element.text[:100]
                print(f"Found error message on page: {error_message}")
                result = False
            except NoSuchElementException:
                # Assume success when no explicit success or error message is found
                print("No explicit success message found - marking as SUCCESS")
                result = True

        # Clean up
        driver.quit()
        return result

    except Exception as e:
        print(f"An error occurred during the upload process: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def upload_to_mathews_panels(file_path, headless=True):
    """Upload the file to both Mathews postpanel websites.

    Args:
        file_path (str): Path to the file to upload
        headless (bool): Whether to run in headless mode

    Returns:
        dict: Results for each website (True if successful, False otherwise)
    """
    # Define the Mathews websites to upload to
    websites = [
        {
            "name": "MI Conferences Postpanel",
            "url": "https://miconferences.postpanel.info/",
            "username": "admin",
            "password": "Admin@890"
        },
        {
            "name": "Mathews Postpanel",
            "url": "https://mathews.postpanel.info/",
            "username": "admin",
            "password": "Admin@890"
        }
    ]

    results = {}

    # Upload to each website
    for site in websites:
        print("\n" + "=" * 60)
        print(f"UPLOADING TO: {site['name']} ({site['url']})")
        print("=" * 60)

        success = upload_file_to_website(
            file_path=file_path,
            website_url=site['url'],
            username=site['username'],
            password=site['password'],
            headless=headless
        )

        results[site['name']] = success

        print(f"Result for {site['name']}: {'SUCCESS' if success else 'FAILED'}")
        print("=" * 60 + "\n")

        # Wait between uploads
        print("Waiting 5 seconds before next upload...")
        time.sleep(5)

    return results

if __name__ == "__main__":
    # File path for the Mathews unsubscribers Excel file
    file_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\bounces\Processed_Data\BounceCollection_20250630_120359\Consolidated_Unsubscribes.xlsx"

    # Verify the file exists
    if os.path.exists(file_path):
        print(f"Found Mathews unsubscribers file to upload at: {file_path}")
    else:
        print(f"Error: File not found at {file_path}")
        print("Please ensure the file exists before running the upload.")
        exit(1)

    # Run in headless mode by default
    headless_mode = True

    # Upload the file to both Mathews panels
    print("Starting upload process to Mathews postpanel websites in headless mode...")
    results = upload_to_mathews_panels(file_path, headless=headless_mode)

    # Print summary
    print("\n" + "=" * 60)
    print("MATHEWS UNSUBSCRIBERS UPLOAD SUMMARY")
    print("=" * 60)

    all_success = True
    for site, success in results.items():
        status = "SUCCESS" if success else "FAILED"
        print(f"{site}: {status}")
        if not success:
            all_success = False

    print("=" * 60)
    if all_success:
        print("All Mathews unsubscribers uploads completed successfully!")
    else:
        print("Some uploads failed. Check the logs above for details.")
