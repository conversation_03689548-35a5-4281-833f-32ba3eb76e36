import os
os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs')
os.system('del *.csv')
from selenium import webdriver 
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By 
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

import pyautogui
import time
timestr = time.strftime("%d%m%Y")
options = webdriver.ChromeOptions()
options.add_experimental_option("excludeSwitches", ["enable-logging"])
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
prefs = {
"download.default_directory": r"H:\Master Bounces and Unsubs\Postpanel Unsubs",
"download.prompt_for_download": False,
"download.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(options)
driver.maximize_window()
#driver.set_window_size(1920, 1080)
driver.get("https://admin.magnusgroup.biz/admin-login.php")
driver.find_element(By.ID, "name").send_keys("<EMAIL>")
driver.find_element(By.ID, "password").send_keys("Magnus@123")
driver.find_element(By.XPATH, "//button[@id='login']").click()
pyautogui.keyDown('ctrl')
pyautogui.press('-')
pyautogui.press('-')
pyautogui.keyUp('ctrl')
driver.get('https://admin.magnusgroup.biz/unsubscribes_list.php')
driver.find_element(By.XPATH, "//span[normalize-space()='Download Unsubscribes']").click()
time.sleep(4)
driver.find_element(By.XPATH, "//span[@class='filter-option pull-left']").click()
driver.find_element(By.XPATH, "//span[normalize-space()='All']").click()
driver.find_element(By.XPATH, "//label[normalize-space()='Works:']").click()
driver.find_element(By.XPATH, "//input[@id='global_uns']").click()
driver.find_element(By.XPATH, "//button[@id='submit_key']").click()
time.sleep(100)
print('Downloaded Successfully!')