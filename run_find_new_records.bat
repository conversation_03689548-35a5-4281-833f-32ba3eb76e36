@echo off
title Find New Records - CSV Comparison
color 0B

echo.
echo ================================================================
echo                FIND NEW RECORDS UTILITY
echo ================================================================
echo.
echo This script will:
echo 1. Compare CSV files in main directory with CSV_Backup directory
echo 2. Remove duplicates from main files
echo 3. Find new records (not in backup)
echo 4. Save new records to 'new_records' directory
echo.
echo Source: H:\Master Bounces and Unsubs\Master Bounces and Unsubs
echo Backup: CSV_Backup folder
echo Output: new_records folder
echo.
pause

echo.
echo Starting new records processing...
echo.

REM Run the Python script
python find_new_records_simple.py

echo.
echo ================================================================
echo                PROCESSING COMPLETED
echo ================================================================
echo.
pause
