import os
import shutil
import glob
import pandas as pd
import numpy as np
import openpyxl

path = "H:\mailwizz\September Events 23-05-2023\materials\parts\port1" #input('Enter path of the directory to work on: ')
os.chdir(path)

filename = 'mat.xlsx' #input('Download settings filename: ')+'.xlsx'
dfws2 = pd.read_excel(filename, sheet_name="Worksheet 2")

pd.io.formats.excel.ExcelFormatter.header_style = None

filename = input('Enter filename: ')+'.xlsx'

workbook = openpyxl.load_workbook(filename)
writer = pd.ExcelWriter(filename, engine='openpyxl')
writer.book = workbook
writer.sheets = dict((ws.title, ws) for ws in workbook.worksheets)
dfws2.to_excel(writer, 'Worksheet 2', index=False)
writer.save()

print('Completed!')