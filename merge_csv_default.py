import os
import glob
import pandas as pd
import rich_progress
from datetime import datetime

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def find_common_prefix(filenames):
    """Find the common prefix/text string from a list of filenames."""
    if not filenames:
        return "merged"

    # Remove file extensions
    names_without_ext = [os.path.splitext(name)[0] for name in filenames]

    # Find common prefix
    if len(names_without_ext) == 1:
        return names_without_ext[0]

    # Find the longest common prefix
    common_prefix = ""
    min_length = min(len(name) for name in names_without_ext)

    for i in range(min_length):
        char = names_without_ext[0][i]
        if all(name[i] == char for name in names_without_ext):
            common_prefix += char
        else:
            break

    # Clean up the prefix (remove trailing underscores, hyphens, spaces)
    common_prefix = common_prefix.rstrip('_- ')

    # If no meaningful common prefix found, try to extract meaningful parts
    if len(common_prefix) < 3:
        # Look for common words or patterns
        words_sets = [set(name.replace('_', ' ').replace('-', ' ').split()) for name in names_without_ext]
        common_words = set.intersection(*words_sets) if words_sets else set()
        if common_words:
            # Sort words and join them
            common_prefix = '_'.join(sorted(common_words))
        else:
            common_prefix = "merged"

    return common_prefix if common_prefix else "merged"

def merge_csv_files():
    """Merge all CSV files in the current directory into a single file."""

    # Print welcome header
    print_header("CSV Files Merger")
    
    # Get the path from the user
    print_section("Input Path")
    path = input("Enter directory path: ").strip().strip('"\'')
    
    if not os.path.exists(path):
        rich_progress.print_status(f"Directory not found: {path}", "error")
        return
    
    os.chdir(path)
    rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")
    
    # Find all CSV files in the directory
    print_section("Finding CSV Files")
    csv_files = glob.glob('*.csv')
    if not csv_files:
        rich_progress.print_status("No CSV files found in the directory.", "error")
        return
    
    rich_progress.print_status(f"Found {len(csv_files)} CSV files:", "success")
    for i, file in enumerate(csv_files, 1):
        rich_progress.print_status(f"  {i}. {file}", "info")
    
    # Create merged directory if it doesn't exist
    print_section("Output Configuration")
    merged_dir = os.path.join(os.getcwd(), "merged")
    os.makedirs(merged_dir, exist_ok=True)
    rich_progress.print_status(f"Output directory: {merged_dir}", "info")

    # Generate default filename based on common text string in files
    common_text = find_common_prefix(csv_files)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    default_output = f"{common_text}_merged_{timestamp}.csv"

    rich_progress.print_status(f"Detected common text: '{common_text}'", "info")

    # Ask for output filename
    output_filename = input(f"Enter output filename (default: {default_output}): ").strip()
    if not output_filename:
        output_filename = default_output

    # Full path to output file in merged directory
    output_file = os.path.join(merged_dir, output_filename)
    
    # Initialize progress bar
    print_section("Processing Files")
    total_steps = len(csv_files) + 2  # +2 for combining and saving
    progress_bar, update_progress = rich_progress.create_progress_bar(
        total=total_steps,
        description="Merging CSV files",
        color_scheme="blue"
    )
    
    # Read and process each CSV file
    all_dataframes = []
    total_input_rows = 0
    
    for i, file in enumerate(csv_files):
        try:
            rich_progress.print_status(f"Reading {file}...", "info")
            df = pd.read_csv(file, encoding='utf-8-sig')
            rows = len(df)
            total_input_rows += rows
            all_dataframes.append(df)
            rich_progress.print_status(f"  - Read {rows} rows from {file}", "success")
            update_progress(1, f"Read {file}")
        except Exception as e:
            rich_progress.print_status(f"  - Error reading {file}: {str(e)}", "error")
            # Try alternative encodings
            try:
                df = pd.read_csv(file, encoding='latin-1')
                rows = len(df)
                total_input_rows += rows
                all_dataframes.append(df)
                rich_progress.print_status(f"  - Read {rows} rows from {file} (latin-1)", "success")
                update_progress(1, f"Read {file}")
            except Exception as e2:
                rich_progress.print_status(f"  - Failed to read {file}: {str(e2)}", "error")
                update_progress(1, f"Failed {file}")
    
    if not all_dataframes:
        rich_progress.print_status("No CSV files could be read successfully.", "error")
        progress_bar.stop()
        return
    
    # Combine all dataframes
    rich_progress.print_status("Combining all data...", "info")
    try:
        merged_df = pd.concat(all_dataframes, ignore_index=True)
        update_progress(1, "Combined data")
        rich_progress.print_status(f"Successfully combined {len(all_dataframes)} files", "success")
    except Exception as e:
        rich_progress.print_status(f"Error combining data: {str(e)}", "error")
        progress_bar.stop()
        return
    
    # Save the merged file
    rich_progress.print_status(f"Saving to {output_file}...", "info")
    try:
        merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        output_rows = len(merged_df)
        update_progress(1, "Saved file")
        rich_progress.print_status(f"Successfully saved {output_rows} rows to {output_file}", "success")
    except Exception as e:
        rich_progress.print_status(f"Error saving file: {str(e)}", "error")
        progress_bar.stop()
        return
    
    # Close progress bar
    progress_bar.stop()
    
    # Print completion summary
    print_header("Merge Completed Successfully!")
    rich_progress.print_status(f"Files processed: {len(csv_files)}", "success")
    rich_progress.print_status(f"Total input rows: {total_input_rows}", "info")
    rich_progress.print_status(f"Output rows: {len(merged_df)}", "info")
    rich_progress.print_status(f"Output file: {output_file}", "success")
    
    # Show column information
    print_section("Column Information")
    rich_progress.print_status(f"Columns in merged file: {len(merged_df.columns)}", "info")
    for col in merged_df.columns:
        rich_progress.print_status(f"  - {col}", "info")

if __name__ == "__main__":
    try:
        merge_csv_files()
    except KeyboardInterrupt:
        rich_progress.print_status("\nOperation cancelled by user.", "warning")
    except Exception as e:
        rich_progress.print_status(f"Unexpected error: {str(e)}", "error")
    
    print("\nPress Enter to exit...")
    input()
