import os
import csv
import sys
import argparse

def find_csv_files(directory):
    """Find all CSV files in the given directory."""
    csv_files = []
    if os.path.exists(directory):
        for file in os.listdir(directory):
            if file.lower().endswith('.csv'):
                csv_files.append(os.path.join(directory, file))
    return csv_files

def read_csv_manually(file_path):
    """Read a CSV file manually to extract emails."""
    emails = []
    status_column = None
    email_column = None

    # Try different encodings
    encodings = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                # Read the first few lines to detect headers
                header_line = None
                lines = []
                for i, line in enumerate(f):
                    lines.append(line)
                    if i > 20:  # Read at most 20 lines to find headers
                        break

                # Try to find the header line
                for i, line in enumerate(lines):
                    if 'email' in line.lower():
                        header_line = i
                        break

                if header_line is None:
                    continue  # Try next encoding if no header found

                # Parse the header to find email and status columns
                headers = [h.strip().lower() for h in lines[header_line].split(',')]

                for i, header in enumerate(headers):
                    if 'email' in header:
                        email_column = i
                    if 'status' in header:
                        status_column = i

                if email_column is None:
                    continue  # Try next encoding if no email column found

                # Now read the whole file and extract emails
                with open(file_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    for i, row in enumerate(reader):
                        if i <= header_line:  # Skip header rows
                            continue

                        if len(row) > email_column:
                            email = row[email_column].strip().strip('"\'')

                            # Check if we need to filter by status
                            if status_column is not None and len(row) > status_column:
                                status = row[status_column].lower()
                                # For mw folder, include both "unsubscribed" and "blacklisted" status
                                if "mw" in file_path.lower():
                                    if status == "unsubscribed" or status == "blacklisted":
                                        emails.append(email)
                                # For other folders, use partial match with 'unsub'
                                elif 'unsub' in status:
                                    emails.append(email)
                            else:
                                # For ee and self_managed folders, include all emails
                                emails.append(email)

                # If we found emails, return them
                if emails:
                    return emails

        except Exception as e:
            print(f"Error with encoding {encoding}: {e}")
            continue

    # If we couldn't read with any encoding, try a more basic approach
    try:
        with open(file_path, 'r', errors='ignore') as f:
            content = f.read()

        # Look for email patterns
        import re
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        found_emails = re.findall(email_pattern, content)

        if found_emails:
            return found_emails
    except Exception as e:
        print(f"Error with basic approach: {e}")

    return []

def process_ee_folder(file_path):
    """Process CSV files from the 'ee' folder."""
    try:
        emails = read_csv_manually(file_path)
        print(f"Processed 'ee' file: {os.path.basename(file_path)} - Found {len(emails)} unsubscribers")
        return emails
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def process_mw_folder(file_path):
    """Process CSV files from the 'mw' folder with status filter for 'unsubscribed' or 'blacklisted'."""
    try:
        emails = read_csv_manually(file_path)
        print(f"Processed 'mw' file: {os.path.basename(file_path)} - Found {len(emails)} unsubscribers with status 'unsubscribed' or 'blacklisted'")
        return emails
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def process_self_managed_folder(file_path):
    """Process CSV files from the 'self_managed' folder."""
    try:
        emails = read_csv_manually(file_path)
        print(f"Processed 'self_managed' file: {os.path.basename(file_path)} - Found {len(emails)} unsubscribers")
        return emails
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def main():
    parser = argparse.ArgumentParser(description='Consolidate unsubscribers from multiple CSV files.')
    parser.add_argument('--root_dir', default=r"H:\Master Bounces and Unsubs\Journal Unsubs",
                        help='Root directory containing the subfolders with CSV files')
    parser.add_argument('--master_file',
                        default=r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\Master_Journal_Unsubscribes.csv",
                        help='Master file to append the consolidated unsubscribers to')
    parser.add_argument('--append', action='store_true',
                        help='Append to master file if specified')
    args = parser.parse_args()

    root_dir = args.root_dir
    master_file = args.master_file
    append_to_master = args.append

    if not os.path.exists(root_dir):
        print(f"Error: Root directory '{root_dir}' does not exist.")
        sys.exit(1)

    # Define subdirectories
    ee_dir = os.path.join(root_dir, "ee")
    mw_dir = os.path.join(root_dir, "mw")
    self_managed_dir = os.path.join(root_dir, "self_managed")

    # Create output directory
    output_dir = os.path.join(root_dir, "consolidated")
    os.makedirs(output_dir, exist_ok=True)

    # Find all CSV files
    ee_files = find_csv_files(ee_dir)
    mw_files = find_csv_files(mw_dir)
    self_managed_files = find_csv_files(self_managed_dir)

    all_files = ee_files + mw_files + self_managed_files

    if not all_files:
        print("No CSV files found in the specified directories.")
        sys.exit(1)

    print(f"Found {len(all_files)} CSV files to process.")

    # Process all files and collect unsubscribers
    all_unsubscribers = []
    processed_files = 0
    failed_files = 0

    for file_path in all_files:
        try:
            print(f"Processing {os.path.basename(file_path)}...")
            if os.path.dirname(file_path) == ee_dir:
                unsubscribers = process_ee_folder(file_path)
                all_unsubscribers.extend(unsubscribers)
            elif os.path.dirname(file_path) == mw_dir:
                unsubscribers = process_mw_folder(file_path)
                all_unsubscribers.extend(unsubscribers)
            elif os.path.dirname(file_path) == self_managed_dir:
                unsubscribers = process_self_managed_folder(file_path)
                all_unsubscribers.extend(unsubscribers)
            processed_files += 1
        except Exception as e:
            print(f"Failed to process file {file_path}: {e}")
            failed_files += 1

    print(f"\nProcessing summary:")
    print(f"- Successfully processed: {processed_files} files")
    print(f"- Failed to process: {failed_files} files")

    if not all_unsubscribers:
        print("No unsubscribers found in any of the processed files.")
        sys.exit(1)

    # Remove duplicates and normalize emails
    unique_unsubscribers = list(set([email.strip().lower() for email in all_unsubscribers if email and isinstance(email, str)]))
    print(f"Total unique unsubscribers found: {len(unique_unsubscribers)}")

    # Save to CSV file
    output_file = os.path.join(output_dir, "consolidated_unsubscribers.csv")
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(['Email'])  # Write header
        for email in unique_unsubscribers:
            writer.writerow([email])

    print(f"Consolidated unsubscribers saved to: {output_file}")

    # Append to master file if requested
    if append_to_master:
        if not os.path.exists(master_file):
            print(f"Creating new master file: {master_file}")
            # Create the master file with the same content
            os.makedirs(os.path.dirname(master_file), exist_ok=True)
            with open(master_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(['Email'])  # Write header
                for email in unique_unsubscribers:
                    writer.writerow([email])
            print(f"Created new master file with {len(unique_unsubscribers)} unsubscribers")
        else:
            # Read existing master file to get current emails
            existing_emails = set()
            try:
                with open(master_file, 'r', encoding='utf-8-sig') as f:
                    reader = csv.reader(f)
                    header = next(reader, None)  # Skip header
                    email_col = 0  # Default to first column

                    # Find the email column if header exists
                    if header:
                        for i, col in enumerate(header):
                            if 'email' in col.lower():
                                email_col = i
                                break

                    # Read all existing emails
                    for row in reader:
                        if len(row) > email_col:
                            email = row[email_col].strip().lower()
                            if email:
                                existing_emails.add(email)

                print(f"Read {len(existing_emails)} existing emails from master file")

                # Find new emails to append
                new_emails = [email for email in unique_unsubscribers if email.lower() not in existing_emails]

                if new_emails:
                    # Append new emails to the master file
                    with open(master_file, 'a', newline='', encoding='utf-8-sig') as f:
                        writer = csv.writer(f)
                        for email in new_emails:
                            writer.writerow([email])

                    print(f"Appended {len(new_emails)} new unsubscribers to master file: {master_file}")
                else:
                    print("No new unsubscribers to append to master file")

            except Exception as e:
                print(f"Error processing master file: {e}")
                print("Creating backup of master file and writing new version")

                # Create backup of existing file
                import shutil
                backup_file = master_file + ".backup"
                shutil.copy2(master_file, backup_file)
                print(f"Backup created: {backup_file}")

                # Write new master file with combined emails
                all_emails = list(existing_emails) + unique_unsubscribers
                unique_all_emails = list(set([email.lower() for email in all_emails if email]))

                with open(master_file, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Email'])  # Write header
                    for email in unique_all_emails:
                        writer.writerow([email])

                print(f"Updated master file with {len(unique_all_emails)} total unsubscribers")

if __name__ == "__main__":
    main()
