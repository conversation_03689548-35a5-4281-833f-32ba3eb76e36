import csv
import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from prettytable import PrettyTable

# Configuration (move these to a config file or environment variables)
LOGIN_URL = "http://swa.dbms.org.in/"
USERNAME = "<EMAIL>"
PASSWORD = "Magnus@123"
CSV_FILE_PATH = r"C:\Users\<USER>\OneDrive\My Files\fid rbnames.csv"
UPLOAD_DIR = r"H:\Master Bounces and Unsubs\Replied Ext\Prev_Rep_bounces_csv"
WAIT_TIMEOUT = 20

def read_csv(file_path):
    """Reads Filter ID and Filter Name values from a CSV file."""
    fid_values = []
    rb_names = []
    try:
        with open(file_path, mode='r') as file:
            reader = csv.reader(file)
            next(reader)  # Skip the header row
            for row in reader:
                fid_values.append(row[0])
                rb_names.append(row[1])
        return fid_values, rb_names
    except FileNotFoundError:
        print(f"Error: CSV file not found at {file_path}")
        return None, None
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return None, None

def display_data_table(fid_values, rb_names):
    """Displays the Filter ID and Filter Name values in a table."""
    if not fid_values or not rb_names:
        return
    table = PrettyTable()
    table.field_names = ["Filter ID", "Filter Name"]
    for fid, rb_name in zip(fid_values, rb_names):
        table.add_row([fid, rb_name])
    print(table)

def setup_driver():
    """Sets up the Chrome driver with the necessary options."""
    options = Options()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    prefs = {
        "upload.default_directory": UPLOAD_DIR,
        "upload.prompt_for_download": False,
        "upload.directory_upgrade": True
    }
    options.add_experimental_option('prefs', prefs)
    return webdriver.Chrome(options=options)

def login(driver, url, username, password):
    """Logs into the website."""
    try:
        driver.get(url)
        driver.find_element(By.ID, "username").send_keys(username)
        driver.find_element(By.ID, "password").send_keys(password)
        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.element_to_be_clickable((By.ID, "btnSubmit"))
        )
        driver.find_element(By.ID, "btnSubmit").click()
        print("Logged in successfully.")
    except TimeoutException:
        print("Login failed: Login button not found.")
        raise
    except NoSuchElementException:
        print("Login failed: Could not find login elements.")
        raise
    except Exception as e:
        print(f"Login failed: {e}")
        raise

def upload_file(driver, fid, rb_name):
    """Uploads the CSV file for the given Filter ID and Filter Name."""
    try:
        upload_url = f"{LOGIN_URL}/upload_data_file.php?datafilter={fid}&datafiltername={rb_name}"
        driver.get(upload_url)

        file_input = driver.find_element(By.ID, 'up_file')
        file_path = os.path.join(UPLOAD_DIR, f"{rb_name}.csv")
        file_input.send_keys(file_path)

        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.element_to_be_clickable((By.ID, "duplicates"))
        )
        driver.find_element(By.ID, "duplicates").click()

        WebDriverWait(driver, WAIT_TIMEOUT).until(
            EC.element_to_be_clickable((By.ID, "submit_key"))
        )
        driver.find_element(By.ID, "submit_key").click()
        print(f'{rb_name} with fid={fid} DBMS Upload Completed!')
    except TimeoutException:
        print(f"Upload failed for {rb_name} with fid={fid}: Element not found.")
        raise
    except NoSuchElementException:
        print(f"Upload failed for {rb_name} with fid={fid}: Could not find upload elements.")
        raise
    except Exception as e:
        print(f"Upload failed for {rb_name} with fid={fid}: {e}")
        raise

def main():
    """Main function to run the script."""
    try:
        fid_values, rb_names = read_csv(CSV_FILE_PATH)
        if not fid_values or not rb_names:
            return
        if len(fid_values) != len(rb_names):
            raise ValueError("The length of fid_values and rb_names must be the same.")

        display_data_table(fid_values, rb_names)

        driver = setup_driver()
        driver.maximize_window()
        login(driver, LOGIN_URL, USERNAME, PASSWORD)

        for fid, rb_name in zip(fid_values, rb_names):
            upload_file(driver, fid, rb_name)

    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    main()
