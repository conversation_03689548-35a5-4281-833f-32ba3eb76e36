import os
import pandas as pd
import glob
import datetime
import re

def extract_segment(path):
    """
    Extracts the desired segment (e.g., "Conference Name 2023") from a path.

    Args:
        path: The path string.

    Returns:
        The extracted segment (string) or None if not found.
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"  # Matches alphanumeric, spaces, hyphens, and 4 digits
    match = re.search(pattern, path)
    return match.group(1) if match else None

def xlsx_to_csv(directory, csn):
    """
    Converts multiple XLSX files in a directory to a single CSV file.

    Args:
        directory: The directory containing the XLSX files.
        csn: The CSN (Conference Segment Name) for the output filename.
    """
    try:
        os.chdir(directory)
    except FileNotFoundError:
        print(f"Error: Directory not found: {directory}")
        return

    xlsx_files = glob.glob("*.xlsx")
    if not xlsx_files:
        print(f"No XLSX files found in: {directory}")
        return

    try:
        df_concat = pd.concat([pd.read_excel(f) for f in xlsx_files], ignore_index=True)
    except Exception as e:
        print(f"Error reading or concatenating XLSX files: {e}")
        return

    # Rename 'email' column to 'Email' if it exists
    if 'email' in df_concat.columns:
        df_concat.rename(columns={'email': 'Email'}, inplace=True)

    # Add 'Article Title' column if it doesn't exist
    if 'Article Title' not in df_concat.columns:
        df_concat['Article Title'] = 0

    # Remove duplicate emails
    df_unique = df_concat.drop_duplicates(subset='Email')

    now = datetime.datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    output_filename = f"{csn}_{timestamp}.csv"

    try:
        df_unique.to_csv(output_filename, encoding="utf-8-sig", index=False)
        print(f"Data saved to: {output_filename}")
        print(f"Total rows in combined file: {len(df_concat)}")
    except Exception as e:
        print(f"Error saving to CSV: {e}")

def main():
    """
    Main function to run the script.
    """
    directory = input("Enter the directory containing the XLSX files: ")
    csn = extract_segment(directory)

    if csn:
        print(f"Extracted segment (CSN): {csn}")
        xlsx_to_csv(directory, csn)
    else:
        print("Error: Desired segment not found in the path.")

if __name__ == "__main__":
    main()
