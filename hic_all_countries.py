##HIC All Countries

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# TLD to Country Name Mapping
tld_to_country = {
    # A
    '.ac': 'Ascension Island', '.ad': 'Andorra', '.ae': 'United Arab Emirates', '.af': 'Afghanistan',
    '.ag': 'Antigua and Barbuda', '.ai': 'Anguilla', '.al': 'Albania', '.am': 'Armenia',
    '.ao': 'Angola', '.aq': 'Antarctica', '.ar': 'Argentina', '.as': 'American Samoa',
    '.at': 'Austria', '.au': 'Australia', '.aw': 'Aruba', '.ax': 'Åland Islands', '.az': 'Azerbaijan',
    # B
    '.ba': 'Bosnia and Herzegovina', '.bb': 'Barbados', '.bd': 'Bangladesh', '.be': 'Belgium',
    '.bf': 'Burkina Faso', '.bg': 'Bulgaria', '.bh': 'Bahrain', '.bi': 'Burundi',
    '.bj': 'Benin', '.bm': 'Bermuda', '.bn': 'Brunei', '.bo': 'Bolivia',
    '.br': 'Brazil', '.bs': 'Bahamas', '.bt': 'Bhutan', '.bw': 'Botswana',
    '.by': 'Belarus', '.bz': 'Belize',
    # C
    '.ca': 'Canada', '.cc': 'Cocos Islands', '.cd': 'Democratic Republic of the Congo', '.cf': 'Central African Republic',
    '.cg': 'Republic of the Congo', '.ch': 'Switzerland', '.ci': 'Côte d\'Ivoire', '.ck': 'Cook Islands',
    '.cl': 'Chile', '.cm': 'Cameroon', '.cn': 'China', '.co': 'Colombia',
    '.cr': 'Costa Rica', '.cu': 'Cuba', '.cv': 'Cape Verde', '.cw': 'Curaçao',
    '.cx': 'Christmas Island', '.cy': 'Cyprus', '.cz': 'Czech Republic',
    # D
    '.de': 'Germany', '.dj': 'Djibouti', '.dk': 'Denmark', '.dm': 'Dominica',
    '.do': 'Dominican Republic', '.dz': 'Algeria',
    # E
    '.ec': 'Ecuador', '.ee': 'Estonia', '.eg': 'Egypt', '.er': 'Eritrea',
    '.es': 'Spain', '.et': 'Ethiopia', '.eu': 'European Union',
    # F
    '.fi': 'Finland', '.fj': 'Fiji', '.fk': 'Falkland Islands', '.fm': 'Micronesia',
    '.fo': 'Faroe Islands', '.fr': 'France',
    # G
    '.ga': 'Gabon', '.gd': 'Grenada', '.ge': 'Georgia', '.gf': 'French Guiana',
    '.gg': 'Guernsey', '.gh': 'Ghana', '.gi': 'Gibraltar', '.gl': 'Greenland',
    '.gm': 'Gambia', '.gn': 'Guinea', '.gp': 'Guadeloupe', '.gq': 'Equatorial Guinea',
    '.gr': 'Greece', '.gs': 'South Georgia and the South Sandwich Islands', '.gt': 'Guatemala', '.gu': 'Guam',
    '.gw': 'Guinea-Bissau', '.gy': 'Guyana',
    # H
    '.hk': 'Hong Kong', '.hm': 'Heard Island and McDonald Islands', '.hn': 'Honduras', '.hr': 'Croatia',
    '.ht': 'Haiti', '.hu': 'Hungary',
    # I
    '.id': 'Indonesia', '.ie': 'Ireland', '.il': 'Israel', '.im': 'Isle of Man',
    '.in': 'India', '.io': 'British Indian Ocean Territory', '.iq': 'Iraq', '.ir': 'Iran',
    '.is': 'Iceland', '.it': 'Italy',
    # J
    '.je': 'Jersey', '.jm': 'Jamaica', '.jo': 'Jordan', '.jp': 'Japan',
    # K
    '.ke': 'Kenya', '.kg': 'Kyrgyzstan', '.kh': 'Cambodia', '.ki': 'Kiribati',
    '.km': 'Comoros', '.kn': 'Saint Kitts and Nevis', '.kp': 'North Korea', '.kr': 'South Korea',
    '.kw': 'Kuwait', '.ky': 'Cayman Islands', '.kz': 'Kazakhstan',
    # L
    '.la': 'Laos', '.lb': 'Lebanon', '.lc': 'Saint Lucia', '.li': 'Liechtenstein',
    '.lk': 'Sri Lanka', '.lr': 'Liberia', '.ls': 'Lesotho', '.lt': 'Lithuania',
    '.lu': 'Luxembourg', '.lv': 'Latvia', '.ly': 'Libya',
    # M
    '.ma': 'Morocco', '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro',
    '.mg': 'Madagascar', '.mh': 'Marshall Islands', '.mk': 'North Macedonia', '.ml': 'Mali',
    '.mm': 'Myanmar', '.mn': 'Mongolia', '.mo': 'Macao', '.mp': 'Northern Mariana Islands',
    '.mq': 'Martinique', '.mr': 'Mauritania', '.ms': 'Montserrat', '.mt': 'Malta',
    '.mu': 'Mauritius', '.mv': 'Maldives', '.mw': 'Malawi', '.mx': 'Mexico',
    '.my': 'Malaysia', '.mz': 'Mozambique',
    # N
    '.na': 'Namibia', '.nc': 'New Caledonia', '.ne': 'Niger', '.nf': 'Norfolk Island',
    '.ng': 'Nigeria', '.ni': 'Nicaragua', '.nl': 'Netherlands', '.no': 'Norway',
    '.np': 'Nepal', '.nr': 'Nauru', '.nu': 'Niue', '.nz': 'New Zealand',
    # O
    '.om': 'Oman',
    # P
    '.pa': 'Panama', '.pe': 'Peru', '.pf': 'French Polynesia', '.pg': 'Papua New Guinea',
    '.ph': 'Philippines', '.pk': 'Pakistan', '.pl': 'Poland', '.pm': 'Saint Pierre and Miquelon',
    '.pn': 'Pitcairn Islands', '.pr': 'Puerto Rico', '.ps': 'Palestine', '.pt': 'Portugal',
    '.pw': 'Palau', '.py': 'Paraguay',
    # Q
    '.qa': 'Qatar',
    # R
    '.re': 'Réunion', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia', '.rw': 'Rwanda',
    # S
    '.sa': 'Saudi Arabia', '.sb': 'Solomon Islands', '.sc': 'Seychelles', '.sd': 'Sudan',
    '.se': 'Sweden', '.sg': 'Singapore', '.sh': 'Saint Helena', '.si': 'Slovenia',
    '.sk': 'Slovakia', '.sl': 'Sierra Leone', '.sm': 'San Marino', '.sn': 'Senegal',
    '.so': 'Somalia', '.sr': 'Suriname', '.ss': 'South Sudan', '.st': 'São Tomé and Príncipe',
    '.su': 'Soviet Union', '.sv': 'El Salvador', '.sx': 'Sint Maarten', '.sy': 'Syria', '.sz': 'Eswatini',
    # T
    '.tc': 'Turks and Caicos Islands', '.td': 'Chad', '.tf': 'French Southern Territories', '.tg': 'Togo',
    '.th': 'Thailand', '.tj': 'Tajikistan', '.tk': 'Tokelau', '.tl': 'East Timor',
    '.tm': 'Turkmenistan', '.tn': 'Tunisia', '.to': 'Tonga', '.tr': 'Turkey',
    '.tt': 'Trinidad and Tobago', '.tv': 'Tuvalu', '.tw': 'Taiwan', '.tz': 'Tanzania',
    # U
    '.ua': 'Ukraine', '.ug': 'Uganda', '.uk': 'United Kingdom', '.us': 'United States',
    '.uy': 'Uruguay', '.uz': 'Uzbekistan',
    # V
    '.va': 'Vatican City', '.vc': 'Saint Vincent and the Grenadines', '.ve': 'Venezuela', '.vg': 'British Virgin Islands',
    '.vi': 'U.S. Virgin Islands', '.vn': 'Vietnam', '.vu': 'Vanuatu',
    # W
    '.wf': 'Wallis and Futuna', '.ws': 'Samoa',
    # Y
    '.ye': 'Yemen', '.yt': 'Mayotte',
    # Z
    '.za': 'South Africa', '.zm': 'Zambia', '.zw': 'Zimbabwe',
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    # If no match found, return 'Other'
    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("HIC Email Domain Filtering Tool - All Countries")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    csn = match.group(1)
    rich_progress.print_status(f"Found segment: {csn}", "success")
else:
    rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
    # Prompt for manual input
    csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
    if csn.strip():
        rich_progress.print_status(f"Using manually entered segment: {csn}", "info")
    else:
        rich_progress.print_status("No segment name provided. Exiting.", "error")
        exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file, low_memory=False)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Define all country code TLDs
print_section("Defining Country TLDs")
rich_progress.print_status("Setting up country code TLDs for filtering...", "info")

# All country code TLDs
country_tlds = [
    # A
    '.ac', '.ad', '.ae', '.af', '.ag', '.ai', '.al', '.am', '.ao', '.aq', '.ar', '.as', '.at', '.au', '.aw', '.ax', '.az',
    # B
    '.ba', '.bb', '.bd', '.be', '.bf', '.bg', '.bh', '.bi', '.bj', '.bm', '.bn', '.bo', '.br', '.bs', '.bt', '.bw', '.by', '.bz',
    # C
    '.ca', '.cc', '.cd', '.cf', '.cg', '.ch', '.ci', '.ck', '.cl', '.cm', '.cn', '.co', '.cr', '.cu', '.cv', '.cw', '.cx', '.cy', '.cz',
    # D
    '.de', '.dj', '.dk', '.dm', '.do', '.dz',
    # E
    '.ec', '.ee', '.eg', '.er', '.es', '.et', '.eu',
    # F
    '.fi', '.fj', '.fk', '.fm', '.fo', '.fr',
    # G
    '.ga', '.gd', '.ge', '.gf', '.gg', '.gh', '.gi', '.gl', '.gm', '.gn', '.gp', '.gq', '.gr', '.gs', '.gt', '.gu', '.gw', '.gy',
    # H
    '.hk', '.hm', '.hn', '.hr', '.ht', '.hu',
    # I
    '.id', '.ie', '.il', '.im', '.in', '.io', '.iq', '.ir', '.is', '.it',
    # J
    '.je', '.jm', '.jo', '.jp',
    # K
    '.ke', '.kg', '.kh', '.ki', '.km', '.kn', '.kp', '.kr', '.kw', '.ky', '.kz',
    # L
    '.la', '.lb', '.lc', '.li', '.lk', '.lr', '.ls', '.lt', '.lu', '.lv', '.ly',
    # M
    '.ma', '.mc', '.md', '.me', '.mg', '.mh', '.mk', '.ml', '.mm', '.mn', '.mo', '.mp', '.mq', '.mr', '.ms', '.mt', '.mu', '.mv', '.mw', '.mx', '.my', '.mz',
    # N
    '.na', '.nc', '.ne', '.nf', '.ng', '.ni', '.nl', '.no', '.np', '.nr', '.nu', '.nz',
    # O
    '.om',
    # P
    '.pa', '.pe', '.pf', '.pg', '.ph', '.pk', '.pl', '.pm', '.pn', '.pr', '.ps', '.pt', '.pw', '.py',
    # Q
    '.qa',
    # R
    '.re', '.ro', '.rs', '.ru', '.rw',
    # S
    '.sa', '.sb', '.sc', '.sd', '.se', '.sg', '.sh', '.si', '.sk', '.sl', '.sm', '.sn', '.so', '.sr', '.ss', '.st', '.su', '.sv', '.sx', '.sy', '.sz',
    # T
    '.tc', '.td', '.tf', '.tg', '.th', '.tj', '.tk', '.tl', '.tm', '.tn', '.to', '.tr', '.tt', '.tv', '.tw', '.tz',
    # U
    '.ua', '.ug', '.uk', '.us', '.uy', '.uz',
    # V
    '.va', '.vc', '.ve', '.vg', '.vi', '.vn', '.vu',
    # W
    '.wf', '.ws',
    # Y
    '.ye', '.yt',
    # Z
    '.za', '.zm', '.zw',
    # Special domains
    '.edu', '.gov', '.mil'
]



# Create a dictionary to store all country dataframes
country_dfs = {}

# Print section for filtering results
print_section("Filtering by Country TLDs")
rich_progress.print_status("Processing all country TLDs...", "info")

# Create a progress bar for filtering
filter_bar, update_filter = rich_progress.create_progress_bar(
    total=len(country_tlds),
    description="Filtering by TLDs",
    color_scheme="green"
)

# Filter for each country TLD
for tld in country_tlds:
    tld_clean = tld.replace('.', '')  # Remove dot for variable naming
    country_dfs[tld_clean] = d2_EVENT[d2_EVENT["Email"].str.endswith(tld, na=False)]
    update_filter(1, f"Filtered {tld}")

# Stop the progress bar
filter_bar.stop()

# Create continent-based filters
print_section("Creating Continent Filters")

# Define continents and their countries
continents = {
    'Europe': ['.al', '.ad', '.at', '.by', '.be', '.ba', '.bg', '.hr', '.cy', '.cz', '.dk', '.ee', '.fi', '.fr', '.de', '.gr', '.hu', '.is', '.ie', '.it', '.lv', '.li', '.lt', '.lu', '.mt', '.md', '.mc', '.me', '.nl', '.mk', '.no', '.pl', '.pt', '.ro', '.ru', '.sm', '.rs', '.sk', '.si', '.es', '.se', '.ch', '.tr', '.ua', '.uk', '.va', '.eu'],
    'Asia': ['.af', '.am', '.az', '.bh', '.bd', '.bt', '.bn', '.kh', '.cn', '.cy', '.ge', '.in', '.id', '.ir', '.iq', '.il', '.jp', '.jo', '.kz', '.kw', '.kg', '.la', '.lb', '.my', '.mv', '.mn', '.mm', '.np', '.kp', '.om', '.pk', '.ps', '.ph', '.qa', '.sa', '.sg', '.kr', '.lk', '.sy', '.tw', '.tj', '.th', '.tl', '.tr', '.tm', '.ae', '.uz', '.vn', '.ye'],
    'Africa': ['.dz', '.ao', '.bj', '.bw', '.bf', '.bi', '.cm', '.cv', '.cf', '.td', '.km', '.cd', '.cg', '.ci', '.dj', '.eg', '.gq', '.er', '.et', '.ga', '.gm', '.gh', '.gn', '.gw', '.ke', '.ls', '.lr', '.ly', '.mg', '.mw', '.ml', '.mr', '.mu', '.ma', '.mz', '.na', '.ne', '.ng', '.rw', '.st', '.sn', '.sc', '.sl', '.so', '.za', '.ss', '.sd', '.sz', '.tz', '.tg', '.tn', '.ug', '.zm', '.zw'],
    'North_America': ['.ca', '.us', '.mx', '.bz', '.cr', '.sv', '.gt', '.hn', '.ni', '.pa', '.ag', '.bs', '.bb', '.cu', '.dm', '.do', '.gd', '.ht', '.jm', '.kn', '.lc', '.vc', '.tt', '.gl'],
    'South_America': ['.ar', '.bo', '.br', '.cl', '.co', '.ec', '.gy', '.py', '.pe', '.sr', '.uy', '.ve'],
    'Oceania': ['.au', '.nz', '.fj', '.pg', '.sb', '.vu', '.fm', '.nr', '.pw', '.ws', '.to', '.tv', '.ki', '.mh'],
    'Antarctica': ['.aq']
}

# Create continent dataframes
continent_dfs = {}
for continent, tlds in continents.items():
    # Create a mask for all TLDs in this continent
    mask = pd.Series(False, index=d2_EVENT.index)
    for tld in tlds:
        mask = mask | d2_EVENT["Email"].str.endswith(tld, na=False)
    continent_dfs[continent] = d2_EVENT[mask]
    rich_progress.print_status(f"Created {continent} filter with {len(continent_dfs[continent])} records", "info")

# Create special filters
print_section("Creating Special Filters")

# 1st Priority Countries Filter (as requested by user)
print_section("Creating 1st Priority Countries Filter")
rich_progress.print_status("Filtering data for 1st priority countries...", "info")

# Define 1st priority countries with their TLDs and special domains
first_priority_filter = pd.Series(False, index=d2_EVENT.index)

# Japan
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".jp", na=False)

# China (including Chinese domains)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".cn", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("163.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("qq.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("126.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("sina.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("sohu.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("tom.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("aliyun.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("21cn.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("baidu.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("yeah.net", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("sogou.com", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("163.net", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("sina.net", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith("chinaren.com", na=False)

# Taiwan
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".tw", na=False)

# South Korea
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".kr", na=False)

# Hong Kong
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".hk", na=False)

# United States
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".us", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".edu", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".gov", na=False)
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".mil", na=False)

# Canada
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".ca", na=False)

# United Kingdom
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".uk", na=False)

# Germany
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".de", na=False)

# France
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".fr", na=False)

# Italy
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".it", na=False)

# Spain
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".es", na=False)

# Netherlands
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".nl", na=False)

# Sweden
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".se", na=False)

# Norway
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".no", na=False)

# Denmark
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".dk", na=False)

# Finland
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".fi", na=False)

# Poland
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".pl", na=False)

# Romania
first_priority_filter = first_priority_filter | d2_EVENT["Email"].str.endswith(".ro", na=False)

# Create the 1st priority dataframe
df_1st_priority = d2_EVENT[first_priority_filter]
rich_progress.print_status(f"1st Priority Countries: {len(df_1st_priority)} records", "success")

# 2nd Priority Countries Filter (as requested by user)
print_section("Creating 2nd Priority Countries Filter")
rich_progress.print_status("Filtering data for 2nd priority countries...", "info")

# Define 2nd priority countries with their TLDs
second_priority_filter = pd.Series(False, index=d2_EVENT.index)

# Australia
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".au", na=False)

# New Zealand
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".nz", na=False)

# Switzerland
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".ch", na=False)

# Portugal
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".pt", na=False)

# Greece
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".gr", na=False)

# Austria
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".at", na=False)

# Belgium
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".be", na=False)

# Luxembourg
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".lu", na=False)

# Ireland
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".ie", na=False)

# Czech Republic
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".cz", na=False)

# Estonia
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".ee", na=False)

# Hungary
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".hu", na=False)

# Cyprus
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".cy", na=False)

# Croatia
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".hr", na=False)

# Singapore
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".sg", na=False)

# Georgia
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".ge", na=False)

# Albania
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".al", na=False)

# Brazil
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".br", na=False)

# Morocco
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".ma", na=False)

# Tunisia
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".tn", na=False)

# Uzbekistan
second_priority_filter = second_priority_filter | d2_EVENT["Email"].str.endswith(".uz", na=False)

# Create the 2nd priority dataframe
df_2nd_priority = d2_EVENT[second_priority_filter]
rich_progress.print_status(f"2nd Priority Countries: {len(df_2nd_priority)} records", "success")

# 3rd Priority Countries Filter (as requested by user)
print_section("Creating 3rd Priority Countries Filter")
rich_progress.print_status("Filtering data for 3rd priority countries...", "info")

# Define 3rd priority countries with their TLDs
third_priority_filter = pd.Series(False, index=d2_EVENT.index)

# United Arab Emirates
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".ae", na=False)

# Australia
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".au", na=False)

# New Zealand
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".nz", na=False)

# Switzerland
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".ch", na=False)

# Portugal
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".pt", na=False)

# Greece
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".gr", na=False)

# Austria
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".at", na=False)

# Belgium
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".be", na=False)

# Luxembourg
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".lu", na=False)

# Ireland
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".ie", na=False)

# Czech Republic
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".cz", na=False)

# Estonia
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".ee", na=False)

# Hungary
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".hu", na=False)

# Cyprus
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".cy", na=False)

# Croatia
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".hr", na=False)

# Singapore
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".sg", na=False)

# Georgia
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".ge", na=False)

# Albania
third_priority_filter = third_priority_filter | d2_EVENT["Email"].str.endswith(".al", na=False)

# Create the 3rd priority dataframe
df_3rd_priority = d2_EVENT[third_priority_filter]
rich_progress.print_status(f"3rd Priority Countries: {len(df_3rd_priority)} records", "success")



# High priority domains (original filter)
df_hic = d2_EVENT[d2_EVENT["Email"].str.endswith(".edu", na=False) |
                d2_EVENT["Email"].str.endswith(".mil", na=False) |
                d2_EVENT["Email"].str.endswith(".gov", na=False) |
                d2_EVENT["Email"].str.endswith(".pl", na=False) |
                d2_EVENT["Email"].str.endswith(".it", na=False) |
                d2_EVENT["Email"].str.endswith(".au", na=False) |
                d2_EVENT["Email"].str.endswith(".at", na=False) |
                d2_EVENT["Email"].str.endswith(".be", na=False) |
                d2_EVENT["Email"].str.endswith(".ca", na=False) |
                d2_EVENT["Email"].str.endswith(".dk", na=False) |
                d2_EVENT["Email"].str.endswith(".uk", na=False) |
                d2_EVENT["Email"].str.endswith(".eu", na=False) |
                d2_EVENT["Email"].str.endswith(".fi", na=False) |
                d2_EVENT["Email"].str.endswith(".fr", na=False) |
                d2_EVENT["Email"].str.endswith(".ge", na=False) |
                d2_EVENT["Email"].str.endswith(".gr", na=False) |
                d2_EVENT["Email"].str.endswith(".nz", na=False) |
                d2_EVENT["Email"].str.endswith(".nl", na=False) |
                d2_EVENT["Email"].str.endswith(".no", na=False) |
                d2_EVENT["Email"].str.endswith(".se", na=False) |
                d2_EVENT["Email"].str.endswith(".ch", na=False) |
                d2_EVENT["Email"].str.endswith(".ae", na=False)]

# Chinese domains
df_hiccn = d2_EVENT[d2_EVENT["Email"].str.endswith("163.com", na=False) |
                d2_EVENT["Email"].str.endswith("qq.com", na=False) |
                d2_EVENT["Email"].str.endswith("126.com", na=False) |
                d2_EVENT["Email"].str.endswith("sina.com", na=False) |
                d2_EVENT["Email"].str.endswith("sohu.com", na=False) |
                d2_EVENT["Email"].str.endswith("tom.com", na=False) |
                d2_EVENT["Email"].str.endswith("aliyun.com", na=False) |
                d2_EVENT["Email"].str.endswith("21cn.com", na=False) |
                d2_EVENT["Email"].str.endswith("baidu.com", na=False) |
                d2_EVENT["Email"].str.endswith(".cn", na=False) |
                d2_EVENT["Email"].str.endswith("yeah.net", na=False) |
                d2_EVENT["Email"].str.endswith("sogou.com", na=False) |
                d2_EVENT["Email"].str.endswith("163.net", na=False) |
                d2_EVENT["Email"].str.endswith("sina.net", na=False) |
                d2_EVENT["Email"].str.endswith("chinaren.com", na=False)]

# Education, Academic and Research domains
print_section("Creating Education, Academic and Research Filters")

# Define comprehensive education-related patterns
education_patterns = [
    ".edu",      # US education domains
    ".ac.",      # Academic domains (e.g., .ac.uk, .ac.in, .ac.za)
    "university", "univ", "college", "school", "institute", "institut",
    "research", "academy", "academic", "scholar", "student",
    "campus", "faculty", "dept", "department"
]

# Define specific academic domains
academic_domains = [
    ".edu", ".ac.uk", ".ac.in", ".ac.za", ".ac.jp", ".ac.kr", ".ac.cn",
    ".ac.th", ".ac.id", ".ac.my", ".ac.sg", ".ac.nz", ".ac.au",
    ".edu.au", ".edu.sg", ".edu.my", ".edu.ph", ".edu.tw", ".edu.hk",
    ".edu.cn", ".edu.in", ".edu.pk", ".edu.bd", ".edu.lk", ".edu.np",
    ".edu.br", ".edu.ar", ".edu.mx", ".edu.co", ".edu.pe", ".edu.cl",
    ".edu.eg", ".edu.sa", ".edu.ae", ".edu.qa", ".edu.kw", ".edu.jo",
    ".edu.tr", ".edu.pl", ".edu.ro", ".edu.gr", ".edu.it", ".edu.es",
    ".uni-", "university.", "college.", "institute.", "research."
]

# Create comprehensive education filter
education_filter = pd.Series(False, index=d2_EVENT.index)

# Filter by .edu domains
education_filter = education_filter | d2_EVENT["Email"].str.contains(".edu", na=False)

# Filter by .ac. domains (academic)
education_filter = education_filter | d2_EVENT["Email"].str.contains(".ac.", na=False)

# Filter by university/college/institute patterns
for pattern in education_patterns:
    education_filter = education_filter | d2_EVENT["Email"].str.contains(pattern, case=False, na=False)

# Filter by specific academic domains
for domain in academic_domains:
    education_filter = education_filter | d2_EVENT["Email"].str.contains(domain, case=False, na=False)

df_education_comprehensive = d2_EVENT[education_filter]

# Also keep the original simple .edu filter for backward compatibility
df_hicedu = d2_EVENT[d2_EVENT["Email"].str.contains(".edu", na=False)]

rich_progress.print_status(f"Education/Academic/Research domains: {len(df_education_comprehensive)} records", "success")
rich_progress.print_status(f"Simple .edu domains: {len(df_hicedu)} records", "info")

# Print counts with consistent formatting
print_section("Filtering Results")
rich_progress.print_status("Domain filtering complete. Results:", "success")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Category':<20} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Print counts for special filters
rich_progress.print_status(f"{'1st Priority':<20} {len(df_1st_priority):>10}", "info")
rich_progress.print_status(f"{'2nd Priority':<20} {len(df_2nd_priority):>10}", "info")
rich_progress.print_status(f"{'3rd Priority':<20} {len(df_3rd_priority):>10}", "info")
rich_progress.print_status(f"{'High Priority':<20} {len(df_hic):>10}", "info")
rich_progress.print_status(f"{'China':<20} {len(df_hiccn):>10}", "info")
rich_progress.print_status(f"{'Education (All)':<20} {len(df_education_comprehensive):>10}", "info")
rich_progress.print_status(f"{'Education (.edu)':<20} {len(df_hicedu):>10}", "info")

# Print counts for continents
for continent, df in continent_dfs.items():
    rich_progress.print_status(f"{continent:<20} {len(df):>10}", "info")

# Print counts for top 10 countries by number of records
print_section("Top 10 Countries")
country_counts = {tld.replace('.', ''): len(df) for tld, df in country_dfs.items() if len(df) > 0}
top_countries = sorted(country_counts.items(), key=lambda x: x[1], reverse=True)[:10]

for country, count in top_countries:
    rich_progress.print_status(f"{country:<20} {count:>10}", "info")

rich_progress.print_status("-" * 40, "info")

# Print country distribution from the Country column
print_section("Country Distribution (from Country Column)")
country_distribution = d2_EVENT['Country'].value_counts()
rich_progress.print_status(f"Total unique countries identified: {len(country_distribution)}", "info")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Country':<30} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Show top 15 countries by count
for country, count in country_distribution.head(15).items():
    rich_progress.print_status(f"{country:<30} {count:>10}", "info")

rich_progress.print_status("-" * 40, "info")

# Define World Bank income categories (as of 2024-2025)
print_section("Defining World Bank Income Categories")

# High-income economies ($14,006 or more)
high_income_tlds = [
    '.ae', '.ag', '.ai', '.an', '.aq', '.ar', '.at', '.au', '.aw', '.bb', '.be', '.bh', '.bm', '.bn', '.bs',
    '.ca', '.ch', '.cl', '.cy', '.cz', '.de', '.dk', '.ee', '.es', '.fi', '.fr', '.gb', '.gd', '.gi', '.gl',
    '.gr', '.hk', '.hr', '.hu', '.ie', '.il', '.im', '.is', '.it', '.je', '.jp', '.kn', '.kr', '.kw', '.ky',
    '.li', '.lt', '.lu', '.lv', '.mc', '.mt', '.mu', '.mx', '.my', '.nl', '.no', '.nz', '.om', '.pa', '.pl',
    '.pr', '.pt', '.qa', '.ro', '.ru', '.sa', '.se', '.sg', '.si', '.sk', '.sm', '.tc', '.tt', '.tw', '.uk',
    '.us', '.uy', '.va', '.vg', '.vi'
]

# Upper-middle-income economies ($4,516 to $14,005)
upper_middle_income_tlds = [
    '.al', '.am', '.az', '.ba', '.bg', '.bz', '.cn', '.co', '.cr', '.cu', '.do', '.dz', '.ec', '.fj', '.ga',
    '.ge', '.gq', '.gt', '.gy', '.id', '.ir', '.jm', '.jo', '.kz', '.lb', '.ly', '.md', '.me', '.mk', '.mn',
    '.mv', '.pe', '.py', '.rs', '.sr', '.th', '.tj', '.tm', '.tr', '.ua', '.ve', '.za'
]

# Lower-middle-income economies ($1,146 to $4,515)
lower_middle_income_tlds = [
    '.ao', '.bd', '.bj', '.bo', '.bt', '.ci', '.cm', '.cv', '.dj', '.eg', '.fm', '.gh', '.gm', '.gn', '.hn',
    '.ht', '.in', '.ke', '.kg', '.kh', '.ki', '.km', '.la', '.lk', '.ls', '.ma', '.mh', '.mm', '.mr', '.na',
    '.ng', '.ni', '.np', '.nr', '.pk', '.pg', '.ph', '.ps', '.pw', '.sb', '.sn', '.so', '.st', '.sv', '.sz',
    '.tl', '.tn', '.tv', '.tz', '.ug', '.uz', '.vu', '.ws', '.zm', '.zw'
]

# Low-income economies ($1,145 or less)
low_income_tlds = [
    '.af', '.bi', '.bf', '.cd', '.cf', '.er', '.et', '.gw', '.kp', '.lr', '.mg', '.ml', '.mw', '.mz', '.ne',
    '.rw', '.sd', '.sl', '.ss', '.sy', '.td', '.tg', '.ye'
]

# Create filters for each income category
print_section("Filtering by Income Categories")

# Create a progress bar for filtering
income_filter_bar, update_income_filter = rich_progress.create_progress_bar(
    total=4,  # 4 income categories
    description="Filtering by income categories",
    color_scheme="purple"
)

# High-income filter
high_income_filter = pd.Series(False, index=d2_EVENT.index)
for tld in high_income_tlds:
    high_income_filter = high_income_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_high_income = d2_EVENT[high_income_filter]
update_income_filter(1, f"Filtered high-income countries ({len(df_high_income)} records)")

# Upper-middle-income filter
upper_middle_filter = pd.Series(False, index=d2_EVENT.index)
for tld in upper_middle_income_tlds:
    upper_middle_filter = upper_middle_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_upper_middle = d2_EVENT[upper_middle_filter]
update_income_filter(1, f"Filtered upper-middle-income countries ({len(df_upper_middle)} records)")

# Lower-middle-income filter
lower_middle_filter = pd.Series(False, index=d2_EVENT.index)
for tld in lower_middle_income_tlds:
    lower_middle_filter = lower_middle_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_lower_middle = d2_EVENT[lower_middle_filter]
update_income_filter(1, f"Filtered lower-middle-income countries ({len(df_lower_middle)} records)")

# Low-income filter
low_income_filter = pd.Series(False, index=d2_EVENT.index)
for tld in low_income_tlds:
    low_income_filter = low_income_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_low_income = d2_EVENT[low_income_filter]
update_income_filter(1, f"Filtered low-income countries ({len(df_low_income)} records)")

# Stop the progress bar
income_filter_bar.stop()

# Print income category results
print_section("Income Category Results")
rich_progress.print_status("Income-based filtering complete. Results:", "success")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Income Category':<25} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'High Income':<25} {len(df_high_income):>10}", "info")
rich_progress.print_status(f"{'Upper-Middle Income':<25} {len(df_upper_middle):>10}", "info")
rich_progress.print_status(f"{'Lower-Middle Income':<25} {len(df_lower_middle):>10}", "info")
rich_progress.print_status(f"{'Low Income':<25} {len(df_low_income):>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Create "Others" filter for data that doesn't belong to any category
print_section("Creating Others Filter")
rich_progress.print_status("Filtering data for others category...", "info")

# Combine all existing filters to identify what should NOT be in Others
all_existing_filters = pd.Series(False, index=d2_EVENT.index)

# Add priority filters
all_existing_filters = all_existing_filters | first_priority_filter
all_existing_filters = all_existing_filters | second_priority_filter
all_existing_filters = all_existing_filters | third_priority_filter

# Add income-based filters
all_existing_filters = all_existing_filters | high_income_filter
all_existing_filters = all_existing_filters | upper_middle_filter
all_existing_filters = all_existing_filters | lower_middle_filter
all_existing_filters = all_existing_filters | low_income_filter

# Add education filter
all_existing_filters = all_existing_filters | education_filter

# Add continent filters
for continent, tlds in continents.items():
    continent_mask = pd.Series(False, index=d2_EVENT.index)
    for tld in tlds:
        continent_mask = continent_mask | d2_EVENT["Email"].str.endswith(tld, na=False)
    all_existing_filters = all_existing_filters | continent_mask

# Add country filters (all country TLDs)
for tld in country_tlds:
    all_existing_filters = all_existing_filters | d2_EVENT["Email"].str.endswith(tld, na=False)

# Add Chinese domains
chinese_mask = pd.Series(False, index=d2_EVENT.index)
for domain, country in chinese_domains.items():
    chinese_mask = chinese_mask | d2_EVENT["Email"].str.endswith(domain, na=False)
all_existing_filters = all_existing_filters | chinese_mask

# Create Others filter (everything NOT in existing categories)
others_filter = ~all_existing_filters
df_others = d2_EVENT[others_filter]
rich_progress.print_status(f"Others category: {len(df_others)} records", "success")

# Create output directories
print_section("Creating Output Directories")
priority_dir = os.path.join(os.getcwd(), "priority")
continent_dir = os.path.join(os.getcwd(), "continent")
countries_dir = os.path.join(os.getcwd(), "countries")
income_dir = os.path.join(os.getcwd(), "income-wise")
education_dir = os.path.join(os.getcwd(), "education")
others_dir = os.path.join(os.getcwd(), "others")

# Create all directories
os.makedirs(priority_dir, exist_ok=True)
os.makedirs(continent_dir, exist_ok=True)
os.makedirs(countries_dir, exist_ok=True)
os.makedirs(income_dir, exist_ok=True)
os.makedirs(education_dir, exist_ok=True)
os.makedirs(others_dir, exist_ok=True)

rich_progress.print_status(f"Created priority directory: {priority_dir}", "info")
rich_progress.print_status(f"Created continent directory: {continent_dir}", "info")
rich_progress.print_status(f"Created countries directory: {countries_dir}", "info")
rich_progress.print_status(f"Created income-wise directory: {income_dir}", "info")
rich_progress.print_status(f"Created education directory: {education_dir}", "info")
rich_progress.print_status(f"Created Others directory: {others_dir}", "info")

print_section("Organizing Output Files")

# Define files to save
files_to_save = []

# Add income-wise filters (requested by user)
files_to_save.append((df_high_income, "high_income.csv", "High Income", income_dir))
files_to_save.append((df_upper_middle, "upper_middle_income.csv", "Upper-Middle Income", income_dir))
files_to_save.append((df_lower_middle, "lower_middle_income.csv", "Lower-Middle Income", income_dir))
files_to_save.append((df_low_income, "low_income.csv", "Low Income", income_dir))

# Add education, academic and research filters (moved to education folder)
files_to_save.append((df_education_comprehensive, f"{csn}_Education_Academic_Research.csv", "Education/Academic/Research", education_dir))
files_to_save.append((df_hicedu, f"{csn}_Education_EDU_Only.csv", "Education (.edu only)", education_dir))

# Add Others filter
files_to_save.append((df_others, "others.csv", "others", others_dir))

# Add priority filters
files_to_save.append((df_1st_priority, "1st_priority.csv", "1st Priority Countries", priority_dir))
files_to_save.append((df_2nd_priority, "2nd_priority.csv", "2nd Priority Countries", priority_dir))
files_to_save.append((df_3rd_priority, "3rd_priority.csv", "3rd Priority Countries", priority_dir))

# Add other filters (not in priority folder)
files_to_save.append((df_hiccn, f"{csn}_China.csv", "China", countries_dir))

# Add continent filters
for continent, df in continent_dfs.items():
    files_to_save.append((df, f"{csn}_{continent}.csv", continent, continent_dir))

# Add country filters (only for countries with records)
for tld, df in country_dfs.items():
    if len(df) > 0:
        country_name = tld.upper()  # Simple conversion to uppercase for country name
        files_to_save.append((df, f"{csn}_{country_name}.csv", country_name, countries_dir))

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving files",
    color_scheme="green"
)

# Save each file with progress tracking
for df, filename, category, directory in files_to_save:
    try:
        output_path = os.path.join(directory, filename)
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        update_save(1, f"Saved {category} ({len(df)} records) to {os.path.basename(directory)}")
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")

# Stop the progress bar
save_bar.stop()

# Print completion message
print_header("Processing Completed Successfully!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")

# Count files by directory
priority_files = sum(1 for _, _, _, dir in files_to_save if dir == priority_dir)
countries_files = sum(1 for _, _, _, dir in files_to_save if dir == countries_dir)
continent_files = sum(1 for _, _, _, dir in files_to_save if dir == continent_dir)
income_files = sum(1 for _, _, _, dir in files_to_save if dir == income_dir)
education_files = sum(1 for _, _, _, dir in files_to_save if dir == education_dir)
others_files = sum(1 for _, _, _, dir in files_to_save if dir == others_dir)

rich_progress.print_status(f"Total output files created: {len(files_to_save)}", "success")
rich_progress.print_status(f"Files in others folder: {others_files}", "info")
rich_progress.print_status(f"Files in education folder: {education_files}", "info")
rich_progress.print_status(f"Files in income-wise folder: {income_files}", "info")
rich_progress.print_status(f"Files in priority folder: {priority_files}", "info")
rich_progress.print_status(f"Files in countries folder: {countries_files}", "info")
rich_progress.print_status(f"Files in continent folder: {continent_files}", "info")
rich_progress.print_status(f"Output directories: {others_dir}, {education_dir}, {income_dir}, {priority_dir}, {countries_dir}, {continent_dir}", "info")
