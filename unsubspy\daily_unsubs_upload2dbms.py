import os
import glob
import pandas as pd
import time
import warnings
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from pandas.core.common import SettingWithCopyWarning

warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)


def process_mw_unsubscribers(mw_path, output_path):
    """Processes unsubscribers from the mw directory."""
    os.chdir(mw_path)
    csv_files = glob.glob('*.csv')
    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols=['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)
    unsub_df = df_concat[df_concat['status'] == "unsubscribed"].drop_duplicates(subset='email')
    unsub_df.rename(columns={'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True)
    unsub_df['Conference Name'] = unsub_df['Conference Name'].str.replace('unsubscribed', 'Global Unsubscriber')
    unsub_df.to_csv(output_path, index=False)


def process_port1_unsubscribers(port1_path, output_path):
    """Processes unsubscribers from the port1 directory."""
    os.chdir(port1_path)
    csv_files = glob.glob('*.csv')
    unsub_mgport = pd.concat([pd.read_csv(f, on_bad_lines='skip', encoding='latin') for f in csv_files], ignore_index=True)
    unsub_mgport.rename(columns={'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
    unsub_mgport['Conference Name'] = unsub_mgport['Conference Name'].replace(to_replace=r".*\(.+?\)", value='Global Unsubscriber', regex=True)
    unsub_mgport['Conference Name'] = unsub_mgport['Conference Name'].fillna('Global Unsubscriber')
    unsub_mgport.drop_duplicates(subset='Email', inplace=True)
    unsub_mgport.to_csv(output_path, index=False)


def combine_global_unsubscribers(unsub_paths, output_path):
    """Combines and processes global unsubscribers."""
    glob_unsubs = pd.concat([pd.read_csv(f) for f in unsub_paths], ignore_index=True)
    glob_unsubs['Conference Name'] = glob_unsubs['Conference Name'].str.replace(' - ', '-')
    glob_unsubs.rename(columns={'Email': 'Email'}, inplace=True)
    glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)
    unsub_df = glob_unsubs[glob_unsubs['Conference Name'] == 'Global Unsubscriber'].drop_duplicates(subset='Email', ignore_index=True)
    unsub_df.drop(['Conference Name'], axis=1, inplace=True)
    unsub_df['Email'] = unsub_df['Email'].str.lower()
    unsub_df.to_csv(output_path, index=False)


def upload_to_dbms(upload_file_path):
    """Uploads data to the DBMS without using ppupl.exe."""
    options = webdriver.ChromeOptions()
    options.add_experimental_option("excludeSwitches", ["enable-logging"])
    prefs = {
        "download.default_directory": r"H:\Master Bounces and Unsubs\Replied Ext\Prev_Rep_bounces_csv",
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    options.add_experimental_option('prefs', prefs)
    driver = webdriver.Chrome(options=options)
    driver.maximize_window()
    driver.get("http://swa.dbms.org.in/")
    driver.find_element(By.ID, "username").send_keys("<EMAIL>")
    driver.find_element(By.ID, "password").send_keys("Magnus@123")
    time.sleep(3)
    driver.find_element(By.ID, "btnSubmit").click()
    time.sleep(3)
    driver.get("http://swa.dbms.org.in/data_filter_sets")
    driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")
    
    # Locate the file input element
    file_input = driver.find_element(By.ID, "up_file")
    
    # Send the file path to the input element
    file_input.send_keys("H:\Master Bounces and Unsubs\Master Bounces and Unsubs\PP_Global_Unsubscribers.csv")
    
    time.sleep(5)  # Allow time for the file to be selected

    driver.find_element(By.ID, "duplicates").click()
    time.sleep(3)
    driver.find_element(By.ID, "submit_key").click()
    time.sleep(10)
    print('[MGConferences_unsubscribers] DBMS Upload Completed!')
    driver.quit()


def main():
    """Main function to orchestrate the data processing and upload."""
    base_dir = r"H:\Master Bounces and Unsubs"
    mw_dir = os.path.join(base_dir, "Postpanel Unsubs", "mw")
    port1_dir = os.path.join(base_dir, "Postpanel Unsubs", "port1")
    postpanel_dir = os.path.join(base_dir, "Postpanel Unsubs")
    master_dir = os.path.join(base_dir, "Master Bounces and Unsubs")

    # --- Process MW Unsubscribers ---
    print("Processing MW unsubscribers...")
    process_mw_unsubscribers(mw_dir, os.path.join(postpanel_dir, "mw_unsubscribers.csv"))

    # --- Process Port1 Unsubscribers ---
    print("Processing Port1 unsubscribers...")
    process_port1_unsubscribers(port1_dir, os.path.join(postpanel_dir, "unsubcriber_sheet.csv"))

    # --- Process Global Unsubscribers ---
    print("Processing global unsubscribers...")
    combine_global_unsubscribers(
        [
            os.path.join(postpanel_dir, "mw_unsubscribers.csv"),
            os.path.join(postpanel_dir, "unsubcriber_sheet.csv"),
        ],
        os.path.join(master_dir, "PP_Global_Unsubscribers.csv"),
    )

    # --- Upload to DBMS ---
    print("Uploading to DBMS...")
    upload_to_dbms(os.path.join(master_dir, "PP_Global_Unsubscribers.csv"))


if __name__ == "__main__":
    main()
