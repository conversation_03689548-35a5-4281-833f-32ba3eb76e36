import os
import glob 
import pandas as pd 
import re

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw')
#list all csv files only
csv_files = glob.glob('*.{}'.format('csv'))
csv_files

df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)

df = df_concat

status = ["unsubscribed"]
unsub_df = df[df['status'].isin(status)]

unsub_df.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True)
unsub_dfg = unsub_df.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber'))

unsub_dfg.to_csv('H:/Master Bounces and Unsubs/Postpanel Unsubs/mw_unsubscribers.csv', index=False)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs')

unsub_mgport = pd.read_csv('unsubcriber_sheet.csv', encoding = 'latin')
unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)

csv_files = glob.glob('*.{}'.format('csv'))
csv_files

glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))

glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True)

glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)

options = ['Global Unsubscriber']

unsub_df = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]

unsub_df.drop_duplicates(subset='Email', inplace=True, ignore_index=True)

unsub_df.drop(['Conference Name'], axis=1, inplace=True)
unsub_df[['Email']] = unsub_df[['Email']].applymap(lambda x:x.lower())

unsub_df.to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv", index=False)