import os
import glob 
import pandas as pd 
import numpy as np
import re
import warnings

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw')

csv_files = glob.glob('*.{}'.format('csv'))
csv_files

df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)

df = df_concat

status = ["unsubscribed"]
unsub_df = df[df['status'].isin(status)]

unsub_df.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True)
unsub_dfg = unsub_df.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber'))

unsub_dfg.to_csv('H:/Master Bounces and Unsubs/Postpanel Unsubs/mw_unsubscribers.csv', index=False)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs')

unsub_mgport = pd.read_csv('unsubcriber_sheet.csv', encoding='latin')
unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)

csv_files = glob.glob('*.{}'.format('csv'))
csv_files

glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))

glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True)

glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)

#sp_filter = input("Enter csname /w s: ")

options = ['Global Unsubscriber'] #, sp_filter

unsub_df = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]

unsub_df.drop_duplicates(subset='Email', inplace=True, ignore_index=True)

unsub_df.drop(['Conference Name'], axis=1, inplace=True)
unsub_df[['Email']] = unsub_df[['Email']].applymap(lambda x:x.lower())

unsub_df.to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv", index=False)

path = input("Enter loc: ")
csn = input("Enter csname: ")

os.chdir(path)
csv_files = glob.glob('*.{}'.format('csv'))
csv_files

os.makedirs(("merged"), exist_ok=True)

df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
df_concat.rename(columns= {'Author Name':'Name'}, inplace=True)
df_concat.dropna(subset='Email', inplace=True)
df_concat.to_csv("./merged/"+csn+"-merged_unfiltered.csv", encoding='utf-8-sig', index=False)

df_hb = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv", on_bad_lines='skip')
df_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Unsubscribes.csv")
# df_rep = pd.read_csv("H:/Master Bounces and Unsubs/Replied Ext/Prev_Rep_bounces_csv/"+csn+"_replied_bouncers.csv")
df_pp_gl_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv")
df_consolidated_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Journal Unsubs/consolidated/consolidated_unsubscribers.csv")

df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
df_concat_unsubs[['Email']] = df_concat_unsubs[['Email']].applymap(lambda x:x.lower())

# Prepare consolidated unsubscribers data
df_consolidated_unsubs[['Email']] = df_consolidated_unsubs[['Email']].applymap(lambda x:x.lower())

df_concat = pd.read_csv(path+"./merged/"+csn+"-merged_unfiltered.csv", low_memory=False)
df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
df_consolidated_filtered = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_consolidated_unsubs.Email))]
# df = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_rep.Email))]
df = df_consolidated_filtered
df.drop(['Article Title'], axis=1, inplace=True)

df.to_csv("./merged/"+csn+"-merged_deduped.csv", encoding='utf-8-sig', index=False)
df = pd.read_csv("./merged/"+csn+"-merged_deduped.csv", usecols = ["Name", "Email"])
df1 = df.replace(r'^\s*$', np.nan, regex=True)
df2 = df1.fillna('Colleague')
result = df2.drop_duplicates(subset = 'Email')
result.to_csv("./merged/"+csn+"-merged_prop_final.csv", mode = 'w+', encoding='utf-8-sig', index=False)
df = pd.read_csv("./merged/"+csn+"-merged_prop_final.csv")

print(len(df))

os.remove("./merged/"+csn+"-merged_deduped.csv")
os.remove("./merged/"+csn+"-merged_unfiltered.csv")
os.rename("./merged/"+csn+"-merged_prop_final.csv", "./merged/"+csn+"-merged.csv")

import random #weights=[1, 1], 
subj_list = ['Call for Paper: [CCT_CSNAME]', 'Hopeful Submission: [CCT_CSNAME]', 'Call for your single Article: [CCT_CSNAME]', 'Waiting for Your New Manuscript Submission: [CCT_CSNAME]', 'Requesting for manuscript submission: [CCT_CSNAME]', 'Need your early Contribution: [CCT_CSNAME]', 'Immediate Response Required: [CCT_CSNAME]', 'Submit your New Piece of writing?', 'Immediate Submit your new editorial/manuscripts: [CCT_CSNAME]', 'Request for your prompt new article submission: [CCT_CSNAME]', 'Request for your exceptional help', 'Appeal for your prompt Submission: [CCT_CSNAME]', 'Ensure with on time submission: [CCT_CSNAME]', 'Request for your instant new manuscript submission: [CCT_CSNAME]', 'Immediate Submit your New Piece of writing?', 'Immediate Response Required: [CCT_CSNAME]', 'Submit your latest work for publication: [CCT_CSNAME]', 'In search of qualified researchers like you', 'Share your valuable research work at [CCT_CSNAME]', 'Finding eminent researchers like you in Online', 'Allow us to enhance the quality of your article', 'Integrate your research work at [CCT_CSNAME]', 'We enhance the quality of your article', 'Allow us to publish your effort at [CCT_CSNAME]', 'Special Invitation to researchers', 'Submit your valuable research at [CCT_CSNAME]', 'Publish your ongoing research at [CCT_CSNAME]', 'Globalize your work at [CCT_CSNAME]', 'Accepting new manuscripts for upcoming edition at [CCT_CSNAME]', 'Publish your work in reputed journal', 'Publish your work at [CCT_CSNAME]', 'Looking forward for new submissions: [CCT_CSNAME]', 'Publish your latest research at [CCT_CSNAME]', 'Publish your work in indexed journal', 'Praiseworthy manuscript: [CCT_CSNAME]']
df["Subject"] = pd.Series(
    random.choices(subj_list, k=len(df)), 
    index=df.index
)

os.makedirs(("parts"), exist_ok=True)

part_count = len(df) // int(input("No. of temps: "))
rows_per_file = part_count
n_chunks = len(df) // rows_per_file

for i in range(n_chunks):
    start = i*rows_per_file
    stop = (i+1)*rows_per_file
    sub_df = df.iloc[start:stop]
    sub_df.to_csv(f"./parts/{csn}-{i+1}.csv", encoding='utf-8-sig', index=False)

print("Completed!")