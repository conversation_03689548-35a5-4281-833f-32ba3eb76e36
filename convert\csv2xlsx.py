import pandas as pd
import glob
import os
import warnings

# Ignore SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=pd.errors.SettingWithCopyWarning)

# Set Excel header style to None (optional, but prevents warnings)
pd.io.formats.excel.ExcelFormatter.header_style = None

def convert_csv_to_xlsx(csv_directory):
    """
    Converts all CSV files in a directory to XLSX files.

    Args:
        csv_directory: The path to the directory containing CSV files.
    """
    try:
        for csv_filepath in glob.glob(os.path.join(csv_directory, "*.csv")):
            try:
                df = pd.read_csv(csv_filepath)
                xlsx_filepath = os.path.splitext(csv_filepath)[0] + ".xlsx"
                df.to_excel(xlsx_filepath, index=False)
                print(f"Converted: {csv_filepath} -> {xlsx_filepath}")
            except pd.errors.EmptyDataError:
                print(f"Skipping empty file: {csv_filepath}")
            except pd.errors.ParserError:
                print(f"Error parsing file: {csv_filepath}. Skipping.")
            except Exception as e:
                print(f"An unexpected error occurred with {csv_filepath}: {e}")
    except FileNotFoundError:
        print(f"Error: Directory not found: {csv_directory}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

def main():
    """
    Main function to run the script.
    """
    csv_directory = input("Enter the path to your CSV files: ")
    convert_csv_to_xlsx(csv_directory)

if __name__ == "__main__":
    main()
