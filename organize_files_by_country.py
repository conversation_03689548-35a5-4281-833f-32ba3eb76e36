import os
import shutil
import re
from pathlib import Path

def organize_files_by_country(folder_path):
    """
    Organizes files by extracting country names from filenames and moving them to country folders.
    
    Expected filename formats:
    - Something_CountryName_Something.extension (e.g., Biofuels_Germany_May.xlsx)
    - Something_Something_Something_CountryName.extension (e.g., IOC_Speaker_May_Australia.xlsx)
    
    Files without a country name in the expected format will be moved to an "Others" folder.
    
    Args:
        folder_path (str): Path to the folder containing files to organize
    """
    # List of known countries to check against
    countries = [
        "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Argentina", "Armenia", "Australia",
    "Austria", "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium",
    "Belize", "Benin", "Bhutan", "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei",
    "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cameroon", "Canada", "Chad", "Chile", "China",
    "Colombia", "Comoros", "Democratic Republic of the Congo", "Republic of the Congo", "Croatia",
    "Cuba", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic",
    "Ecuador", "Egypt", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France",
    "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea",
    "Guinea-Bissau", "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran",
    "Iraq", "Ireland", "Israel", "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati",
    "North Korea", "South Korea", "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon",
    "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania", "Luxembourg", "Madagascar", "Malawi",
    "Malaysia", "Maldives", "Mali", "Malta", "Mauritania", "Mauritius", "Mexico", "Micronesia",
    "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", "Namibia",
    "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Macedonia",
    "Norway", "Oman", "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", "Paraguay",
    "Peru", "Philippines", "Poland", "Portugal", "Qatar", "Romania", "Russia", "Rwanda", "Samoa",
    "San Marino", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore",
    "Slovakia", "Slovenia", "Solomon Islands", "Somalia", "South Africa", "South Sudan", "Spain",
    "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan", "Tajikistan",
    "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia",
    "Turkey", "Turkmenistan", "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom",
    "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City", "Venezuela", "Vietnam",
    "Yemen", "Zambia", "Zimbabwe"
    ]
    
    # Ensure the folder path exists
    if not os.path.isdir(folder_path):
        print(f"Error: '{folder_path}' is not a valid directory")
        return
    
    # Change to the specified directory
    os.chdir(folder_path)
    
    # Get all files in the directory
    files = [f for f in os.listdir() if os.path.isfile(f)]
    
    if not files:
        print(f"No files found in {folder_path}")
        return
    
    print(f"Found {len(files)} files to organize")
    
    # Create Others folder for files without country name
    others_folder = "Others"
    if not os.path.exists(others_folder):
        os.makedirs(others_folder)
        print(f"Created 'Others' folder for files without country name")
    
    # Process each file
    for i, filename in enumerate(files, 1):
        try:
            # Split the filename by underscores
            parts = os.path.splitext(filename)[0].split('_')
            
            # Check each part against the countries list
            country = None
            for part in parts:
                if part in countries:
                    country = part
                    break
            
            # If no exact match, try case-insensitive matching
            if not country:
                for part in parts:
                    for c in countries:
                        if part.lower() == c.lower():
                            country = c  # Use the proper case from the list
                            break
                    if country:
                        break
            
            if country:
                print(f"[{i}/{len(files)}] File: {filename} -> Country: {country}")
                
                # Create country folder if it doesn't exist
                if not os.path.exists(country):
                    os.makedirs(country)
                    print(f"  ✓ Created folder: {country}")
                
                # Move file to country folder
                destination = os.path.join(country, filename)
                shutil.move(filename, destination)
                print(f"  ✓ Moved to {destination}")
            else:
                # Move to Others folder if no country name found
                print(f"[{i}/{len(files)}] No country name found in: {filename}")
                destination = os.path.join(others_folder, filename)
                shutil.move(filename, destination)
                print(f"  ✓ Moved to {others_folder}/{filename}")
                
        except Exception as e:
            print(f"  ✗ Error processing {filename}: {str(e)}")
    
    print(f"\nOrganization complete!")

if __name__ == "__main__":
    # Get folder path from user
    folder_path = input("Enter the folder path containing files to organize: ").strip()
    organize_files_by_country(folder_path)


