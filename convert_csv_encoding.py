import os
import pandas as pd
import glob
import re
from pathlib import Path
from rich.console import Console
from rich.progress import Progress
import unicodedata

def transliterate(text):
    """
    Transliterate Unicode characters to their closest ASCII equivalents.
    For example: é -> e, ü -> u, ñ -> n, etc.
    
    Args:
        text: Text to transliterate
        
    Returns:
        Transliterated text
    """
    if not isinstance(text, str):
        return text
    
    # Normalize to decomposed form (e.g., é -> e + ´)
    normalized = unicodedata.normalize('NFKD', text)
    
    # Remove non-ASCII characters (like accents)
    result = ''.join([c for c in normalized if not unicodedata.combining(c)])
    
    # Replace common characters that don't decompose well
    replacements = {
        # Latin/Germanic/Nordic characters
        'æ': 'ae', 'Æ': 'AE',
        'œ': 'oe', 'Œ': 'OE',
        'ß': 'ss', 'ẞ': 'SS',
        'ø': 'o', 'Ø': 'O',
        'å': 'a', 'Å': 'A',
        
        # Slavic characters (Polish, Czech, Slovak, etc.)
        'ł': 'l', 'Ł': 'L',
        'ń': 'n', 'Ń': 'N',
        'ś': 's', 'Ś': 'S',
        'ź': 'z', 'Ź': 'Z',
        'ż': 'z', 'Ż': 'Z',
        'č': 'c', 'Č': 'C',
        'ě': 'e', 'Ě': 'E',
        'ř': 'r', 'Ř': 'R',
        'š': 's', 'Š': 'S',
        'ť': 't', 'Ť': 'T',
        'ů': 'u', 'Ů': 'U',
        'ž': 'z', 'Ž': 'Z',
        
        # Turkish characters
        'ı': 'i', 'İ': 'I',
        'ğ': 'g', 'Ğ': 'G',
        'ş': 's', 'Ş': 'S',
        'ç': 'c', 'Ç': 'C',
        
        # German characters (most handled by decomposition)
        'ä': 'ae', 'Ä': 'AE',
        'ö': 'oe', 'Ö': 'OE',
        'ü': 'ue', 'Ü': 'UE',
        
        # French characters (most handled by decomposition)
        'œ': 'oe', 'Œ': 'OE',
        
        # Spanish/Portuguese characters
        'ñ': 'n', 'Ñ': 'N',
        'ç': 'c', 'Ç': 'C',
        
        # Hungarian characters
        'ő': 'o', 'Ő': 'O',
        'ű': 'u', 'Ű': 'U',
        
        # Romanian characters
        'ă': 'a', 'Ă': 'A',
        'ș': 's', 'Ș': 'S',
        'ț': 't', 'Ț': 'T',
        
        # Baltic characters
        'ą': 'a', 'Ą': 'A',
        'ę': 'e', 'Ę': 'E',
        'į': 'i', 'Į': 'I',
        'ų': 'u', 'Ų': 'U',
        'ė': 'e', 'Ė': 'E',
        
        # Icelandic characters
        'þ': 'th', 'Þ': 'Th',
        'ð': 'd', 'Ð': 'D',
        
        # Croatian/Serbian/Bosnian
        'đ': 'd', 'Đ': 'D',
        
        # Cyrillic approximations (basic)
        'а': 'a', 'А': 'A',
        'б': 'b', 'Б': 'B',
        'в': 'v', 'В': 'V',
        'г': 'g', 'Г': 'G',
        'д': 'd', 'Д': 'D',
        'е': 'e', 'Е': 'E',
        'ж': 'zh', 'Ж': 'Zh',
        'з': 'z', 'З': 'Z',
        'и': 'i', 'И': 'I',
        'й': 'y', 'Й': 'Y',
        'к': 'k', 'К': 'K',
        'л': 'l', 'Л': 'L',
        'м': 'm', 'М': 'M',
        'н': 'n', 'Н': 'N',
        'о': 'o', 'О': 'O',
        'п': 'p', 'П': 'P',
        'р': 'r', 'Р': 'R',
        'с': 's', 'С': 'S',
        'т': 't', 'Т': 'T',
        'у': 'u', 'У': 'U',
        'ф': 'f', 'Ф': 'F',
        'х': 'kh', 'Х': 'Kh',
        'ц': 'ts', 'Ц': 'Ts',
        'ч': 'ch', 'Ч': 'Ch',
        'ш': 'sh', 'Ш': 'Sh',
        'щ': 'shch', 'Щ': 'Shch',
        'ъ': '', 'Ъ': '',
        'ы': 'y', 'Ы': 'Y',
        'ь': '', 'Ь': '',
        'э': 'e', 'Э': 'E',
        'ю': 'yu', 'Ю': 'Yu',
        'я': 'ya', 'Я': 'Ya'
    }
    
    for old, new in replacements.items():
        result = result.replace(old, new)
    
    return result

def convert_csv_encoding(directory_path):
    """
    Converts all CSV files in a directory from latin-1 to utf-8-sig encoding
    while transliterating non-English characters to their English alternatives.
    
    Args:
        directory_path (str): Path to the directory containing CSV files
    """
    console = Console()
    console.print(f"\n[bold blue]Converting CSV files from latin-1 to utf-8-sig and transliterating characters[/bold blue]")
    
    # Get all CSV files in the directory
    csv_files = list(Path(directory_path).glob("*.csv"))
    
    if not csv_files:
        console.print(f"[yellow]No CSV files found in {directory_path}[/yellow]")
        return
    
    console.print(f"[green]Found {len(csv_files)} CSV files to convert[/green]")
    
    # Create backup directory
    backup_dir = os.path.join(directory_path, "original_encoding_backup")
    os.makedirs(backup_dir, exist_ok=True)
    console.print(f"[blue]Created backup directory: {backup_dir}[/blue]")
    
    # Process each file with progress bar
    with Progress() as progress:
        task = progress.add_task("[cyan]Converting files...", total=len(csv_files))
        
        for csv_file in csv_files:
            try:
                # Read the file with pandas using latin-1 encoding
                df = pd.read_csv(csv_file, encoding='latin-1')
                
                # Create backup of original file
                backup_path = os.path.join(backup_dir, csv_file.name)
                with open(csv_file, 'rb') as f:
                    content = f.read()
                with open(backup_path, 'wb') as f:
                    f.write(content)
                
                # Fix column names with BOM characters
                new_columns = {}
                for col in df.columns:
                    # Check for BOM characters in column names
                    if re.match(r'ï»¿.*Author Name.*', col) or col.startswith('\ufeff'):
                        new_columns[col] = "Author Name"
                    elif "Author Name" in col and col != "Author Name":
                        new_columns[col] = "Author Name"
                    else:
                        # Transliterate other column names
                        transliterated = transliterate(col)
                        if transliterated != col:
                            new_columns[col] = transliterated
                
                # Rename columns if needed
                if new_columns:
                    df = df.rename(columns=new_columns)
                    console.print(f"[green]Fixed column headers in {csv_file.name}[/green]")
                
                # Transliterate string data in all columns
                for column in df.columns:
                    if df[column].dtype == 'object':  # Only process string columns
                        df[column] = df[column].apply(transliterate)
                
                # Write with utf-8-sig encoding
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                
                progress.update(task, advance=1, description=f"Converted: {csv_file.name}")
                
            except Exception as e:
                progress.update(task, advance=1, description=f"Error with {csv_file.name}")
                console.print(f"[red]Error converting {csv_file.name}: {str(e)}[/red]")
    
    console.print(f"[bold green]Conversion complete! Original files backed up to {backup_dir}[/bold green]")

if __name__ == "__main__":
    directory = input("Enter the directory path containing CSV files: ")
    convert_csv_encoding(directory)







