import os
import glob 
import pandas as pd 
import numpy as np
import re
import warnings
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw')
#list all csv files only
csv_files = glob.glob('*.{}'.format('csv'))
df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False, usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)
df = df_concat
status = ["unsubscribed"]
unsub_df = df[df['status'].isin(status)]
unsub_df1 = unsub_df.drop_duplicates(subset='email') #newline added
unsub_df1.to_csv('mw_list_unsubscriber.csv', encoding = 'utf-8-sig', index=False) #newline added
unsub_df2 = pd.read_csv('mw_list_unsubscriber.csv') #newline
unsub_df2.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True) #changed
unsub_dfg = unsub_df2.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber')) #changed

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs')
unsub_dfg.to_csv('mw_unsubscribers.csv', mode='w+', index=False)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs\port1')
csv_files = glob.glob('*.{}'.format('csv'))
unsub_mgport = pd.concat([pd.read_csv(f, on_bad_lines='skip', encoding = 'latin') for f in csv_files], ignore_index=True)
#unsub_mgport = pd.read_csv('unsubcriber_sheet.csv', encoding = 'latin')
unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
unsub_mgportg['Conference Name'] = unsub_mgportg['Conference Name'].fillna('Global Unsubscriber')
unsub_mgportg.drop_duplicates(subset='Email', inplace=True)

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs')
unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)

os.chdir(r"H:\Master Bounces and Unsubs\Postpanel Unsubs")
csv_files = glob.glob('*.{}'.format('csv'))
glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))
glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True)
glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)
#sp_filter = input("Enter csname /w s: ")
options = ['Global Unsubscriber']
unsub_df3 = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]
unsub_df3.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
unsub_df3.drop(['Conference Name'], axis=1, inplace=True)
unsub_df3[['Email']] = unsub_df3[['Email']].applymap(lambda x:x.lower())
unsub_df3.drop_duplicates(subset='Email', inplace=True)

os.chdir(r'H:\Master Bounces and Unsubs\Master Bounces and Unsubs')
unsub_df3.to_csv("PP_Global_Unsubscribers.csv", index=False)
print('Saved to Location and Uploading to DBMS')
'''
import os
from selenium import webdriver 
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By 
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
import time
timestr = time.strftime("%d%m%Y")
options = webdriver.ChromeOptions()
options.add_experimental_option("excludeSwitches", ["enable-logging"])
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
prefs = {
"upload.default_directory": r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs",
"upload.prompt_for_download": False,
"upload.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(options)
driver.set_window_size(1920, 1080)  # Ensure proper rendering
driver.get("http://swa.dbms.org.in/")
driver.find_element(By.ID, "username").send_keys("<EMAIL>")
driver.find_element(By.ID, "password").send_keys("Magnus@123")
time.sleep(3)
driver.find_element(By.ID, "btnSubmit").click()
time.sleep(3)
driver.get("http://swa.dbms.org.in/data_filter_sets")
driver.get("http://swa.dbms.org.in/upload_data_file.php?datafilter=318&datafiltername=MGConferences_unsubscribers")

# Define the file path - using absolute path with proper escaping
file_path = r"H:\Master Bounces and Unsubs\Master Bounces and Unsubs\new_records\PP_Global_Unsubscribers.csv"

# Direct file upload using send_keys - this replaces the PyAutoGUI approach
upload_input = driver.find_element(By.ID, 'up_file')
upload_input.send_keys(file_path)

# Wait for the file to be uploaded
time.sleep(3)

driver.find_element(By.ID, "duplicates").click()
time.sleep(3)
driver.find_element(By.ID, "submit_key").click()
time.sleep(10)

print('[MGConferences_unsubscribers] DBMS Upload Completed!')'''