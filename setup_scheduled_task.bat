@echo off
echo ===============================================
echo    SETUP SCHEDULED TASK FOR UNSUBSCRIBE CHECK
echo ===============================================
echo.

REM Get the full path to the batch file
set "BATCH_FILE=%~dp0run_check_and_run_unsubs.bat"
echo Batch file path: %BATCH_FILE%

REM Create the scheduled task
echo Creating scheduled task...
schtasks /create /tn "CheckForCSVAndRunUnsubs" /tr "\"%BATCH_FILE%\"" /sc DAILY /st 09:00 /ru "%USERNAME%" /f

if %errorlevel% neq 0 (
    echo ERROR: Failed to create scheduled task. Error code: %errorlevel%
    echo You may need to run this script as administrator.
) else (
    echo SUCCESS: Scheduled task created successfully.
    echo The task will run daily at 9:00 AM.
)

echo.
echo To modify or delete this task, use the Windows Task Scheduler.
echo.
pause
