#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to delete all text files containing "log" in their filename
from the specified directory.

Author: AI Assistant
Date: 2025-08-13
"""

import os
import glob
import sys
from pathlib import Path

def delete_log_files(directory_path):
    """
    Delete all text files containing 'log' in their filename from the specified directory.
    
    Args:
        directory_path (str): Path to the directory to search for log files
    """
    
    # Convert to Path object for easier handling
    directory = Path(directory_path)
    
    # Check if directory exists
    if not directory.exists():
        print(f"Error: Directory '{directory_path}' does not exist.")
        return
    
    if not directory.is_dir():
        print(f"Error: '{directory_path}' is not a directory.")
        return
    
    # Find all text files containing 'log' in their filename
    log_files = []
    
    # Search for .txt files containing 'log' in filename
    txt_pattern = directory / "*log*.txt"
    log_files.extend(glob.glob(str(txt_pattern)))
    
    # Also search for .log files
    log_pattern = directory / "*.log"
    log_files.extend(glob.glob(str(log_pattern)))
    
    # Remove duplicates and sort
    log_files = sorted(list(set(log_files)))
    
    if not log_files:
        print("No text files containing 'log' found in the directory.")
        return
    
    print(f"Found {len(log_files)} log files to delete:")
    for file_path in log_files:
        print(f"  - {os.path.basename(file_path)}")
    
    # Ask for confirmation
    response = input("\nDo you want to delete these files? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        deleted_count = 0
        failed_count = 0
        
        for file_path in log_files:
            try:
                os.remove(file_path)
                print(f"✓ Deleted: {os.path.basename(file_path)}")
                deleted_count += 1
            except Exception as e:
                print(f"✗ Failed to delete {os.path.basename(file_path)}: {e}")
                failed_count += 1
        
        print(f"\nSummary:")
        print(f"  Successfully deleted: {deleted_count} files")
        if failed_count > 0:
            print(f"  Failed to delete: {failed_count} files")
    else:
        print("Operation cancelled. No files were deleted.")

def main():
    """Main function"""
    # Default directory path
    default_directory = r"C:\Users\<USER>\OneDrive\My Files"
    
    # Check if directory path is provided as command line argument
    if len(sys.argv) > 1:
        directory_path = sys.argv[1]
    else:
        directory_path = default_directory
    
    print(f"Searching for log files in: {directory_path}")
    print("-" * 50)
    
    delete_log_files(directory_path)

if __name__ == "__main__":
    main()
