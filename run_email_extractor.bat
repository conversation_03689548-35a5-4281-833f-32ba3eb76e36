@echo off
echo ================================================
echo Elastic Email CSV Extractor
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Install required packages if not already installed
echo Installing required Python packages...
pip install pandas >nul 2>&1

echo.
echo Starting email extraction...
echo.

REM Run the Python script with default directories
python elastic_email_extractor.py

echo.
echo Press any key to exit...
pause >nul
