import pandas as pd
import os
import chardet

folder_path = input('Enter Folder Path: ')

# Iterate through all files in the folder
for filename in os.listdir(folder_path):
    if filename.endswith('.txt') or filename.endswith('.tsv') or filename.endswith('.tab') or filename.endswith('.csv'):
        file_path = os.path.join(folder_path, filename)

        # Detect the file encoding
        try:
            with open(file_path, 'rb') as f:
                result = chardet.detect(f.read())
                encoding = result['encoding']

            # Check if the file is actually tab-separated
            with open(file_path, 'r', encoding=encoding) as f:
                first_line = f.readline()
                if '\t' not in first_line:
                    print(f"Skipping {filename}: Not a tab-separated file")
                    continue

            # Read the tab-separated file
            df = pd.read_csv(file_path, sep='\t', encoding=encoding)

            # Generate the output file path with .xlsx extension
            output_path = os.path.splitext(file_path)[0] + '.xlsx'

            # Save the DataFrame to an Excel file
            df.to_excel(output_path, index=False)

            print(f"Excel file saved to: {output_path}")
            print(len(df))

        except Exception as e:
            print(f"Error processing {filename}: {e}")
