import os
import pandas as pd
from pathlib import Path
import chardet
from rich.console import <PERSON>sol<PERSON>
from rich.progress import Progress

def convert_csv_to_xlsx(csv_path, chunk_size=1000000):
    """
    Converts a CSV file with Latin-1 encoding to XLSX format.
    If the file has more than 1 million rows, it splits the output into multiple XLSX files.
    
    Args:
        csv_path (str): Path to the CSV file
        chunk_size (int): Maximum number of rows per XLSX file
    """
    console = Console()
    csv_path = Path(csv_path)
    
    if not csv_path.exists():
        console.print(f"[red]Error: File {csv_path} does not exist[/red]")
        return
    
    # Detect encoding to confirm it's Latin-1
    with open(csv_path, 'rb') as rawdata:
        result = chardet.detect(rawdata.read(100000))
    detected_encoding = result['encoding']
    confidence = result['confidence']
    
    console.print(f"[blue]Detected encoding: {detected_encoding} (confidence: {confidence:.2f})[/blue]")
    
    # Use Latin-1 encoding or detected encoding if confidence is high
    encoding_to_use = 'latin-1'
    if detected_encoding.lower() in ['iso-8859-1', 'windows-1252'] and confidence > 0.7:
        encoding_to_use = detected_encoding
    
    # Create output directory
    output_dir = csv_path.parent / "xlsx_output"
    output_dir.mkdir(exist_ok=True)
    base_filename = csv_path.stem
    
    console.print(f"[green]Converting {csv_path.name} to XLSX format...[/green]")
    
    # Count total rows first
    total_rows = sum(1 for _ in open(csv_path, encoding=encoding_to_use)) - 1  # Subtract header
    console.print(f"[blue]Total rows: {total_rows:,}[/blue]")
    
    if total_rows <= chunk_size:
        # For smaller files, convert directly
        console.print("[blue]File size is under 1 million rows. Converting to single XLSX file...[/blue]")
        df = pd.read_csv(csv_path, encoding=encoding_to_use, low_memory=False)
        output_path = output_dir / f"{base_filename}.xlsx"
        df.to_excel(output_path, index=False)
        console.print(f"[green]Conversion complete! File saved to: {output_path}[/green]")
    else:
        # For larger files, process in chunks
        console.print(f"[yellow]File has {total_rows:,} rows. Splitting into multiple XLSX files...[/yellow]")
        
        with Progress() as progress:
            task = progress.add_task("[cyan]Converting chunks...", total=total_rows)
            
            # Process the CSV in chunks
            chunk_num = 1
            for chunk in pd.read_csv(csv_path, encoding=encoding_to_use, chunksize=chunk_size, low_memory=False):
                output_path = output_dir / f"{base_filename}_part{chunk_num:03d}.xlsx"
                chunk.to_excel(output_path, index=False)
                
                rows_processed = len(chunk)
                progress.update(task, advance=rows_processed)
                console.print(f"[green]Created: {output_path.name} ({rows_processed:,} rows)[/green]")
                chunk_num += 1
        
        console.print(f"[bold green]Conversion complete! {chunk_num-1} XLSX files created in {output_dir}[/bold green]")

if __name__ == "__main__":
    console = Console()
    console.print("[bold]CSV to XLSX Converter (with splitting for large files)[/bold]")
    csv_path = input("Enter the path to the CSV file: ")
    
    try:
        convert_csv_to_xlsx(csv_path)
    except Exception as e:
        console.print(f"[red]Error during conversion: {str(e)}[/red]")