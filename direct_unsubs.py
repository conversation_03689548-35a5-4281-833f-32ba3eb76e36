import os
import glob
import pandas as pd
from rich.progress import Progress

# Set Excel header style to None (removes formatting)
pd.io.formats.excel.ExcelFormatter.header_style = None

def process_csv_files(directory, output_name):
    """Process all CSV files in a directory and extract email addresses.

    Args:
        directory (str): Directory containing CSV files
        output_name (str): Base name for output files (without extension)
    """
    print(f"\nProcessing files for: {output_name}")

    try:
        # Change to the specified directory
        os.chdir(directory)
        print(f"Changed directory to: {directory}")

        # Find all CSV files
        csv_files = glob.glob('*.csv')
        print(f"Found {len(csv_files)} CSV files")

        if not csv_files:
            print("No CSV files found in the directory.")
            return

        # Initialize an empty DataFrame
        all_emails = pd.DataFrame(columns=['Email'])
        processed_files = 0
        skipped_files = 0

        # Initialize progress bar
        with Progress() as progress:
            task_id = progress.add_task("[cyan]Processing files...", total=len(csv_files))

            # Process each CSV file
            for f in csv_files:
                try:
                    df = pd.read_csv(f, on_bad_lines='skip', low_memory=False)

                    # Check for 'Email' or 'email' header (case-insensitive)
                    email_col = None
                    for col in df.columns:
                        if col.lower() == 'email':
                            email_col = col
                            break

                    if email_col:
                        # Extract emails and add to main DataFrame
                        temp_df = pd.DataFrame({'Email': df[email_col]})
                        all_emails = pd.concat([all_emails, temp_df], ignore_index=True)
                        processed_files += 1
                        print(f"Processed: {f} - Found {len(df)} rows")
                    else:
                        print(f"Warning: File {f} does not contain an email column. Skipping.")
                        skipped_files += 1

                except pd.errors.EmptyDataError:
                    print(f"Warning: File {f} is empty. Skipping.")
                    skipped_files += 1
                except pd.errors.ParserError:
                    print(f"Warning: File {f} has parsing errors. Skipping.")
                    skipped_files += 1
                except Exception as e:
                    print(f"Error processing file {f}: {str(e)}")
                    skipped_files += 1
                finally:
                    progress.update(task_id, advance=1)

        # Clean the data
        original_count = len(all_emails)
        all_emails.drop_duplicates(subset='Email', inplace=True)
        all_emails.dropna(subset='Email', inplace=True)
        final_count = len(all_emails)

        # Create output directory if it doesn't exist
        output_dir = 'merged'
        os.makedirs(output_dir, exist_ok=True)

        # Save the combined emails to CSV and Excel files
        csv_path = os.path.join(output_dir, f"{output_name}.csv")
        xlsx_path = os.path.join(output_dir, f"{output_name}.xlsx")

        all_emails.to_csv(csv_path, index=False, encoding='utf-8-sig')
        all_emails.to_excel(xlsx_path, index=False)

        print(f"\nSummary for {output_name}:")
        print(f"  Processed files: {processed_files}")
        print(f"  Skipped files: {skipped_files}")
        print(f"  Total emails found: {original_count}")
        print(f"  Unique emails after cleaning: {final_count}")
        print(f"  Removed duplicates/empty: {original_count - final_count}")
        print(f"  Output saved to: {csv_path} and {xlsx_path}")

    except Exception as e:
        print(f"Error: {str(e)}")

# Main execution
if __name__ == "__main__":
    print("===== Email Unsubscribe Processor =====\n")

    # Process Journals unsubscribes
    journals_path = "H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_j.exe\ee_unsubs"
    if journals_path:
        process_csv_files(journals_path, "Journals_unsubscribes")

    # Process Conferences unsubscribes
    conferences_path = "H:\Master Bounces and Unsubs\Bounces\ee.exe\ee_unsubs"
    if conferences_path:
        process_csv_files(conferences_path, "Conferences_unsubscribes")

    print("\nAll processing completed!")
