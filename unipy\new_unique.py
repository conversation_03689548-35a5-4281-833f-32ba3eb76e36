import pandas as pd
import os
import re
import glob

def extract_segment(path):
    """
    Extracts a segment from a path using a regular expression.

    Args:
        path: The path string.

    Returns:
        The extracted segment (string) or None if not found.
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"  # Matches alphanumeric, spaces, hyphens, and 4 digits
    match = re.search(pattern, path)
    return match.group(1) if match else None

def process_data(cluster_folder, newdata_folder, output_path, csn):
    """
    Reads data from CSV files in folders, finds unique emails, and saves the result.

    Args:
        cluster_folder: Path to the folder containing cluster CSV files.
        newdata_folder: Path to the folder containing newdata CSV files.
        output_path: Path to save the output CSV file.
        csn: The CSN string for the output filename.
    """
    try:
        # Find all CSV files in the cluster folder
        cluster_files = glob.glob(os.path.join(cluster_folder, "*.csv"))
        if not cluster_files:
            raise FileNotFoundError(f"No CSV files found in cluster folder: {cluster_folder}")

        # Concatenate all cluster CSV files into a single DataFrame
        df_cluster = pd.concat([pd.read_csv(file) for file in cluster_files], ignore_index=True)

        # Find all CSV files in the newdata folder
        newdata_files = glob.glob(os.path.join(newdata_folder, "*.csv"))
        if not newdata_files:
            raise FileNotFoundError(f"No CSV files found in newdata folder: {newdata_folder}")

        # Concatenate all newdata CSV files into a single DataFrame
        df_newdata = pd.concat([pd.read_csv(file) for file in newdata_files], ignore_index=True)

    except FileNotFoundError as e:
        print(f"Error: {e}")
        return
    except pd.errors.EmptyDataError:
        print("Error: One or more of the input files are empty.")
        return
    except pd.errors.ParserError:
        print("Error: There was an error parsing one or more of the input files.")
        return
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return

    # Check if 'Email' column exists in both DataFrames
    if 'Email' not in df_cluster.columns or 'Email' not in df_newdata.columns:
        print("Error: 'Email' column not found in one or both of the input files.")
        return

    df_new_uniq = df_newdata[~df_newdata.Email.isin(df_cluster.Email)]

    # Check if 'Name' or 'Author Name' column exists
    if 'Author Name' in df_new_uniq.columns:
        # If 'Author Name' exists, rename it to 'Name' for consistency
        df_new_uniq.rename(columns={'Author Name': 'Name'}, inplace=True)
        print("Renamed 'Author Name' column to 'Name'")
    elif 'Name' not in df_new_uniq.columns:
        print("Error: Neither 'Name' nor 'Author Name' column found in the newdata file.")
        return

    # Check if 'Email' column exists
    if 'Email' not in df_new_uniq.columns:
        print("Error: 'Email' column not found in the newdata file.")
        return

    column_list = ['Name', 'Email']

    # Create the "new_unique" subfolder within the output path
    new_unique_folder = os.path.join(output_path, "new_unique")
    os.makedirs(new_unique_folder, exist_ok=True)  # Create the directory if it doesn't exist

    output_file = os.path.join(new_unique_folder, f"{csn}_new-unique.csv")
    df_new_uniq.to_csv(output_file, columns=column_list, encoding='utf-8-sig', index=False)

    print(f"Number of unique rows: {len(df_new_uniq)}")
    print(f"Data saved to: {output_file}")

def main():
    """
    Main function to run the script.
    """
    path = input("Enter Path to Save: ")
    csn = extract_segment(path)

    if csn:
        print(f"Extracted segment: {csn}")
    else:
        print("Error: Desired segment not found in the path.")
        return

    cluster_folder = input("Enter Path of Cluster Folder: ")
    newdata_folder = input("Enter Path of Newdata Folder: ")

    process_data(cluster_folder, newdata_folder, path, csn)

if __name__ == "__main__":
    main()
