import os
from selenium import webdriver 
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By 
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from pynput.keyboard import Key, Controller
import pyautogui
import time
import subprocess

timestr = time.strftime("%d%m%Y")
options = webdriver.ChromeOptions()
#options.add_argument('--headless')
options.add_experimental_option("excludeSwitches", ["enable-logging"])
prefs = {
"download.default_directory": r"H:\Master Bounces and Unsubs\Postpanel Unsubs\mw",
"download.prompt_for_download": False,
"download.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(options)
#driver = webdriver.Chrome()
driver.maximize_window()
driver.get("https://auth.wetransfer.com/")
time.sleep(2)
driver.find_element(By.XPATH, '//*[@id="__next"]/div/div[2]/div[2]/div[2]/div[1]/div[3]/div[4]/button[1]').click()
time.sleep(2)
driver.find_element(By.XPATH, '//*[@id="__next"]/div/div[3]/div/div[2]/button').click()
time.sleep(2)
driver.find_element(By.XPATH, '//*[@id="navigation.login"]/a/span').click()
time.sleep(2)
driver.implicitly_wait(5)
driver.find_element(By.XPATH, '//*[@id="google-button"]').click()
driver.find_element(By.XPATH, '//*[@id="identifierId"]').send_keys("<EMAIL>")
driver.find_element(By.XPATH, '//*[@id="identifierNext"]/div/button/span').click()
driver.find_element(By.XPATH, '//*[@id="password"]/div[1]/div/div[1]/input').send_keys("Messi@2017")
browser = driver.find_element(By.XPATH, '//*[@id="passwordNext"]/div/button/span').click()
time.sleep(3)
driver.find_element(By.XPATH, '//*[@id="__next"]/div/div[2]/div/div[1]/div[1]/div[1]/div[1]/div/h2').click()
time.sleep(3)
subprocess.call("script.exe")
subprocess.call("script2.exe")
time.sleep(3)
driver.find_element(By.XPATH, '//*[@id="autosuggest"]').send_keys('<EMAIL>,<EMAIL>,')
driver.find_element(By.XPATH, '//*[@id="__next"]/div/div[2]/div/div[2]/button[2]').click()
time.sleep(60)