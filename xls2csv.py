import os
import pandas as pd 
import glob
import datetime

path = input('Loc: ')
os.chdir(path)

import re

# Given path
path = path

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    desired_segment = match.group(1)
    print(desired_segment)
else:
    print("Desired segment not found")


csn = desired_segment #input('Enter csn: ')  


xls_files = glob.glob('*.{}'.format('xlsx'))
xls_files

df_concat = pd.concat([pd.read_excel(f) for f in xls_files], ignore_index=True) #from( , usecols = ['Author Name', 'Email', 'email'])
df_concat.rename(columns={'email': 'Email'}, inplace=True)

if 'Article Title' not in df_concat.columns:
    df_concat['Article Title'] = 0

now = datetime.datetime.now()
timestamp = now.strftime('%Y%m%d%H%M%S')

df_concat.drop_duplicates(subset='Email').to_csv((csn+'_'+timestamp+'.csv'), encoding='utf-8-sig', index=False)

print(len(df_concat))