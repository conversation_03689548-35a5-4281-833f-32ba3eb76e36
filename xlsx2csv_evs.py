import os
import pandas as pd 
import glob

os.chdir(input('Loc: '))

xls_files = glob.glob('*.{}'.format('xlsx'))
xls_files

df_concat = pd.concat([pd.read_excel(f) for f in xls_files], ignore_index=True) #from( , usecols = ['Author Name', 'Email', 'email'])
df_concat.rename(columns={'firstname': 'Author Name', 'email': 'Email', 'title': 'Article Title'}, inplace=True)

df_concat.drop_duplicates(subset='Email').to_csv((input('filename: ')+'.csv'), encoding='utf-8-sig', index=False)

print(len(df_concat))