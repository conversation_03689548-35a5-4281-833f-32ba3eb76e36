Starting master-s_2.0.py at 01-09-2025 14:51:25.46 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:17<00:00, 17.38s/it]
Processing: 100%|##########| 1/1 [00:17<00:00, 17.38s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:05<00:00,  5.29s/it]
Starting: 100%|##########| 1/1 [00:05<00:00,  5.29s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:09<00:00,  9.09s/it]
Processing: 100%|##########| 1/1 [00:09<00:00,  9.09s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:27<00:00, 27.41s/it]
Processing: 100%|##########| 1/1 [00:27<00:00, 27.41s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.30it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.30it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:03<00:00,  3.65s/it]
Processing: 100%|##########| 1/1 [00:03<00:00,  3.65s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 10.30it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.07s/it]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.07s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:41<00:00, 41.07s/it]
Finishing: 100%|##########| 1/1 [00:41<00:00, 41.07s/it]
SUCCESS: master-s_2.0.py completed successfully at 01-09-2025 14:53:51.26 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 01-09-2025 14:54:10.79 
