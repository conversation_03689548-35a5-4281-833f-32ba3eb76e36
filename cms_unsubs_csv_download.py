import os
import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

# Try to import rich for gradient progress bars, fall back to tqdm if not available
try:
    from rich.progress import Progress, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn, SpinnerColumn
    from rich.console import Console
    from rich.theme import Theme
    RICH_AVAILABLE = True
    print("Using Rich for gradient progress bars")
except ImportError:
    from tqdm import tqdm
    RICH_AVAILABLE = False
    print("Rich library not found. Using standard tqdm progress bars.")
    print("To install rich, run: pip install rich")

path_dir = r'H:\Master Bounces and Unsubs\Postpanel Unsubs'

if not os.path.exists(path_dir):
    os.makedirs(path_dir)
os.chdir(path_dir)

def wait_for_download_with_progress(download_dir, timeout=100):
    """
    Waits for the download to complete by checking the download directory and shows a progress bar.
    :param download_dir: Directory where the file is being downloaded.
    :param timeout: Maximum time to wait for the download to complete (in seconds).
    """
    # Get the initial set of files in the directory
    initial_files = set(os.listdir(download_dir))
    end_time = time.time() + timeout

    if RICH_AVAILABLE:
        # Use Rich for gradient progress bar
        console = Console()
        console.print("[bold cyan]Waiting for download to complete...[/bold cyan]")

        with Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}[/bold blue]"),
            BarColumn(complete_style="#6655ff", finished_style="#8844ff"),  # Purple gradient
            TaskProgressColumn(),
            TimeRemainingColumn(),
        ) as progress:
            download_task = progress.add_task("[cyan]Downloading...[/cyan]", total=100)

            while time.time() < end_time:
                files = os.listdir(download_dir)
                # Check for any partially downloaded file (e.g., .crdownload or .tmp extensions)
                downloading_files = [f for f in files if f.endswith('.crdownload') or f.endswith('.tmp')]
                if downloading_files:
                    file_path = os.path.join(download_dir, downloading_files[0])
                    if os.path.exists(file_path):
                        # Get the current size of the file
                        file_size = os.path.getsize(file_path)
                        # Simulate progress (you can adjust this logic based on expected file size if known)
                        file_progress = min(100, int((file_size / (file_size + 1)) * 100))  # Avoid division by zero
                        progress.update(download_task, completed=file_progress)
                    time.sleep(0.5)  # Check more frequently for smoother progress
                else:
                    # Check for newly added .csv files
                    current_files = set(files)
                    new_files = current_files - initial_files
                    completed_files = [f for f in new_files if f.endswith('.csv')]
                    if completed_files:
                        progress.update(download_task, completed=100)
                        console.print(f"[bold green]Download completed! New file(s): {', '.join(completed_files)}[/bold green]")
                        return

            # If we get here, the download timed out
            console.print("[bold red]Download timed out![/bold red]")
            raise TimeoutError("Download did not complete within the specified timeout.")
    else:
        # Fallback to tqdm if Rich is not available
        progress_bar = tqdm(total=100, desc="Downloading", unit="%", ncols=80)  # Initialize progress bar

        while time.time() < end_time:
            files = os.listdir(download_dir)
            # Check for any partially downloaded file (e.g., .crdownload or .tmp extensions)
            downloading_files = [f for f in files if f.endswith('.crdownload') or f.endswith('.tmp')]
            if downloading_files:
                file_path = os.path.join(download_dir, downloading_files[0])
                if os.path.exists(file_path):
                    # Get the current size of the file
                    file_size = os.path.getsize(file_path)
                    # Simulate progress (you can adjust this logic based on expected file size if known)
                    progress = min(100, int((file_size / (file_size + 1)) * 100))  # Avoid division by zero
                    progress_bar.n = progress
                    progress_bar.refresh()
                time.sleep(1)  # Wait for 1 second before checking again
            else:
                # Check for newly added .csv files
                current_files = set(files)
                new_files = current_files - initial_files
                completed_files = [f for f in new_files if f.endswith('.csv')]
                if completed_files:
                    progress_bar.n = 100  # Set progress bar to 100%
                    progress_bar.refresh()
                    progress_bar.close()
                    print(f"Download completed! New file(s): {', '.join(completed_files)}")
                    return
        progress_bar.close()
        raise TimeoutError("Download did not complete within the specified timeout.")

# Selenium setup
options = webdriver.ChromeOptions()
options.add_experimental_option("excludeSwitches", ["enable-logging"])
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
prefs = {
    "download.default_directory": path_dir,
    "download.prompt_for_download": False,
    "download.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)

# Initialize Rich console for better output
if RICH_AVAILABLE:
    console = Console()
    console.print("[bold magenta]Starting CMS Unsubscribes Download Process[/bold magenta]")
else:
    print("Starting CMS Unsubscribes Download Process")

# Perform the automated download process
try:
    if RICH_AVAILABLE:
        console.print("[cyan]Initializing Chrome browser...[/cyan]")
    else:
        print("Initializing Chrome browser...")

    driver = webdriver.Chrome(options=options)
    driver.maximize_window()
    driver.set_window_size(1920, 1080)

    if RICH_AVAILABLE:
        console.print("[cyan]Navigating to login page...[/cyan]")
    else:
        print("Navigating to login page...")

    driver.get("https://admin.magnusgroup.biz/admin-login.php")
    time.sleep(2)

    if RICH_AVAILABLE:
        console.print("[cyan]Logging in...[/cyan]")
    else:
        print("Logging in...")

    driver.find_element(By.ID, "name").send_keys("server_admin")
    driver.find_element(By.ID, "password").send_keys("Magnus@123")
    driver.find_element(By.XPATH, "//button[@id='login']").click()
    time.sleep(2)

    if RICH_AVAILABLE:
        console.print("[cyan]Navigating to unsubscribes list...[/cyan]")
    else:
        print("Navigating to unsubscribes list...")

    driver.get('https://admin.magnusgroup.biz/unsubscribes_list.php')
    time.sleep(3)

    if RICH_AVAILABLE:
        console.print("[cyan]Setting up download parameters...[/cyan]")
    else:
        print("Setting up download parameters...")

    driver.find_element(By.XPATH, "//span[normalize-space()='Download Unsubscribes']").click()
    time.sleep(4)
    driver.find_element(By.XPATH, "//span[@class='filter-option pull-left']").click()
    driver.find_element(By.XPATH, "//span[normalize-space()='All']").click()
    driver.find_element(By.XPATH, "//label[normalize-space()='Works:']").click()
    driver.find_element(By.XPATH, "//input[@id='global_uns']").click()

    if RICH_AVAILABLE:
        console.print("[cyan]Initiating download...[/cyan]")
    else:
        print("Initiating download...")

    driver.find_element(By.XPATH, "//button[@id='submit_key']").click()

    # Wait for the download to complete with progress bar
    try:
        wait_for_download_with_progress(path_dir, timeout=120)
        if RICH_AVAILABLE:
            console.print("[bold green]Download process completed successfully![/bold green]")
        else:
            print("Download process completed successfully!")
    except TimeoutError as e:
        if RICH_AVAILABLE:
            console.print(f"[bold red]Download timeout: {str(e)}[/bold red]")
        else:
            print(f"Download timeout: {str(e)}")

except Exception as e:
    if RICH_AVAILABLE:
        console.print(f"[bold red]Error during execution: {str(e)}[/bold red]")
    else:
        print(f"Error during execution: {str(e)}")

finally:
    # Always close the browser to clean up resources
    try:
        if 'driver' in locals():
            if RICH_AVAILABLE:
                console.print("[cyan]Closing browser...[/cyan]")
            else:
                print("Closing browser...")
            driver.quit()
    except Exception as close_error:
        if RICH_AVAILABLE:
            console.print(f"[bold red]Error closing browser: {str(close_error)}[/bold red]")
        else:
            print(f"Error closing browser: {str(close_error)}")