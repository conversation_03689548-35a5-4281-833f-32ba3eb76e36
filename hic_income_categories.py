##HIC Income Categories Filter

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("HIC Income Categories Filter")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    csn = match.group(1)
    rich_progress.print_status(f"Found segment: {csn}", "success")
else:
    rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
    # Prompt for manual input
    csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
    if csn.strip():
        rich_progress.print_status(f"Using manually entered segment: {csn}", "info")
    else:
        rich_progress.print_status("No segment name provided. Exiting.", "error")
        exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file, low_memory=False)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# TLD to Country Name Mapping
tld_to_country = {
    # A
    '.ac': 'Ascension Island', '.ad': 'Andorra', '.ae': 'United Arab Emirates', '.af': 'Afghanistan',
    '.ag': 'Antigua and Barbuda', '.ai': 'Anguilla', '.al': 'Albania', '.am': 'Armenia',
    '.ao': 'Angola', '.aq': 'Antarctica', '.ar': 'Argentina', '.as': 'American Samoa',
    '.at': 'Austria', '.au': 'Australia', '.aw': 'Aruba', '.ax': 'Åland Islands', '.az': 'Azerbaijan',
    # B
    '.ba': 'Bosnia and Herzegovina', '.bb': 'Barbados', '.bd': 'Bangladesh', '.be': 'Belgium',
    '.bf': 'Burkina Faso', '.bg': 'Bulgaria', '.bh': 'Bahrain', '.bi': 'Burundi',
    '.bj': 'Benin', '.bm': 'Bermuda', '.bn': 'Brunei', '.bo': 'Bolivia',
    '.br': 'Brazil', '.bs': 'Bahamas', '.bt': 'Bhutan', '.bw': 'Botswana',
    '.by': 'Belarus', '.bz': 'Belize',
    # C
    '.ca': 'Canada', '.cc': 'Cocos Islands', '.cd': 'Democratic Republic of the Congo', '.cf': 'Central African Republic',
    '.cg': 'Republic of the Congo', '.ch': 'Switzerland', '.ci': 'Côte d\'Ivoire', '.ck': 'Cook Islands',
    '.cl': 'Chile', '.cm': 'Cameroon', '.cn': 'China', '.co': 'Colombia',
    '.cr': 'Costa Rica', '.cu': 'Cuba', '.cv': 'Cape Verde', '.cw': 'Curaçao',
    '.cx': 'Christmas Island', '.cy': 'Cyprus', '.cz': 'Czech Republic',
    # D
    '.de': 'Germany', '.dj': 'Djibouti', '.dk': 'Denmark', '.dm': 'Dominica',
    '.do': 'Dominican Republic', '.dz': 'Algeria',
    # E
    '.ec': 'Ecuador', '.ee': 'Estonia', '.eg': 'Egypt', '.er': 'Eritrea',
    '.es': 'Spain', '.et': 'Ethiopia', '.eu': 'European Union',
    # F
    '.fi': 'Finland', '.fj': 'Fiji', '.fk': 'Falkland Islands', '.fm': 'Micronesia',
    '.fo': 'Faroe Islands', '.fr': 'France',
    # G
    '.ga': 'Gabon', '.gd': 'Grenada', '.ge': 'Georgia', '.gf': 'French Guiana',
    '.gg': 'Guernsey', '.gh': 'Ghana', '.gi': 'Gibraltar', '.gl': 'Greenland',
    '.gm': 'Gambia', '.gn': 'Guinea', '.gp': 'Guadeloupe', '.gq': 'Equatorial Guinea',
    '.gr': 'Greece', '.gs': 'South Georgia and the South Sandwich Islands', '.gt': 'Guatemala', '.gu': 'Guam',
    '.gw': 'Guinea-Bissau', '.gy': 'Guyana',
    # H
    '.hk': 'Hong Kong', '.hm': 'Heard Island and McDonald Islands', '.hn': 'Honduras', '.hr': 'Croatia',
    '.ht': 'Haiti', '.hu': 'Hungary',
    # I
    '.id': 'Indonesia', '.ie': 'Ireland', '.il': 'Israel', '.im': 'Isle of Man',
    '.in': 'India', '.io': 'British Indian Ocean Territory', '.iq': 'Iraq', '.ir': 'Iran',
    '.is': 'Iceland', '.it': 'Italy',
    # J
    '.je': 'Jersey', '.jm': 'Jamaica', '.jo': 'Jordan', '.jp': 'Japan',
    # K
    '.ke': 'Kenya', '.kg': 'Kyrgyzstan', '.kh': 'Cambodia', '.ki': 'Kiribati',
    '.km': 'Comoros', '.kn': 'Saint Kitts and Nevis', '.kp': 'North Korea', '.kr': 'South Korea',
    '.kw': 'Kuwait', '.ky': 'Cayman Islands', '.kz': 'Kazakhstan',
    # L
    '.la': 'Laos', '.lb': 'Lebanon', '.lc': 'Saint Lucia', '.li': 'Liechtenstein',
    '.lk': 'Sri Lanka', '.lr': 'Liberia', '.ls': 'Lesotho', '.lt': 'Lithuania',
    '.lu': 'Luxembourg', '.lv': 'Latvia', '.ly': 'Libya',
    # M
    '.ma': 'Morocco', '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro',
    '.mg': 'Madagascar', '.mh': 'Marshall Islands', '.mk': 'North Macedonia', '.ml': 'Mali',
    '.mm': 'Myanmar', '.mn': 'Mongolia', '.mo': 'Macao', '.mp': 'Northern Mariana Islands',
    '.mq': 'Martinique', '.mr': 'Mauritania', '.ms': 'Montserrat', '.mt': 'Malta',
    '.mu': 'Mauritius', '.mv': 'Maldives', '.mw': 'Malawi', '.mx': 'Mexico',
    '.my': 'Malaysia', '.mz': 'Mozambique',
    # N
    '.na': 'Namibia', '.nc': 'New Caledonia', '.ne': 'Niger', '.nf': 'Norfolk Island',
    '.ng': 'Nigeria', '.ni': 'Nicaragua', '.nl': 'Netherlands', '.no': 'Norway',
    '.np': 'Nepal', '.nr': 'Nauru', '.nu': 'Niue', '.nz': 'New Zealand',
    # O
    '.om': 'Oman',
    # P
    '.pa': 'Panama', '.pe': 'Peru', '.pf': 'French Polynesia', '.pg': 'Papua New Guinea',
    '.ph': 'Philippines', '.pk': 'Pakistan', '.pl': 'Poland', '.pm': 'Saint Pierre and Miquelon',
    '.pn': 'Pitcairn Islands', '.pr': 'Puerto Rico', '.ps': 'Palestine', '.pt': 'Portugal',
    '.pw': 'Palau', '.py': 'Paraguay',
    # Q
    '.qa': 'Qatar',
    # R
    '.re': 'Réunion', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia', '.rw': 'Rwanda',
    # S
    '.sa': 'Saudi Arabia', '.sb': 'Solomon Islands', '.sc': 'Seychelles', '.sd': 'Sudan',
    '.se': 'Sweden', '.sg': 'Singapore', '.sh': 'Saint Helena', '.si': 'Slovenia',
    '.sk': 'Slovakia', '.sl': 'Sierra Leone', '.sm': 'San Marino', '.sn': 'Senegal',
    '.so': 'Somalia', '.sr': 'Suriname', '.ss': 'South Sudan', '.st': 'São Tomé and Príncipe',
    '.su': 'Soviet Union', '.sv': 'El Salvador', '.sx': 'Sint Maarten', '.sy': 'Syria', '.sz': 'Eswatini',
    # T
    '.tc': 'Turks and Caicos Islands', '.td': 'Chad', '.tf': 'French Southern Territories', '.tg': 'Togo',
    '.th': 'Thailand', '.tj': 'Tajikistan', '.tk': 'Tokelau', '.tl': 'East Timor',
    '.tm': 'Turkmenistan', '.tn': 'Tunisia', '.to': 'Tonga', '.tr': 'Turkey',
    '.tt': 'Trinidad and Tobago', '.tv': 'Tuvalu', '.tw': 'Taiwan', '.tz': 'Tanzania',
    # U
    '.ua': 'Ukraine', '.ug': 'Uganda', '.uk': 'United Kingdom', '.us': 'United States',
    '.uy': 'Uruguay', '.uz': 'Uzbekistan',
    # V
    '.va': 'Vatican City', '.vc': 'Saint Vincent and the Grenadines', '.ve': 'Venezuela', '.vg': 'British Virgin Islands',
    '.vi': 'U.S. Virgin Islands', '.vn': 'Vietnam', '.vu': 'Vanuatu',
    # W
    '.wf': 'Wallis and Futuna', '.ws': 'Samoa',
    # Y
    '.ye': 'Yemen', '.yt': 'Mayotte',
    # Z
    '.za': 'South Africa', '.zm': 'Zambia', '.zw': 'Zimbabwe',
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    # If no match found, return 'Other'
    return 'Other'

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Define World Bank income categories (as of 2024-2025)
print_section("Defining World Bank Income Categories")

# High-income economies ($14,006 or more)
high_income_tlds = [
    '.ae', '.ag', '.ai', '.an', '.aq', '.ar', '.at', '.au', '.aw', '.bb', '.be', '.bh', '.bm', '.bn', '.bs',
    '.ca', '.ch', '.cl', '.cy', '.cz', '.de', '.dk', '.ee', '.es', '.fi', '.fr', '.gb', '.gd', '.gi', '.gl',
    '.gr', '.hk', '.hr', '.hu', '.ie', '.il', '.im', '.is', '.it', '.je', '.jp', '.kn', '.kr', '.kw', '.ky',
    '.li', '.lt', '.lu', '.lv', '.mc', '.mt', '.mu', '.mx', '.my', '.nl', '.no', '.nz', '.om', '.pa', '.pl',
    '.pr', '.pt', '.qa', '.ro', '.ru', '.sa', '.se', '.sg', '.si', '.sk', '.sm', '.tc', '.tt', '.tw', '.uk',
    '.us', '.uy', '.va', '.vg', '.vi'
]

# Upper-middle-income economies ($4,516 to $14,005)
upper_middle_income_tlds = [
    '.al', '.am', '.az', '.ba', '.bg', '.bz', '.cn', '.co', '.cr', '.cu', '.do', '.dz', '.ec', '.fj', '.ga',
    '.ge', '.gq', '.gt', '.gy', '.id', '.ir', '.jm', '.jo', '.kz', '.lb', '.ly', '.md', '.me', '.mk', '.mn',
    '.mv', '.pe', '.py', '.rs', '.sr', '.th', '.tj', '.tm', '.tr', '.ua', '.ve', '.za'
]

# Lower-middle-income economies ($1,146 to $4,515)
lower_middle_income_tlds = [
    '.ao', '.bd', '.bj', '.bo', '.bt', '.ci', '.cm', '.cv', '.dj', '.eg', '.fm', '.gh', '.gm', '.gn', '.hn',
    '.ht', '.in', '.ke', '.kg', '.kh', '.ki', '.km', '.la', '.lk', '.ls', '.ma', '.mh', '.mm', '.mr', '.na',
    '.ng', '.ni', '.np', '.nr', '.pk', '.pg', '.ph', '.ps', '.pw', '.sb', '.sn', '.so', '.st', '.sv', '.sz',
    '.tl', '.tn', '.tv', '.tz', '.ug', '.uz', '.vu', '.ws', '.zm', '.zw'
]

# Low-income economies ($1,145 or less)
low_income_tlds = [
    '.af', '.bi', '.bf', '.cd', '.cf', '.er', '.et', '.gw', '.kp', '.lr', '.mg', '.ml', '.mw', '.mz', '.ne',
    '.rw', '.sd', '.sl', '.ss', '.sy', '.td', '.tg', '.ye'
]

# Create filters for each income category
print_section("Filtering by Income Categories")

# Create a progress bar for filtering
income_filter_bar, update_income_filter = rich_progress.create_progress_bar(
    total=4,  # 4 income categories
    description="Filtering by income categories",
    color_scheme="purple"
)

# High-income filter
high_income_filter = pd.Series(False, index=d2_EVENT.index)
for tld in high_income_tlds:
    high_income_filter = high_income_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_high_income = d2_EVENT[high_income_filter]
update_income_filter(1, f"Filtered high-income countries ({len(df_high_income)} records)")

# Upper-middle-income filter
upper_middle_filter = pd.Series(False, index=d2_EVENT.index)
for tld in upper_middle_income_tlds:
    upper_middle_filter = upper_middle_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_upper_middle = d2_EVENT[upper_middle_filter]
update_income_filter(1, f"Filtered upper-middle-income countries ({len(df_upper_middle)} records)")

# Lower-middle-income filter
lower_middle_filter = pd.Series(False, index=d2_EVENT.index)
for tld in lower_middle_income_tlds:
    lower_middle_filter = lower_middle_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_lower_middle = d2_EVENT[lower_middle_filter]
update_income_filter(1, f"Filtered lower-middle-income countries ({len(df_lower_middle)} records)")

# Low-income filter
low_income_filter = pd.Series(False, index=d2_EVENT.index)
for tld in low_income_tlds:
    low_income_filter = low_income_filter | d2_EVENT["Email"].str.endswith(tld, na=False)
df_low_income = d2_EVENT[low_income_filter]
update_income_filter(1, f"Filtered low-income countries ({len(df_low_income)} records)")

# Stop the progress bar
income_filter_bar.stop()

# Create output directory
print_section("Creating Output Directory")
income_dir = os.path.join(os.getcwd(), "income-wise")
os.makedirs(income_dir, exist_ok=True)
rich_progress.print_status(f"Created income-wise directory: {income_dir}", "info")

# Save income category files
print_section("Saving Income Category Files")

files_to_save = [
    (df_high_income, f"{csn}_high_income.csv", "High Income"),
    (df_upper_middle, f"{csn}_upper_middle_income.csv", "Upper-Middle Income"),
    (df_lower_middle, f"{csn}_lower_middle_income.csv", "Lower-Middle Income"),
    (df_low_income, f"{csn}_low_income.csv", "Low Income")
]

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving income category files",
    color_scheme="green"
)

# Save each file with progress tracking
for df, filename, category in files_to_save:
    try:
        output_path = os.path.join(income_dir, filename)
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        update_save(1, f"Saved {category} ({len(df)} records)")
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")

# Stop the progress bar
save_bar.stop()

# Print completion message
print_header("Income Categories Processing Completed!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")

# Print income category results
print_section("Income Category Results")
rich_progress.print_status("Income-based filtering complete. Results:", "success")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Income Category':<25} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'High Income':<25} {len(df_high_income):>10}", "info")
rich_progress.print_status(f"{'Upper-Middle Income':<25} {len(df_upper_middle):>10}", "info")
rich_progress.print_status(f"{'Lower-Middle Income':<25} {len(df_lower_middle):>10}", "info")
rich_progress.print_status(f"{'Low Income':<25} {len(df_low_income):>10}", "info")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"Output directory: {income_dir}", "info")
