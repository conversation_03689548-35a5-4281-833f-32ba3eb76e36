import os
import datetime
import subprocess
import time

# Get today's date in the format "dd-mm-yyyy"
today_date = datetime.datetime.now().strftime("%d-%m-%Y")
file_name = f"Replied Bounces {today_date}.xlsx"  # Adjust the file extension as needed

# Path to the directory where the file should be located
directory_path = "H:/Master Bounces and Unsubs/Replied bounces"  # Replace with the actual path

# Full path to the file
file_path = os.path.join(directory_path, file_name)

# Path to the Python script to be executed
script_path = "C:/Users/<USER>/OneDrive/My Files/irep_2.0.py"  # Replace with the actual script path

while True:
    # Check if the file exists
    if os.path.isfile(file_path):
        print(f"File found: {file_name}")
        # Run the Python script
        subprocess.run(["python", script_path])
        break
    else:
        print(f"File not found: {file_name}. Checking again in 1 hour.")
        # Wait for 1 hour (3600 seconds)
        time.sleep(3600)


