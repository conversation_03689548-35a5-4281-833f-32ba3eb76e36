import os
import glob
import pandas as pd
import numpy as np
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

def create_gradient_progress():
    """Create a progress bar with gradient colors"""
    return Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
        "[progress.percentage]{task.percentage:>3.0f}%",
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=Console()
    )

def clean_name(name):
    """Clean names by removing text after comma and normalizing whitespace"""
    if pd.isna(name):
        return name
    
    # Convert to string and take only the part before the first comma
    name_str = str(name).split(',')[0]
    
    # Remove leading/trailing whitespace and normalize internal whitespace
    cleaned = ' '.join(name_str.split())
    
    return cleaned

def merge_csv_files():
    """
    Merge multiple CSV files containing col1, email, and name columns.
    Standardizes column names where col1 and name both represent the same data (Name).
    """
    console = Console()
    
    # Display header
    header_text = Text("CSV Files Merger - col1/name/email", style="bold magenta")
    console.print(Panel(header_text, expand=False))
    
    # Get directory path from user
    directory_path = input("Enter the directory path containing CSV files: ").strip('"\'')
    
    # Check if directory exists
    if not os.path.exists(directory_path):
        console.print(f"[red]Error: Directory not found: {directory_path}[/red]")
        return
    
    # Store original directory
    original_dir = os.getcwd()
    
    try:
        # Change to the directory
        os.chdir(directory_path)
        
        # Find all CSV files
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            console.print("[red]No CSV files found in the directory.[/red]")
            return
        
        console.print(f"[green]Found {len(csv_files)} CSV files:[/green]")
        for file in csv_files:
            console.print(f"  • {file}")
        
        # Ask for output filename prefix
        output_prefix = input("\nEnter output filename prefix (default: 'merged'): ").strip()
        if not output_prefix:
            output_prefix = "merged"
        
        # Initialize list to store dataframes
        all_dfs = []
        total_rows = 0
        
        # Create progress bar
        with create_gradient_progress() as progress:
            task = progress.add_task("Reading and processing CSV files...", total=len(csv_files))
            
            for file in csv_files:
                try:
                    # Read CSV file
                    df = pd.read_csv(file, encoding='utf-8-sig', on_bad_lines='skip', low_memory=False)
                    
                    # Standardize column names - both col1 and name represent the same data (Name)
                    column_mapping = {
                        'col1': 'Name',
                        'name': 'Name', 
                        'email': 'Email',
                        'Email ID': 'Email',
                        'Author Name': 'Name'
                    }
                    
                    # Apply column renaming
                    for old_col, new_col in column_mapping.items():
                        if old_col in df.columns:
                            df.rename(columns={old_col: new_col}, inplace=True)
                    
                    # If we have both 'Name' columns from col1 and name, merge them
                    # This shouldn't happen after renaming, but just in case
                    if 'col1' in df.columns and 'name' in df.columns:
                        # Create a combined Name column, prioritizing non-null values
                        df['Name'] = df['col1'].fillna(df['name'])
                        df.drop(columns=['col1', 'name'], inplace=True)
                    
                    # Clean names by removing text after comma and normalizing whitespace
                    if 'Name' in df.columns:
                        df['Name'] = df['Name'].apply(clean_name)
                        # Trim leading, trailing, and extra spaces
                        df['Name'] = df['Name'].astype(str).str.strip()
                        df['Name'] = df['Name'].replace(r'\s+', ' ', regex=True)
                    
                    # Drop rows with missing Email
                    if 'Email' in df.columns:
                        df.dropna(subset=['Email'], inplace=True)
                    
                    all_dfs.append(df)
                    total_rows += len(df)
                    
                    progress.console.print(f"  ✓ Read {file}: {len(df)} rows")
                    
                except Exception as e:
                    progress.console.print(f"  ✗ Error reading {file}: {str(e)}")
                
                progress.advance(task)
        
        if not all_dfs:
            console.print("[red]No data could be read from any CSV files.[/red]")
            return
        
        # Concatenate all dataframes
        console.print("\n[yellow]Concatenating data...[/yellow]")
        combined_df = pd.concat(all_dfs, ignore_index=True)
        
        # Create merged directory if it doesn't exist
        merged_dir = "merged"
        os.makedirs(merged_dir, exist_ok=True)
        
        # Remove duplicates based on Email if Email column exists
        if 'Email' in combined_df.columns:
            console.print("[yellow]Removing duplicate emails...[/yellow]")
            initial_count = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['Email'], keep='first')
            final_count = len(combined_df)
            console.print(f"[green]Removed {initial_count - final_count} duplicate emails[/green]")
        
        # Create output filename
        output_filename = os.path.join(merged_dir, f"{output_prefix}.csv")
        
        # Save the merged file
        console.print(f"\n[yellow]Saving merged file to merged folder...[/yellow]")
        combined_df.to_csv(output_filename, index=False, encoding='utf-8-sig')
        
        # Display summary
        summary_text = f"""
[bold green]Merge Complete![/bold green]

📊 [bold]Summary:[/bold]
   • Files processed: {len(csv_files)}
   • Total rows: {len(combined_df):,}
   • Columns: {', '.join(combined_df.columns.tolist())}
   
📁 [bold]Output:[/bold]
   • File: {output_filename}
   • Location: {os.path.abspath(output_filename)}
        """
        
        console.print(Panel(summary_text, title="Merge Results", border_style="green"))
        
        # Show sample of merged data
        if len(combined_df) > 0:
            console.print("\n[bold]Sample of merged data (first 5 rows):[/bold]")
            console.print(combined_df.head().to_string(index=False))
        
    except Exception as e:
        console.print(f"[red]Error during processing: {str(e)}[/red]")
    
    finally:
        # Change back to original directory
        os.chdir(original_dir)

if __name__ == "__main__":
    merge_csv_files()
