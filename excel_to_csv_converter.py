import os
import pandas as pd
from pathlib import Path
import shutil
from datetime import datetime

def convert_excel_to_csv(folder_path):
    """
    Converts all Excel (.xlsx) files in a folder to CSV format and moves originals to backup.
    
    Args:
        folder_path (str): Path to the folder containing Excel files
    """
    # Create backup folder with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_folder = os.path.join(folder_path, f"excel_backup_{timestamp}")
    os.makedirs(backup_folder, exist_ok=True)
    
    # Get all Excel files
    excel_files = list(Path(folder_path).glob("*.xlsx"))
    
    if not excel_files:
        print(f"No Excel files found in {folder_path}")
        return
    
    print(f"Found {len(excel_files)} Excel files to convert")
    
    # Process each file
    for i, excel_path in enumerate(excel_files, 1):
        try:
            # Create output CSV path
            csv_path = excel_path.with_suffix('.csv')
            
            print(f"[{i}/{len(excel_files)}] Converting {excel_path.name} to CSV...")
            
            # Read Excel file
            df = pd.read_excel(excel_path)
            
            # Write to CSV
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            
            # Move original to backup
            shutil.move(str(excel_path), os.path.join(backup_folder, excel_path.name))
            
            print(f"  ✓ Created {csv_path.name}")
            print(f"  ✓ Moved original to backup folder")
            
        except Exception as e:
            print(f"  ✗ Error processing {excel_path.name}: {str(e)}")
    
    print(f"\nConversion complete!")
    print(f"Original Excel files backed up to: {backup_folder}")
    print(f"CSV files created in: {folder_path}")

if __name__ == "__main__":
    # Get folder path from user
    folder_path = input("Enter the folder path containing Excel files: ").strip()
    
    # Validate folder path
    if not os.path.isdir(folder_path):
        print(f"Error: '{folder_path}' is not a valid directory")
    else:
        convert_excel_to_csv(folder_path)