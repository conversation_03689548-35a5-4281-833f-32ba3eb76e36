import os
from selenium import webdriver 
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By 
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

os.chdir(r'H:\Master Bounces and Unsubs\Postpanel Unsubs\mw')
os.system('del *.csv')

import pyautogui
import time
timestr = time.strftime("%d%m%Y")
options = webdriver.ChromeOptions()
options.add_experimental_option("excludeSwitches", ["enable-logging"])
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
prefs = {
"download.default_directory": r"H:\Master Bounces and Unsubs\Postpanel Unsubs\mw",
"download.prompt_for_download": False,
"download.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(options)
#driver = webdriver.Chrome()
driver.maximize_window()
driver.get("https://216-10-242-48.cprapid.com:2087/")
time.sleep(3)
#driver.find_element(By.XPATH, '//*[@id="details-button"]').click()
#driver.find_element(By.XPATH, '//*[@id="proceed-link"]').click()
driver.find_element(By.ID, "user").send_keys("root")
driver.find_element(By.ID, "pass").send_keys("Magnus@12527")
driver.find_element(By.XPATH, "//button[@id='login_submit']").click()
time.sleep(3)
driver.find_element(By.XPATH, "//span[@class='cp-app__details-title'][normalize-space()='List Accounts']").click()
time.sleep(3)
pyautogui.keyDown('ctrl')
pyautogui.press('-')
pyautogui.press('-')
pyautogui.keyUp('ctrl')
driver.find_element(By.XPATH, "//input[@type='image']").click()
time.sleep(5)
driver.switch_to.window(driver.window_handles[1])
driver.find_element(By.XPATH, "//span[normalize-space()='phpMyAdmin']").click()
time.sleep(5)
driver.switch_to.window(driver.window_handles[2])
driver.find_element(By.XPATH, "//a[normalize-space()='emailinvite_mailWizz']").click()
time.sleep(3)
driver.find_element(By.XPATH, '//*[@id="filterText"]').send_keys('mw_list_subscriber')
driver.find_element(By.XPATH, '//*[@id="row_tbl_105"]/th/a').click()
time.sleep(10)
driver.find_element(By.XPATH, '//*[@id="topmenu"]/li[6]/a').click()
time.sleep(6)
#driver.find_element(By.XPATH, '//*[@id="plugins"]').click()
driver.find_element(By.XPATH, '//*[@id="plugins"]/option[2]').click()
time.sleep(6)
driver.find_element(By.XPATH, '//*[@id="buttonGo"]').click()
time.sleep(200)
print('Downloaded Successfully! Compiling Under Process...')