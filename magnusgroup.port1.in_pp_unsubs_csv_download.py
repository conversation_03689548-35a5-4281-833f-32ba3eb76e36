import os
import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By

# Import the rich progress module
from rich_progress import wait_for_download_with_progress, print_status, RICH_AVAILABLE

path_dir = r'H:\Master Bounces and Unsubs\Postpanel Unsubs\port1'

if not os.path.exists(path_dir):
    os.makedirs(path_dir)
os.chdir(path_dir)

# The wait_for_download_with_progress function is now imported from rich_progress module

# Selenium setup
options = webdriver.ChromeOptions()

# Suppress console logging messages
options.add_experimental_option("excludeSwitches", ["enable-logging"])

# Add logging preferences to capture browser logs
options.add_argument('log-level=3')  # Set log level to errors only

# Headless mode and other performance settings
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

# Disable extensions and other features that might cause errors
options.add_argument("--disable-extensions")
options.add_argument("--disable-notifications")

# Download preferences
prefs = {
    "download.default_directory": path_dir,
    "download.prompt_for_download": False,
    "download.directory_upgrade": True,
    "browser.download.show_plugins_in_list": False,
    "browser.download.folderList": 2,
    "browser.download.manager.showWhenStarting": False,
    "download.manager.showTaskbarProgress": False  # Disable taskbar progress updates
}
options.add_experimental_option('prefs', prefs)

# Print header
print_status("Starting MAGNUSGROUP.PORT1.IN Unsubscribes Download Process", "header")

# Perform the automated download process
try:
    print_status("Initializing Chrome browser...", "info")
    driver = webdriver.Chrome(options=options)
    driver.set_window_size(1920, 1080)  # Ensure proper rendering

    print_status("Navigating to login page...", "info")
    driver.get("https://magnusgroup.port1.in/")

    print_status("Logging in...", "info")
    driver.find_element(By.ID, "name").send_keys("admin")
    driver.find_element(By.ID, "password").send_keys("Admin@890")
    driver.find_element(By.XPATH, "//button[@id='login']").click()
    time.sleep(2)

    print_status("Navigating to unsubscriber page...", "info")
    driver.find_element(By.ID, "unsubscriber").click()

    print_status("Setting up download parameters...", "info")
    driver.find_element(By.XPATH, "//div[@class='panel-heading']//button[2]").click()
    time.sleep(2)
    driver.find_element(By.XPATH, "//input[@id='global_un_s']").click()
    time.sleep(1)

    print_status("Initiating download...", "info")
    driver.find_element(By.XPATH, "//button[normalize-space()='Download']").click()

    # Wait for the download to complete with progress bar (using purple gradient)
    try:
        wait_for_download_with_progress(path_dir, timeout=100, color_scheme="purple")
        print_status("Download process completed successfully!", "success")
    except TimeoutError as e:
        print_status(f"Download timeout: {str(e)}", "error")

except Exception as e:
    print_status(f"Error during execution: {str(e)}", "error")

finally:
    # Always close the browser to clean up resources
    try:
        if 'driver' in locals():
            print_status("Closing browser...", "info")
            driver.quit()
    except Exception as close_error:
        print_status(f"Error closing browser: {str(close_error)}", "error")