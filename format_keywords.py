import pandas as pd
import os
import argparse
from pathlib import Path
import math

def format_keywords_with_splitting(input_file, column_name, output_dir=None, suffix="[TI]", max_keywords=2048):
    """
    Reads keywords from an Excel file, formats them for search queries, and splits into multiple files.
    
    Args:
        input_file (str): Path to the Excel file containing keywords
        column_name (str): Name of the column containing keywords
        output_dir (str, optional): Directory to save the formatted output files
        suffix (str, optional): Suffix to add to each keyword, default is "[TI]"
        max_keywords (int, optional): Maximum number of keywords per file, default is 2048
    
    Returns:
        list: List of paths to the created output files
    """
    try:
        # Read the Excel file
        print(f"Reading keywords from {input_file}...")
        df = pd.read_excel(input_file)
        
        # Check if the column exists
        if column_name not in df.columns:
            print(f"Error: Column '{column_name}' not found in the Excel file.")
            print(f"Available columns: {', '.join(df.columns)}")
            return None
        
        # Extract keywords from the specified column
        keywords = df[column_name].dropna().astype(str).tolist()
        
        # Remove any empty strings
        keywords = [k.strip() for k in keywords if k.strip()]
        
        if not keywords:
            print("No keywords found in the specified column.")
            return None
        
        total_keywords = len(keywords)
        print(f"Found {total_keywords} keywords.")
        
        # Calculate how many files we need
        num_files = math.ceil(total_keywords / max_keywords)
        print(f"Splitting into {num_files} files with maximum {max_keywords} keywords each.")
        
        # Prepare output directory
        if output_dir:
            output_path = Path(output_dir)
            # Create the directory if it doesn't exist
            output_path.mkdir(parents=True, exist_ok=True)
        else:
            # Use current directory if no output directory specified
            output_path = Path.cwd()
        
        output_files = []
        
        # Process keywords in chunks
        for i in range(num_files):
            start_idx = i * max_keywords
            end_idx = min((i + 1) * max_keywords, total_keywords)
            
            # Get current chunk of keywords
            current_keywords = keywords[start_idx:end_idx]
            
            # Format each keyword with quotes and suffix
            formatted_keywords = [f'"{keyword}"{suffix}' for keyword in current_keywords]
            
            # Join with " OR "
            search_query = " OR ".join(formatted_keywords)
            
            # Wrap in parentheses
            search_query = f"({search_query})"
            
            # Create output filename
            file_name = f"keywords_part{i+1}_of_{num_files}.txt"
            file_path = output_path / file_name
            
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(search_query)
                print(f"Part {i+1}: Saved {len(current_keywords)} keywords to {file_path}")
                output_files.append(file_path)
            except PermissionError:
                # If permission error, try saving to desktop
                desktop_path = Path.home() / "Desktop" / file_name
                with open(desktop_path, 'w', encoding='utf-8') as f:
                    f.write(search_query)
                print(f"Permission denied at original location. Saved part {i+1} to desktop instead: {desktop_path}")
                output_files.append(desktop_path)
        
        return output_files
    
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Format keywords from Excel for search queries and split into multiple files.')
    parser.add_argument('input_file', help='Path to the Excel file containing keywords')
    parser.add_argument('column_name', help='Name of the column containing keywords')
    parser.add_argument('--output', '-o', help='Directory to save the formatted output files')
    parser.add_argument('--suffix', '-s', default='[TI]', help='Suffix to add to each keyword, default is "[TI]"')
    parser.add_argument('--max', '-m', type=int, default=2048, help='Maximum number of keywords per file, default is 2048')
    
    args = parser.parse_args()
    
    # Format and split keywords
    output_files = format_keywords_with_splitting(
        args.input_file, 
        args.column_name, 
        args.output, 
        args.suffix,
        args.max
    )
    
    # Print summary if successful
    if output_files:
        print(f"\nSuccessfully created {len(output_files)} files.")

if __name__ == "__main__":
    # If run directly from command line, use argparse
    if len(os.sys.argv) > 1:
        main()
    # If run without arguments, use interactive mode
    else:
        print("=== Keyword Formatter for Search Queries (Split Version) ===")
        
        # Get input file path
        while True:
            input_file = input("Enter path to Excel file: ")
            if os.path.exists(input_file):
                break
            else:
                print(f"File not found: {input_file}")
        
        # Try to read the Excel file to get column names
        try:
            df = pd.read_excel(input_file)
            print(f"\nAvailable columns: {', '.join(df.columns)}")
        except Exception as e:
            print(f"Error reading Excel file: {str(e)}")
            exit(1)
        
        column_name = input("Enter column name containing keywords: ")
        
        # Get output directory path
        output_dir = input("Enter output directory path (leave blank for current directory): ")
        
        suffix = input("Enter suffix to add to each keyword (default is [TI]): ")
        if not suffix:
            suffix = "[TI]"
        
        max_keywords = input("Enter maximum keywords per file (default is 2048): ")
        if not max_keywords:
            max_keywords = 2048
        else:
            max_keywords = int(max_keywords)
        
        if not output_dir:
            output_dir = None
        
        # Format and split keywords
        output_files = format_keywords_with_splitting(
            input_file, 
            column_name, 
            output_dir, 
            suffix,
            max_keywords
        )
        
        # Print summary if successful
        if output_files:
            print(f"\nSuccessfully created {len(output_files)} files.")
        
        print("\nPress Enter to exit...")
        input()
