import os
import glob
import pandas as pd

def process_email_verification_results(path, master_hardbounces_path):
    """
    Processes email verification results from CSV files in a directory,
    categorizes emails based on their status, and updates a master hard bounces file.

    Args:
        path (str): The directory containing the CSV files with email verification results.
        master_hardbounces_path (str): The path to the master hard bounces CSV file.
    """

    try:
        os.chdir(path)
    except FileNotFoundError:
        print(f"Error: Directory not found: {path}")
        return

    csv_files = glob.glob('*.csv')
    if not csv_files:
        print(f"Error: No CSV files found in: {path}")
        return

    # Read and concatenate all CSV files into a single DataFrame
    df_hbs = pd.concat([pd.read_csv(f) for f in csv_files], ignore_index=True)
    df_hbs.rename(columns={'email': 'Email'}, inplace=True)

    # Filter emails based on different criteria
    df_st = df_hbs[df_hbs['reasons'].str.contains("PotentialSpamTrap", na=False)]
    df_invalid = df_hbs[df_hbs['result'].str.contains("Invalid", na=False)]
    df_highrisk = df_hbs[df_hbs['status'].str.contains("HighRisk", na=False)]
    df_lowrisk = df_hbs[df_hbs['status'].str.contains("LowRisk", na=False)]
    df_valid = df_hbs[df_hbs['result'].str.contains("Valid", na=False)]

    # Save SpamTraps and Invalid emails to separate CSV files
    df_st.Email.to_csv('SpamTraps.csv', index=False)
    df_invalid.Email.to_csv('Invalid.csv', index=False)

    # Combine invalid and spam trap emails
    df_concat_invalid = pd.concat([df_invalid, df_st], axis=0)

    # Read the master hard bounces file
    try:
        df_hb_all = pd.read_csv(master_hardbounces_path)
    except FileNotFoundError:
        print(f"Error: Master hard bounces file not found: {master_hardbounces_path}")
        return
    except pd.errors.EmptyDataError:
        print(f"Warning: Master hard bounces file is empty: {master_hardbounces_path}")
        df_hb_all = pd.DataFrame(columns=['Email'])

    # Combine valid and low-risk emails
    df_concat_valids = pd.concat([df_valid, df_lowrisk], axis=0)

    # Combine all invalid emails (including spam traps and master hard bounces)
    df_concat_allinvalids = pd.concat([df_concat_invalid, df_hb_all], axis=0)

    # Update the master hard bounces file
    df_concat_allinvalids.Email.to_csv(master_hardbounces_path, mode='w+', index=False)
    print(f"Master hard bounces file updated at: {master_hardbounces_path}")


if __name__ == "__main__":
    path = input("Enter the directory containing the CSV files: ")
    master_hardbounces_path = "H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv"
    process_email_verification_results(path, master_hardbounces_path)
