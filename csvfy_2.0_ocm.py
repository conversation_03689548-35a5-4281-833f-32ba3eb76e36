from tqdm import tqdm
import os
import glob 
import pandas as pd 
import numpy as np
import re
import warnings
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

path = input("Loc: ")
import re

# Given path
path = path

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    desired_segment = match.group(1)
    print(desired_segment)
else:
    print("Desired segment not found")


csn = desired_segment #input('Enter csn: ') 

sp_filter = csn #input("Csn/ws: ")

os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs/mw')


#list all csv files only
csv_files = glob.glob('*.{}'.format('csv'))
csv_files
df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)
df = df_concat
status = ["unsubscribed"]
unsub_df = df[df['status'].isin(status)]
unsub_df1 = unsub_df.drop_duplicates(subset='email') #newline added
unsub_df1.to_csv('mw_list_unsubscriber.csv', encoding = 'utf-8-sig', index=False) #newline added
unsub_df2 = pd.read_csv('mw_list_unsubscriber.csv') #newline
unsub_df2.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True) #changed
unsub_dfg = unsub_df2.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber'))
os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs')
unsub_dfg.to_csv('mw_unsubscribers.csv', index=False)

os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs/port1')
csv_files = glob.glob('*.{}'.format('csv'))
csv_files
unsub_mgport = pd.concat([pd.read_csv(f, on_bad_lines='skip', encoding = 'latin') for f in csv_files], ignore_index=True)
#unsub_mgport = pd.read_csv('unsubcriber_sheet.csv', encoding = 'latin')
unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
unsub_mgportg['Conference Name'] = unsub_mgportg['Conference Name'].fillna('Global Unsubscriber')
unsub_mgportg.drop_duplicates(subset='Email', inplace=True)
os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs')
unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)

os.chdir(r"H:/Master Bounces and Unsubs/Postpanel Unsubs")

csv_files = glob.glob('*.{}'.format('csv'))
csv_files
glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))
glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True)
glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)
#sp_filter = input("Csn/ws: ")
options = ['Global Unsubscriber', sp_filter]
unsub_df3 = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]
unsub_df3.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
unsub_df3.drop(['Conference Name'], axis=1, inplace=True)
unsub_df3[['Email']] = unsub_df3[['Email']].applymap(lambda x:x.lower())
unsub_df3.to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv", index=False)

#path = input("Loc: ")
csn = sp_filter #input("Enter csname: ")

os.chdir(path)

csv_files = glob.glob('*.{}'.format('csv'))
csv_files
os.makedirs(("processing"), exist_ok=True)
df_concat = pd.concat([pd.read_csv(f, low_memory=False, encoding='utf-8-sig') for f in csv_files ], ignore_index=True)
df_concat.rename(columns= {'Author Name':'Name', 'col1':'Name', 'col2': 'Article Title', 'email': 'Email', 'name': 'Name'}, inplace=True)
df_concat.dropna(subset='Email', inplace=True)
df_concat.to_csv("./processing/"+csn+"-merged_unfiltered.csv", mode='w+', encoding='utf-8-sig', index=False)
df_hb = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv", on_bad_lines='skip')
df_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Unsubscribes.csv")
df_rep = pd.read_csv("H:/Master Bounces and Unsubs/Replied Ext/Prev_Rep_bounces_csv/"+csn+"_replied_bouncers.csv")
df_pp_gl_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv")
df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
df_concat_unsubs[['Email']] = df_concat_unsubs[['Email']].applymap(lambda x:x.lower())
df_concat = pd.read_csv(path+"./processing/"+csn+"-merged_unfiltered.csv", low_memory=False)
df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
df = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_rep.Email))]
if 'Article Title' not in df.columns:
    df['Article Title'] = 0
df.drop(['Article Title'], axis=1, inplace=True)
df.to_csv("./processing/"+csn+"-merged_deduped.csv", mode='w+',encoding='utf-8-sig', index=False)
df = pd.read_csv("./processing/"+csn+"-merged_deduped.csv", low_memory=False, usecols = ["Name", "Email"])
df1 = df.replace(r'^\s*$', np.nan, regex=True)
df2 = df1.fillna('Colleague')
result = df2.drop_duplicates(subset = 'Email')
result.to_csv("./processing/"+csn+"-merged.csv", mode = 'w+', encoding='utf-8-sig', index=False)
df = pd.read_csv("./processing/"+csn+"-merged.csv")

print(len(df))

os.remove("./processing/"+csn+"-merged_deduped.csv")
os.remove("./processing/"+csn+"-merged_unfiltered.csv")
#os.rename("./processing/"+csn+"-merged_prop_final.csv", "./processing/"+csn+"-merged.csv")
os.remove("./processing/"+csn+"-merged.csv")
os.rmdir("./processing")

import random #weights=[1, 1], 
subj_list = ["Join the Inner Circle: Organizing Committee Member Invitation for a Game-Changing [CCT_CSNAME] Conference", "Make a Difference: Become a Committee Member for a Groundbreaking [CCT_CSNAME] Conference", "Calling All Experts! Join our Organizing Committee for a Leading [CCT_CSNAME] Conference", "Join the Organizing Committee for our Cutting-Edge [CCT_CSNAME] Event", "Contribute to the Success of a Premier [CCT_CSNAME] Conference as a Committee Member", "Your Expertise Needed: Join the Committee for a Game-Changing [CCT_CSNAME] gathering", "Join the Brainpower Behind our Revolutionary [CCT_CSNAME] Conference!", "Be Part of Our Organizing Committee for a Prestigious [CCT_CSNAME]!", "Your Expertise Needed: Invitation to Join the Organizing Committee for a Premier [CCT_CSNAME] Congress", "Be a Key Player: Join the Organizing Committee for a Dynamic [CCT_CSNAME] Congress", "Join us as a Committee Member for our Prestigious [CCT_CSNAME]!", "Become an Organizing Committee Member for an Exciting [CCT_CSNAME]!", "Shape the Future: Join the Organizing Committee for an Innovative Scientific Conference", "Help us Engineer Success as an Organizing Committee Member for a Scientific Conference"]

# Function to replace placeholders in the subject list
def format_subject(subject, csn):
    return subject.replace("[CCT_CSNAME]", csn)

# Create a list of formatted subjects
formatted_subj_list = [format_subject(subj, csn) for subj in subj_list]

# Randomly assign subjects to the DataFrame
df["Subject"] = pd.Series(random.choices(formatted_subj_list, k=len(df)), index=df.index)


os.makedirs(("result"), exist_ok=True)

# Get the number of rows in the dataframe
n_rows = len(df)
n_temps = 1 #int(input("No. of temps: "))
# Calculate the size of each chunk
chunk_size = n_rows // n_temps

for i in tqdm(range(1), colour='cyan'):
    # Split the dataframe into five chunks and save them as separate csv files
    for i in range(n_temps):
        start = i * chunk_size
        end = (i + 1) * chunk_size if i < n_temps-1 else n_rows
        chunk = df[start:end]
        chunk.to_csv(f"./result/{csn}-{i+1}_OCM.csv", encoding='utf-8-sig', mode='w+', index=False)
pass
print("Completed!")