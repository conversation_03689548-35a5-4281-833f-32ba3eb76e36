import os
import pandas as pd
import re
import numpy as np
import datetime
import subprocess
import time
from tqdm import tqdm

def trim_all_columns(df):
    """
    Trim whitespace from ends of each value across all series in dataframe
    """
    trim_strings = lambda x: x.strip('.,;() ') if isinstance(x, str) else x
    return df.applymap(trim_strings)

def find_email(text):
    """
    Extract valid email addresses using regex
    """
    email = re.findall(r'^[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*@[a-zA-Z0-9._-]+(?:\.[a-zA-Z0-9._-]+)*$', str(text))
    return ",".join(email)

def clean_filename(name):
    """
    Clean a string to be used as a filename by removing invalid characters
    and replacing spaces around hyphens.
    """
    if pd.isna(name) or name == '':
        return 'Unknown'
    
    # Convert to string and strip whitespace
    name = str(name).strip()
    
    # Replace spaces around hyphens (e.g., 'M-Nano - 2025' becomes 'M-Nano-2025')
    name = re.sub(r'\s*-\s*', '-', name)
    
    # Remove or replace invalid filename characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '_')
    
    # Remove extra spaces and replace with underscores
    name = re.sub(r'\s+', '_', name)
    
    return name

def main():
    """Main function to run the separation process."""
    print("Excel File Separator by Conference Short Name")
    print("=" * 60)
    
    # Default file path based on user's request
    default_file_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\replied bounces\Replied Bounces 07-07-2025.xlsx"
    
    # Ask user for file path or use default
    print(f"Default file path: {default_file_path}")
    user_input = input("Press Enter to use default path, or enter a different path: ").strip()
    
    file_path = user_input if user_input else default_file_path
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        return
    
    try:
        # Read the Excel file
        print("Reading Excel file...")
        df = pd.read_excel(file_path)
        
        print(f"✓ Successfully loaded {len(df)} rows")
        print(f"Available columns: {', '.join(df.columns.tolist())}")
        
        # Check if required columns exist
        if 'Conference Short Name' not in df.columns:
            print("Error: 'Conference Short Name' column not found in the file.")
            return
        
        # Get the directory of the input file
        input_dir = os.path.dirname(file_path)
        
        # Create separated directory
        separated_dir = os.path.join(input_dir, "separated")
        os.makedirs(separated_dir, exist_ok=True)
        print(f"✓ Created separated directory: {separated_dir}")
        
        # Process Email and Alternate Email columns (similar to irep_2.0.py)
        print("Combining Email and Alternate Email columns...")
        
        # Read Email column data
        df1 = df[['Conference Short Name', 'Name', 'Email']].copy()
        df1 = df1[df1['Email'].notna() & (df1['Email'] != '')]
        
        # Read Alternate Email column data
        df2 = df[['Conference Short Name', 'Name', 'Alternate Email']].copy()
        df2 = df2[df2['Alternate Email'].notna() & (df2['Alternate Email'] != '')]
        df2.rename(columns={'Alternate Email': 'Email'}, inplace=True)
        
        # Combine both dataframes
        combined_df = pd.concat([df1, df2], ignore_index=True)
        print(f"✓ Combined {len(df1)} Email records and {len(df2)} Alternate Email records")
        print(f"✓ Total combined records: {len(combined_df)}")
        
        # Clean email data (similar to irep_2.0.py)
        print("Cleaning email data...")
        
        # Remove mailto: prefixes and clean email data
        combined_df = combined_df.apply(lambda x: x.str.replace('mailto:', '') if x.dtype == 'object' else x)
        combined_df = combined_df.apply(lambda x: x.str.replace('mailto', '') if x.dtype == 'object' else x)
        combined_df = combined_df.apply(lambda x: x.str.replace(':', ',') if x.dtype == 'object' else x)
        combined_df = combined_df.apply(lambda x: x.str.replace(';', ',') if x.dtype == 'object' else x)
        
        # Remove spaces from email column
        combined_df['Email'] = combined_df['Email'].astype(str).str.replace(' ', '')
        
        # Split emails by comma and expand
        email_split = combined_df['Email'].str.split(',', expand=True)
        
        # Process each email part separately
        all_email_records = []
        
        for i in range(min(3, email_split.shape[1])):  # Process up to 3 email parts
            temp_df = combined_df[['Conference Short Name', 'Name']].copy()
            temp_df['Email'] = email_split.iloc[:, i]
            temp_df = temp_df[temp_df['Email'].notna() & (temp_df['Email'] != '')]
            all_email_records.append(temp_df)
        
        # Combine all email records
        if all_email_records:
            final_df = pd.concat(all_email_records, ignore_index=True)
        else:
            final_df = combined_df
        
        # Trim all columns
        final_df = trim_all_columns(final_df)
        
        # Apply email validation
        final_df['Email'] = final_df["Email"].apply(lambda x: find_email(x))
        final_df['Email'] = final_df['Email'].str.lower()
        
        # Remove duplicates and empty emails
        final_df = final_df.drop_duplicates(subset="Email")
        final_df.replace(r'^\s*$', np.nan, regex=True, inplace=True)
        final_df = final_df.dropna(subset=['Email'])
        
        print(f"✓ Email data cleaned. {len(final_df)} valid records remaining")
        
        # Get unique values from Conference Short Name column
        unique_values = final_df['Conference Short Name'].dropna().unique()
        print(f"Found {len(unique_values)} unique values in 'Conference Short Name' column")
        
        # Process each unique value
        print(f"Processing {len(unique_values)} conference(s)...")
        
        summary_data = []
        
        for value in tqdm(unique_values, desc="Processing conferences", colour='green'):
            # Filter data for this value
            filtered_df = final_df[final_df['Conference Short Name'] == value].copy()
            
            # Keep only Name and Email columns (as per irep_2.0.py)
            if 'Name' in filtered_df.columns and 'Email' in filtered_df.columns:
                output_df = filtered_df[['Name', 'Email']].copy()
            elif 'Email' in filtered_df.columns:
                output_df = filtered_df[['Email']].copy()
            else:
                continue
            
            # Clean the value for filename
            clean_value = clean_filename(value)
            
            # Create output filename
            output_filename = f"{clean_value}_replied_bouncers.csv"
            output_path = os.path.join(separated_dir, output_filename)
            
            # Save the filtered data
            output_df.to_csv(output_path, index=False, encoding='utf-8-sig')
            
            # Add to summary
            summary_data.append((str(value), output_filename, len(output_df)))
        
        # Display summary
        print("\nSeparation Summary:")
        print("-" * 80)
        for conf_name, filename, row_count in summary_data:
            print(f"Conference: {conf_name}")
            print(f"Filename: {filename}")
            print(f"Rows: {row_count}")
            print("-" * 40)
        
        print(f"\n✓ Separation complete!")
        print(f"Files saved to: {separated_dir}")
        
    except Exception as e:
        print(f"Error processing file: {str(e)}")

if __name__ == "__main__":
    main()
