import os
from selenium import webdriver 
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By 
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriver<PERSON>ait

import pyautogui
import time
timestr = time.strftime("%d%m%Y")
options = webdriver.ChromeOptions()
options.add_experimental_option("excludeSwitches", ["enable-logging"]) 
options.add_argument("--headless")  # Run in headless mode
options.add_argument("--disable-gpu")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")
prefs = {
"download.default_directory": r"H:\Master Bounces and Unsubs\Postpanel Unsubs\port1",
"download.prompt_for_download": False,
"download.directory_upgrade": True
}
options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(options)
driver.maximize_window()
#driver.set_window_size(1920, 1080)
driver.get("https://magnusgroup.port1.in/")
driver.find_element(By.ID, "name").send_keys("admin")
driver.find_element(By.ID, "password").send_keys("Admin@890")
driver.find_element(By.XPATH, "//button[@id='login']").click()
pyautogui.keyDown('ctrl')
pyautogui.press('-')
pyautogui.press('-')
pyautogui.keyUp('ctrl')
driver.find_element(By.LINK_TEXT, "Unsubscribers").click()
driver.find_element(By.XPATH, "//div[@class='panel-heading']//button[2]").click()
time.sleep(2)
driver.find_element(By.XPATH, "//input[@id='global_un_s']").click()
time.sleep(1)
driver.find_element(By.XPATH, "//button[normalize-space()='Download']").click()
time.sleep(5)
#driver.switch_to.window(driver.window_handles[1])
print('Downloaded successfully!')