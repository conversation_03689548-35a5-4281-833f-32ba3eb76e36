import os
import glob
import pandas as pd
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

def create_gradient_progress():
    """Create a progress bar with gradient colors"""
    return Progress(
        TextColumn("[bold blue]{task.description}"),
        BarColumn(bar_width=None, style="bar.back", complete_style="green", finished_style="bright_green"),
        "[progress.percentage]{task.percentage:>3.0f}%",
        TimeElapsedColumn(),
        TimeRemainingColumn(),
        console=Console()
    )

def concatenate_postpanel_unsubs():
    """
    Concatenate multiple CSV files from Mathews Postpanel Unsubs directory
    """
    console = Console()
    
    # Display header
    header_text = Text("Postpanel Unsubscribers CSV Concatenator", style="bold magenta")
    console.print(Panel(header_text, expand=False))
    
    # Directory path
    directory_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\postpanel"
    
    # Check if directory exists
    if not os.path.exists(directory_path):
        console.print(f"[red]Error: Directory not found: {directory_path}[/red]")
        return
    
    # Change to the directory
    original_dir = os.getcwd()
    os.chdir(directory_path)
    
    try:
        # Find all CSV files, excluding already concatenated files
        all_csv_files = glob.glob('*.csv')
        csv_files = [f for f in all_csv_files if not f.startswith('mathews_postpanel_unsubs_concatenated_')]

        if not csv_files:
            console.print("[red]No CSV files found in the directory (excluding already concatenated files).[/red]")
            return
        
        console.print(f"[green]Found {len(csv_files)} CSV files:[/green]")
        for file in csv_files:
            console.print(f"  • {file}")
        
        # Initialize list to store dataframes
        all_dfs = []
        total_rows = 0
        
        # Create progress bar
        with create_gradient_progress() as progress:
            task = progress.add_task("Reading CSV files...", total=len(csv_files))
            
            for file in csv_files:
                try:
                    # Read CSV file
                    df = pd.read_csv(file, encoding='utf-8-sig', on_bad_lines='skip')
                    
                    # Standardize column names
                    if 'Email ID' in df.columns:
                        df.rename(columns={'Email ID': 'Email'}, inplace=True)
                    
                    all_dfs.append(df)
                    total_rows += len(df)
                    
                    progress.console.print(f"  ✓ Read {file}: {len(df)} rows")
                    
                except Exception as e:
                    progress.console.print(f"  ✗ Error reading {file}: {str(e)}")
                
                progress.advance(task)
        
        if not all_dfs:
            console.print("[red]No data could be read from any CSV files.[/red]")
            return
        
        # Concatenate all dataframes
        console.print("\n[yellow]Concatenating data...[/yellow]")
        combined_df = pd.concat(all_dfs, ignore_index=True)

        # Create merged directory if it doesn't exist
        merged_dir = "merged"
        os.makedirs(merged_dir, exist_ok=True)

        # Save the main concatenated file
        main_output_filename = os.path.join(merged_dir, "mathews_postpanel_unsubs_concatenated.csv")
        console.print(f"\n[yellow]Saving main concatenated file to merged folder...[/yellow]")
        combined_df.to_csv(main_output_filename, index=False, encoding='utf-8-sig')

        # Split data by 'To' column
        console.print(f"\n[yellow]Splitting data by 'To' column...[/yellow]")

        # Create separated directory inside merged folder for split files
        separated_dir = os.path.join(merged_dir, "separated")
        os.makedirs(separated_dir, exist_ok=True)

        unique_to_values = combined_df['To'].unique()
        split_files = []

        with create_gradient_progress() as progress:
            split_task = progress.add_task("Creating split files...", total=len(unique_to_values))

            for to_value in unique_to_values:
                # Filter data for this 'To' value
                filtered_df = combined_df[combined_df['To'] == to_value]

                # Extract conference code from brackets or use special handling for Global Unsubscriber
                if "Global Unsubscriber" in to_value:
                    safe_filename = "Global_Unsubscriber"
                else:
                    # Extract text within parentheses
                    import re
                    match = re.search(r'\(([^)]+)\)', to_value)
                    if match:
                        conference_code = match.group(1)
                        safe_filename = f"{conference_code}_Unsubscribes"
                    else:
                        # Fallback to original method if no brackets found
                        safe_filename = "".join(c for c in to_value if c.isalnum() or c in (' ', '-', '_')).rstrip()
                        safe_filename = safe_filename.replace(' ', '_')[:50]

                # Create output filename in separated directory
                split_filename = os.path.join(separated_dir, f"{safe_filename}.csv")

                # Save the split file
                filtered_df.to_csv(split_filename, index=False, encoding='utf-8-sig')
                split_files.append((split_filename, len(filtered_df), to_value))

                progress.console.print(f"  ✓ Created {safe_filename}.csv: {len(filtered_df)} rows")
                progress.advance(split_task)
        
        # Display summary
        split_files_summary = "\n".join([f"  • {os.path.basename(filename)}: {row_count} rows ({to_val})"
                                       for filename, row_count, to_val in split_files])

        summary_text = f"""
[bold green]Concatenation and Splitting Complete![/bold green]

[bold]Summary:[/bold]
• Files processed: {len(csv_files)}
• Total rows: {len(combined_df)}
• Main output file: merged/{os.path.basename(main_output_filename)}
• Split files created: {len(split_files)} (saved in merged/separated/)

[bold]Split files by 'To' column (in merged/separated/):[/bold]
{split_files_summary}

[bold]Columns in output:[/bold]
{', '.join(combined_df.columns.tolist())}
        """

        console.print(Panel(summary_text, title="Results", expand=False))
        
    except Exception as e:
        console.print(f"[red]An error occurred: {str(e)}[/red]")
    
    finally:
        # Change back to original directory
        os.chdir(original_dir)

if __name__ == "__main__":
    concatenate_postpanel_unsubs()
