import os
import glob
import pandas as pd
import numpy as np
import pandas.io.formats.excel
import math
pd.io.formats.excel.ExcelFormatter.header_style = None

path = input("Enter path of files for combining: ")
os.chdir(path)

csv_files = glob.glob('*.{}'.format('csv'))

all_emails = pd.DataFrame(columns=['Email'])  # Initialize an empty DataFrame

for f in csv_files:
    try:
        df = pd.read_csv(f, on_bad_lines='skip', low_memory=False)
        
        # Check for 'Email' or 'email' header (case-insensitive)
        if 'Email' in df.columns:
            emails = df['Email']
        elif 'email' in df.columns:
            emails = df['email']
        else:
            print(f"Warning: File {f} does not contain 'Email' or 'email' column. Skipping.")
            continue

        # Create a temporary DataFrame to hold the emails
        temp_df = pd.DataFrame({'Email': emails})
        
        # Concatenate to the main DataFrame
        all_emails = pd.concat([all_emails, temp_df], ignore_index=True)

    except pd.errors.EmptyDataError:
        print(f"Warning: File {f} is empty. Skipping.")
    except pd.errors.ParserError:
        print(f"Warning: File {f} has parsing errors. Skipping.")
    except Exception as e:
        print(f"An error occurred while processing file {f}: {e}")

# Remove duplicates and NaN values
all_emails.drop_duplicates(subset='Email', inplace=True)
all_emails.dropna(subset='Email', inplace=True)

# Save the combined emails to a CSV file
all_emails.Email.to_csv("Email.csv", index=False)
print("Combined!")
