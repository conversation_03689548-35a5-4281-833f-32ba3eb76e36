import warnings
import os
import pandas as pd
import glob
from tqdm import tqdm
from datetime import date
import zipfile
import shutil

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

base_dir = r"H:\Master Bounces and Unsubs"
bounces_dir = os.path.join(base_dir, "Bounces")
master_dir = os.path.join(base_dir, "Master Bounces and Unsubs")
master_tmp_dir = os.path.join(base_dir, "Master_bounces_tmp")
weekly_dir = os.path.join(base_dir, "Weekly Bounces and Unsubs")
postpanel_unsubs_dir = os.path.join(base_dir, "Postpanel Unsubs")
mw_unsubs_dir = os.path.join(postpanel_unsubs_dir, "mw")
master_softbounces_dir = os.path.join(master_dir, "Master_Softbounces")

def zip_bounces_folder():
    os.chdir(bounces_dir)
    fantasy_zip = zipfile.ZipFile(os.path.join(bounces_dir, 'Bounces collection.zip'), 'w')
    for folder, subfolders, files in os.walk(bounces_dir):
        for file in files:
            if folder.endswith(('.exe')):
                fantasy_zip.write(os.path.join(folder, file), os.path.relpath(os.path.join(folder,file), bounces_dir), compress_type = zipfile.ZIP_DEFLATED)
    fantasy_zip.close()
    current_date = date.today()
    str_current_date = str(current_date)
    file_name = f"Bounces collection {str_current_date}.zip"
    os.rename('Bounces collection.zip', file_name)

def process_email_provider(provider_name, input_subdir, output_prefix, hb_options, sb_options, unsub_options, hb_col, sb_col, unsub_col, email_col='to', message_category_col='messagecategory', reason_col='reason', event_col='event', st_text_col='st_text', type_col='type', raw_rcpt_to_col='raw_rcpt_to'):
    input_path = os.path.join(bounces_dir, provider_name, input_subdir)
    os.chdir(input_path)
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print(f"No CSV files found in {input_path}")
        return

    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode') for f in csv_files], ignore_index=True)

    if email_col in df_concat.columns and unsub_col in df_concat.columns:
        unsub_df = df_concat[df_concat[unsub_col].isin(unsub_options)].copy()
        if not unsub_df.empty:
            unsub_df[['Email']] = unsub_df[email_col]
            unsub_dfd = unsub_df.drop(columns=[col for col in [unsub_col, message_category_col, event_col, st_text_col, type_col, reason_col] if col in unsub_df.columns], errors='ignore')
            unsub_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            unsub_dfd[['Email']].to_csv(os.path.join(input_path, f"{output_prefix}_unsubs.csv"), index=False)

    if email_col in df_concat.columns and sb_col in df_concat.columns:
        sb_df = df_concat[df_concat[sb_col].isin(sb_options)].copy()
        if not sb_df.empty:
            sb_df[['Email']] = sb_df[email_col]
            sb_dfd = sb_df.drop(columns=[col for col in [sb_col, message_category_col, event_col, st_text_col, type_col, reason_col] if col in sb_df.columns], errors='ignore')
            sb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            sb_dfd[['Email']].to_csv(os.path.join(input_path, f"{output_prefix}_sb.csv"), index=False)

    if email_col in df_concat.columns and hb_col in df_concat.columns:
        hb_df = df_concat[df_concat[hb_col].isin(hb_options)].copy()
        if not hb_df.empty:
            hb_df[['Email']] = hb_df[email_col]
            hb_dfd = hb_df.drop(columns=[col for col in [hb_col, message_category_col, event_col, st_text_col, type_col, reason_col] if col in hb_df.columns], errors='ignore')
            hb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            hb_dfd[['Email']].to_csv(os.path.join(input_path, f"{output_prefix}_hb.csv"), index=False)
    elif email_col in df_concat.columns and reason_col in df_concat.columns and hb_options:
        hb_df = df_concat[df_concat[reason_col].str.contains('|'.join(hb_options), case=False, na=False)].copy()
        if not hb_df.empty:
            hb_df[['Email']] = hb_df[email_col]
            hb_dfd = hb_df.drop(columns=[col for col in [hb_col, message_category_col, event_col, st_text_col, type_col, reason_col] if col in hb_df.columns], errors='ignore')
            hb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            hb_dfd[['Email']].to_csv(os.path.join(input_path, f"{output_prefix}_hb.csv"), index=False)

    elif email_col in df_concat.columns and type_col in df_concat.columns and reason_col in df_concat.columns and hb_options:
        hb_df_allb = df_concat[df_concat[type_col].isin(['bounce'])].copy()
        hb_df = hb_df_allb[hb_df_allb[reason_col].str.contains('|'.join(hb_options), case=False, na=False)].copy()
        if not hb_df.empty:
            hb_df[['Email']] = hb_df[email_col]
            hb_dfd = hb_df.drop(columns=[col for col in [hb_col, message_category_col, event_col, st_text_col, type_col, reason_col] if col in hb_df.columns], errors='ignore')
            hb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            hb_dfd[['Email']].to_csv(os.path.join(input_path, f"{output_prefix}_hb.csv"), index=False)

        if email_col in hb_df_allb.columns and reason_col in hb_df_allb.columns:
            sb_df = hb_df_allb[~(hb_df_allb['Email'].isin(hb_df['Email']))].copy()
            if not sb_df.empty:
                sb_dfd = sb_df.drop(columns=[col for col in [hb_col, message_category_col, event_col, st_text_col, type_col, reason_col] if col in sb_df.columns], errors='ignore')
                sb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
                sb_dfd[['Email']].to_csv(os.path.join(input_path, f"{output_prefix}_sb.csv"), index=False)

def process_sendgrid():
    input_path = os.path.join(bounces_dir, "sg.exe")
    os.chdir(input_path)
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print(f"No CSV files found in {input_path}")
        return

    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip') for f in csv_files], ignore_index=True)
    if 'email' in df_concat.columns and 'event' in df_concat.columns and 'reason' in df_concat.columns:
        df_concat.rename(columns={'email': 'Email'}, inplace=True)
        unsub_filtr = ["spamreport", "unsubscribed", "complaint"]
        df_unsubs = df_concat[df_concat['event'].isin(unsub_filtr)].copy()
        if not df_unsubs.empty:
            df_unsubsdc = df_unsubs.drop(["event","reason"], axis=1)
            df_unsubsdc.to_csv(os.path.join(input_path, "sg_unsubs.csv"), index=False)

        allb_filtr = ["bounce"]
        df_allb = df_concat[df_concat['event'].isin(allb_filtr)].copy()

        hb_options = ["User unknown", "service disabled", "does not exist", "Address unknown", "User not found",
                      "no mailbox", "no mail-box", "no mail", "unrecognized address", "mailbox is unavailable",
                      "mailbox unavailable"]
        df_hb = df_allb[df_allb["reason"].str.contains('|'.join(hb_options), case=False, na=False)].copy()
        if not df_hb.empty:
            df_hbdc = df_hb.drop(["event", "reason"], axis=1)
            df_hbdc.to_csv(os.path.join(input_path, "sg_hb.csv"), index=False)

        df_sb = df_allb[~(df_allb['Email'].isin(df_hb['Email']))].copy()
        if not df_sb.empty:
            df_sbdc = df_sb.drop(["event", "reason"], axis=1)
            df_sbdcd = df_sbdc.drop_duplicates(subset="Email", keep="first", inplace=False)
            df_sbdcd.to_csv(os.path.join(input_path, "sg_sb.csv"), index=False)

def process_sparkpost():
    input_path = os.path.join(bounces_dir, "sp.exe")
    os.chdir(input_path)
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print(f"No CSV files found in {input_path}")
        return

    df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
    if 'raw_rcpt_to' in df_concat.columns and 'type' in df_concat.columns and 'reason' in df_concat.columns:
        df_concat.rename(columns={'raw_rcpt_to': 'Email'}, inplace=True)
        unsub_filtr = ["list_unsubscribe", "spam_complaint"]
        df_unsubs = df_concat[df_concat['type'].isin(unsub_filtr)].copy()
        if not df_unsubs.empty:
            df_unsubsdc = df_unsubs.drop(["type","reason"], axis=1)
            df_unsubsdc.to_csv(os.path.join(input_path, "sp_unsubs.csv"), index=False)

        allb_filtr = ["bounce"]
        df_allb = df_concat[df_concat['type'].isin(allb_filtr)].copy()

        hb_options = ["does not exist", "Does Not Exist", "mailbox unavailable", "Mailbox unavailable",
                      "mailbox is unavailable", "no mail-box", "Recipient not found",
                      "email account that you tried to reach is disabled", "no mailbox", "No mailbox"]
        df_hb = df_allb[df_allb["reason"].str.contains('|'.join(hb_options), case=False, na=False)].copy()
        if not df_hb.empty:
            df_hbdc = df_hb.drop(["type", "reason"], axis=1)
            df_hbdc.to_csv(os.path.join(input_path, "sp_hb.csv"), index=False)

        df_sb = df_allb[~(df_allb['Email'].isin(df_hb['Email']))].copy()
        if not df_sb.empty:
            df_sbdc = df_sb.drop(["type", "reason"], axis=1)
            df_sbdcd = df_sbdc.drop_duplicates(subset="Email", keep="first", inplace=False)
            df_sbdcd.to_csv(os.path.join(input_path, "sp_sb.csv"), index=False)

def process_sendinblue():
    input_path = os.path.join(bounces_dir, "sib.exe")
    os.chdir(input_path)
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print(f"No CSV files found in {input_path}")
        return

    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip') for f in csv_files], ignore_index=True)
    if 'email' in df_concat.columns and 'st_text' in df_concat.columns:
        df_concat.rename(columns={'email': 'Email'}, inplace=True)
        hb_options = ["Hard bounce", "Invalid"]
        hb_df = df_concat[df_concat['st_text'].isin(hb_options)].copy()
        if not hb_df.empty:
            hbd_df = hb_df.drop(['st_text'], axis=1)
            hbd_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
            hbd_df.to_csv(os.path.join(input_path, "sib_hb.csv"), index=False)

        sb_options = ["Soft bounce", "Deferred", "Blocked"]
        sb_df = df_concat[df_concat['st_text'].isin(sb_options)].copy()
        if not sb_df.empty:
            sbd_df = sb_df.drop(['st_text'], axis=1)
            sbd_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
            sbd_df.to_csv(os.path.join(input_path, "sib_sb.csv"), index=False)

        unsub_options = ["Complaint", "Unsubscribed"]
        unsub_df = df_concat[df_concat['st_text'].isin(unsub_options)].copy()
        if not unsub_df.empty:
            unsubd_df = unsub_df.drop(['st_text'], axis=1)
            unsubd_df.drop_duplicates(subset='Email', keep='first', inplace=True, ignore_index=True)
            unsubd_df.to_csv(os.path.join(input_path, "sib_unsubs.csv"), index=False)

def process_elasticemail(input_subdir, output_prefix):
    input_path = os.path.join(bounces_dir, "ee.exe", input_subdir)
    os.chdir(input_path)
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print(f"No CSV files found in {input_path}")
        return

    df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode') for f in csv_files], ignore_index=True)
    if 'to' in df_concat.columns and 'eventtype' in df_concat.columns and 'messagecategory' in df_concat.columns:
        df_concat[['to', 'eventtype', 'messagecategory']].to_csv(os.path.join(input_path, "ee_allb.csv"), index=False)
        df = pd.read_csv(os.path.join(input_path, "ee_allb.csv"), usecols= ['to', 'eventtype', 'messagecategory'])
        df.rename(columns={'to': 'Email'}, inplace=True)

        unsub_options = ["AbuseReport", "Unsubscribed"]
        unsub_df = df[df['eventtype'].isin(unsub_options)].copy()
        if not unsub_df.empty:
            unsub_dfd = unsub_df.drop(["eventtype", "messagecategory"], axis=1)
            unsub_dfd.to_csv(os.path.join(input_path, f"{output_prefix}_unsubs.csv"), index=False)

        sb_options = ["Bounced"]
        sb_df = df[df['eventtype'].isin(sb_options)].copy()
        hb_options = ["NoMailbox"]
        hb_df = df[df['messagecategory'].isin(hb_options)].copy()

        if not sb_df.empty:
            sb_dfd = sb_df.drop(["eventtype", "messagecategory"], axis=1)
            hb_dfd = hb_df.drop(["eventtype", "messagecategory"], axis=1)
            sb_dfdd = sb_dfd[~(sb_dfd['Email'].isin(hb_dfd['Email']))].copy()
            sb_dfdd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            sb_dfdd.to_csv(os.path.join(input_path, f"{output_prefix}_sb.csv"), index=False)
            hb_dfd.drop_duplicates(subset="Email", keep='first', inplace=True, ignore_index=False)
            hb_dfd.to_csv(os.path.join(input_path, f"{output_prefix}_hb.csv"), index=False)

for i in tqdm(range(1), desc="Zipping Bounces: "):
    zip_bounces_folder()

for i in tqdm(range(1), desc="Processing ElasticEmail Journals: "):
    process_elasticemail("ee_j.exe", "ee_j")

for i in tqdm(range(1), desc="Processing ElasticEmail Conferences: "):
    process_elasticemail("", "ee")

for i in tqdm(range(1), desc="Processing SendGrid: "):
    process_sendgrid()

for i in tqdm(range(1), desc="Processing Sendinblue: "):
    process_sendinblue()

for i in tqdm(range(1), desc="Processing SparkPost: "):
    process_sparkpost()

for i in tqdm(range(1), desc="Concatenating Weekly Data: "):
    os.chdir(master_dir)
    hb_files = [os.path.join(bounces_dir, provider, f"{provider_prefix}_hb.csv")
                for provider, provider_prefix in [("ee.exe", "ee"), ("sg.exe", "sg"), ("sib.exe", "sib"), ("sp.exe", "sp")]
                if os.path.exists(os.path.join(bounces_dir, provider, f"{provider_prefix}_hb.csv"))]
    hb_df_list = [pd.read_csv(f) for f in hb_files]
    if hb_df_list:
        wk_hb = pd.concat(hb_df_list, ignore_index=True)
        wk_hb.drop_duplicates(subset="Email", keep="first", inplace=True)
        wk_hb['Email'] = wk_hb['Email'].str.lower()
        wk_hb.to_csv(os.path.join(base_dir, "Weekly_hardbounces.csv"), index=False)

    unsubs_files = [os.path.join(bounces_dir, provider, f"{provider_prefix}_unsubs.csv")
                    for provider, provider_prefix in [("ee.exe", "ee"), ("sg.exe", "sg"), ("sib.exe", "sib"), ("sp.exe", "sp")]
                    if os.path.exists(os.path.join(bounces_dir, provider, f"{provider_prefix}_unsubs.csv"))]
    unsubs_df_list = [pd.read_csv(f) for f in unsubs_files]
    if unsubs_df_list:
        wk_unsub = pd.concat(unsubs_df_list, ignore_index=True)
        wk_unsub.drop_duplicates(subset="Email", keep="first", inplace=True)
        wk_unsub['Email'] = wk_unsub['Email'].str.lower()
        wk_unsub.to_csv(os.path.join(base_dir, "Weekly_unsubscribes.csv"), index=False)

for i in tqdm(range(1), desc="Updating Master Data: "):
    os.makedirs(master_tmp_dir, exist_ok=True)
    shutil.copy(os.path.join(bounces_dir, "ee.exe", "ee_j.exe", "ee_sb_j.csv"), os.path.join(master_tmp_dir, "ee_sb_j.csv"))
    shutil.copy(os.path.join(bounces_dir, "ee.exe", "ee_j.exe", "ee_unsubs_j.csv"), os.path.join(master_tmp_dir, "ee_unsubs_j.csv"))
    shutil.copy(os.path.join(bounces_dir, "sib.exe", "sib_sb.csv"), os.path.join(master_tmp_dir, "sib_sb.csv"))
    shutil.copy(os.path.join(bounces_dir, "ee.exe", "ee_sb.csv"), os.path.join(master_tmp_dir, "ee_sb.csv"))
    shutil.copy(os.path.join(bounces_dir, "sg.exe", "sg_sb.csv"), os.path.join(master_tmp_dir, "sg_sb.csv"))
    shutil.copy(os.path.join(bounces_dir, "sp.exe", "sp_sb.csv"), os.path.join(master_tmp_dir, "sp_sb.csv"))

    os.chdir(master_dir)

    master_hb_path = os.path.join(master_dir, "Master_Hardbounces.csv")
    weekly_hb_path = os.path.join(base_dir, "Weekly_Hardbounces.csv")
    if os.path.exists(master_hb_path) and os.path.exists(weekly_hb_path):
        df_master_hardbounces = pd.read_csv(master_hb_path)
        df_weekly_hardbounces = pd.read_csv(weekly_hb_path)
        pd.concat([df_master_hardbounces, df_weekly_hardbounces]).drop_duplicates(subset="Email", keep="first", inplace=False).to_csv('Master_Hardbounces.csv', index=False)
    elif os.path.exists(weekly_hb_path):
        pd.read_csv(weekly_hb_path).drop_duplicates(subset="Email", keep="first", inplace=False).to_csv('Master_Hardbounces.csv', index=False)

    master_unsubs_path = os.path.join(master_dir, "Master_Unsubscribes.csv")
    weekly_unsubs_path = os.path.join(base_dir, "Weekly_Unsubscribes.csv")
    if os.path.exists(master_unsubs_path) and os.path.exists(weekly_unsubs_path):
        df_master_unsubscribes = pd.read_csv(master_unsubs_path)
        df_weekly_unsubscribes = pd.read_csv(weekly_unsubs_path)
        pd.concat([df_master_unsubscribes, df_weekly_unsubscribes]).drop_duplicates(subset="Email", keep="first", inplace=False).to_csv('Master_Unsubscribes.csv', index=False)
    elif os.path.exists(weekly_unsubs_path):
        pd.read_csv(weekly_unsubs_path).drop_duplicates(subset="Email", keep="first", inplace=False).to_csv('Master_Unsubscribes.csv', index=False)

    os.chdir(master_tmp_dir)

    softbounce_sources = [
        ("Sendinblue_Softbounces.csv", "sib_sb.csv"),
        ("Sendgrid_Softbounces.csv", "sg_sb.csv"),
        ("Elasticemail_Softbounces.csv", "ee_sb.csv"),
        ("Sparkpost_Softbounces.csv", "sp_sb.csv")
    ]
    for excel_file, csv_file in softbounce_sources:
        excel_path = os.path.join(master_tmp_dir, excel_file)
        csv_path = os.path.join(master_tmp_dir, csv_file)
        if os.path.exists(excel_path) and os.path.exists(csv_path):
            sib_excel_df = pd.read_csv(excel_path)
            sib_csv_df = pd.read_csv(csv_path)
            sib_concat_df = pd.concat([sib_excel_df, sib_csv_df], ignore_index=True)
            sib_concat_df.drop_duplicates(subset='Email', keep='first', inplace=True)
            sib_concat_df.to_csv(excel_file, index=False)
        elif os.path.exists(csv_path):
            pd.read_csv(csv_path).drop_duplicates(subset='Email', keep='first', inplace=True).to_csv(excel_file, index=False)

for i in tqdm(range(1), desc="Finalizing and Moving Files: "):
    pd.io.formats.excel.ExcelFormatter.header_style = None

    os.chdir(master_tmp_dir)
    files_to_remove = ["ee_sb.csv", "sg_sb.csv", "sib_sb.csv", "ee_sb_j.csv", "sp_sb.csv"]
    for file in files_to_remove:
        file_path = os.path.join(master_tmp_dir, file)
        if os.path.exists(file_path):
            os.remove(file_path)

    weekly_unsubs_journal_csv = "Weekly_unsubscribes_journal.csv"
    ee_unsubs_j_path = os.path.join(master_tmp_dir, "ee_unsubs_j.csv")
    weekly_unsubs_journal_path = os.path.join(master_tmp_dir, weekly_unsubs_journal_csv)
    if os.path.exists(ee_unsubs_j_path):
        os.rename(ee_unsubs_j_path, weekly_unsubs_journal_path)
        shutil.move(weekly_unsubs_journal_path, weekly_dir)
    elif os.path.exists(weekly_unsubs_journal_path):
        shutil.move(weekly_unsubs_journal_path, weekly_dir)

    os.chdir(base_dir)
    weekly_hb_path = os.path.join(base_dir, "Weekly_hardbounces.csv")
    weekly_unsubs_path = os.path.join(base_dir, "Weekly_unsubscribes.csv")
    if os.path.exists(weekly_hb_path):
        shutil.move(weekly_hb_path, weekly_dir)
    if os.path.exists(weekly_unsubs_path):
        shutil.move(weekly_unsubs_path, weekly_dir)

    weekly_unsubs_csv_path = os.path.join(weekly_dir, "Weekly_unsubscribes.csv")
    weekly_unsubs_xlsx_path = os.path.join(weekly_dir, "Weekly_unsubscribes.xlsx")
    if os.path.exists(weekly_unsubs_csv_path):
        df_unsub_c = pd.read_csv(weekly_unsubs_csv_path)
        df_unsub_c.to_excel(weekly_unsubs_xlsx_path, index=False)

    weekly_unsubs_journal_csv_path = os.path.join(weekly_dir, "Weekly_unsubscribes_journal.csv")
    weekly_unsubs_journal_xlsx_path = os.path.join(weekly_dir, "Weekly_unsubscribes_journal.xlsx")
    if os.path.exists(weekly_unsubs_journal_csv_path):
        df_unsub_j = pd.read_csv(weekly_unsubs_journal_csv_path)
        df_unsub_j.to_excel(weekly_unsubs_journal_xlsx_path, index=False)

for i in tqdm(range(1), desc="Copying Soft Bounce Files: "):
    os.makedirs(master_softbounces_dir, exist_ok=True)
    source = master_tmp_dir
    destination = master_softbounces_dir
    allfiles = os.listdir(source)
    soft_bounce_files = [f for f in allfiles if "Softbounces.csv" in f]
    for f in soft_bounce_files:
        shutil.copy(os.path.join(source, f), destination)

for i in tqdm(range(1), desc="Processing Postpanel Unsubscribes: "):
    os.chdir(mw_unsubs_dir)
    csv_files = glob.glob('*.csv')
    if csv_files:
        df_concat = pd.concat([pd.read_csv(f, on_bad_lines='skip', usecols = ['email', 'status', 'date_added']) for f in csv_files], ignore_index=True)
        df = df_concat
        status = ["unsubscribed"]
        unsub_df = df[df['status'].isin(status)].copy()
        if not unsub_df.empty:
            unsub_df.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True)
            unsub_dfg = unsub_df.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber'))
            unsub_dfg.to_csv(os.path.join(postpanel_unsubs_dir, 'mw_unsubscribers.csv'), index=False)

    os.chdir(postpanel_unsubs_dir)
    unsub_mgport = pd.read_csv('unsubcriber_sheet.csv')
    unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
    unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
    unsub_mgportg.to_csv('unsubcriber_sheet.csv', index=False)

    csv_files = glob.glob('*.csv')
    glob_unsubs = pd.concat([pd.read_csv(f) for f in csv_files if 'unsubcriber_sheet' not in f and 'mw_unsubscribers' not in f], ignore_index=True)
    if not glob_unsubs.empty:
        glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))
        glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True, errors='ignore')
        if 'DateTime Info' in glob_unsubs.columns:
            glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)
        options = ['Global Unsubscriber']
        unsub_df = glob_unsubs[glob_unsubs['Conference Name'].isin(options)].copy()
        if not unsub_df.empty:
            unsub_df.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
            if 'Conference Name' in unsub_df.columns:
                unsub_df.drop(['Conference Name'], axis=1, inplace=True)
            unsub_df['Email'] = unsub_df['Email'].str.lower()
            unsub_df.to_csv(os.path.join(master_dir, "PP_Global_Unsubscribers.csv"), index=False)

for i in tqdm(range(1), desc="Zipping Master Data: "):
    os.chdir(master_dir)
    fantasy_zip = zipfile.ZipFile(os.path.join(master_dir, 'Master Bounces and Unsubs.zip'), 'w')
    for folder, subfolders, files in os.walk(master_dir):
        for file in files:
            if file.endswith(('.csv')):
                fantasy_zip.write(os.path.join(folder, file), os.path.relpath(os.path.join(folder,file), master_dir), compress_type = zipfile.ZIP_DEFLATED)
    fantasy_zip.close()
    current_date = date.today()
    str_current_date = str(current_date)
    file_name = f"Master Bounces and Unsubs {str_current_date}.zip"
    os.rename('Master Bounces and Unsubs.zip', file_name)

print("Finished!")