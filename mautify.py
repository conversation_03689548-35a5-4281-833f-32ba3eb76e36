#sit in process

import pandas as pd
import os
import glob

# Import the rich progress module
from rich_progress import print_status, create_progress_bar, RICH_AVAILABLE

path = input("Enter loc: ")
os.chdir(path)

import shutil
import os

def remove_directory(directory_path):
    # Check if the directory exists
    if os.path.exists(directory_path):
        try:
            shutil.rmtree(directory_path)
            print_status(f'Directory {directory_path} has been removed successfully.', 'success')
        except Exception as e:
            print_status(f'Failed to remove {directory_path}. Reason: {e}', 'error')
    else:
        print_status(f'The directory {directory_path} does not exist.', 'info')

# Usage
directory_path = path+'\\output'
remove_directory(directory_path)

import os

def create_folder(folder_path, folder_name):
    # Construct the complete path for the new folder
    complete_path = os.path.join(folder_path, folder_name)

    # Check if the folder already exists
    if not os.path.exists(complete_path):
        try:
            os.makedirs(complete_path)
            print_status(f'Folder "{folder_name}" has been created successfully at {folder_path}.', 'success')
        except Exception as e:
            print_status(f'Failed to create "{folder_name}". Reason: {e}', 'error')
    else:
        print_status(f'The folder "{folder_name}" already exists at {folder_path}.', 'info')

# Usage
folder_path = path+'\\output'
folder_name = 'sent'
create_folder(folder_path, folder_name)

import re

# Given path
path = path

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Print header
print_status("Mautify CSV Processing Script", "header")

# Search for the pattern in the path
print_status("Extracting conference segment name...", "info")
match = re.search(pattern, path)

if match:
    desired_segment = match.group(1)
    print_status(f"Conference Segment Name: {desired_segment}", "success")
else:
    print_status("Desired segment not found", "error")


csn = desired_segment #input('Enter csn: ')

"""
for filename in os.listdir(path):
    if filename.endswith('.csv'):
        old_path = os.path.join(path, filename)
        new_path = os.path.join(path, filename[:-4] + '.xlsx')  # Remove '.csv' and add '.xlsx'
        os.rename(old_path, new_path)

files = os.listdir(path)
files_xls = [f for f in files if f[-4:] == 'xlsx']

df = pd.DataFrame()

sheetname='Valid+BasicCheck+DEA'
for f in files_xls:
    data = pd.read_excel(os.path.join(path, f), sheetname, engine='openpyxl')
    df = pd.concat([data, df])

print(df)

os.makedirs(("valid"), exist_ok=True)

df.to_csv('valid/'+sheetname+'.csv', index=False)


#sit in process

import pandas as pd
import os
import openpyxl

os.chdir(path)
files = os.listdir(path)
files_xls = [f for f in files if f[-4:] == 'xlsx']

df = pd.DataFrame()

sheetname='CatchAll_AcceptAll'
for f in files_xls:
    data = pd.read_excel(os.path.join(path, f), sheetname, engine='openpyxl')
    df = pd.concat([data, df])

print(df)

df.to_csv('valid/'+sheetname+'.csv', index=False)


#sit in process

import pandas as pd
import os
import openpyxl

os.chdir(path)
files = os.listdir(path)
files_xls = [f for f in files if f[-4:] == 'xlsx']

df = pd.DataFrame()

sheetname='Invalid'
for f in files_xls:
    data = pd.read_excel(os.path.join(path, f), sheetname, engine='openpyxl')
    df = pd.concat([data, df])

print(df)

os.makedirs(("invalid"), exist_ok=True)

df.to_csv('invalid/'+sheetname+'.csv', index=False)"""



#csvfy

# Import necessary libraries
import os
import glob
import pandas as pd
import numpy as np
import re
import warnings
import random
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# Print processing header
print_status("Starting CSV Processing", "header")

os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs/mw')

#list all csv files only
csv_files = glob.glob('*.{}'.format('csv'))

# Print status
print_status(f"Processing {len(csv_files)} CSV files from MailWizz...", "info")

# Create a progress bar for reading CSV files
progress, update_progress = create_progress_bar(len(csv_files), "Reading MailWizz CSV files", "blue")

try:
    # Read each CSV file with progress bar
    dfs = []
    for i, f in enumerate(csv_files):
        update_progress(0, f"Reading file {i+1}/{len(csv_files)}: {f}")
        df = pd.read_csv(f, on_bad_lines='skip', usecols=['email', 'status', 'date_added'])
        dfs.append(df)
        update_progress(1)

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Concatenate all dataframes
    print_status("Concatenating files...", "info")
    df_concat = pd.concat(dfs, ignore_index=True)
    print_status(f"Successfully read {len(df_concat)} records from {len(csv_files)} files", "success")

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error reading CSV files: {str(e)}", "error")
    raise e
df = df_concat
status = ["unsubscribed"]
unsub_df = df[df['status'].isin(status)]
unsub_df1 = unsub_df.drop_duplicates(subset='email') #newline added
unsub_df1.to_csv('mw_list_unsubscriber.csv', encoding = 'utf-8-sig', index=False) #newline added
unsub_df2 = pd.read_csv('mw_list_unsubscriber.csv') #newline
unsub_df2.rename(columns = {'email': 'Email', 'status': 'Conference Name', 'date_added': 'DateTime Info'}, inplace=True) #changed
unsub_dfg = unsub_df2.apply(lambda x: x.str.replace('unsubscribed' , 'Global Unsubscriber'))
os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs')
unsub_dfg.to_csv('mw_unsubscribers.csv', index=False)

os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs/port1')

# Print status
print_status("Processing Port1 unsubscribers...", "info")

# List all CSV files
csv_files = glob.glob('*.{}'.format('csv'))

# Create a progress bar for reading CSV files
progress, update_progress = create_progress_bar(len(csv_files), "Reading Port1 CSV files", "green")

try:
    # Read each CSV file with progress bar
    dfs = []
    for i, f in enumerate(csv_files):
        update_progress(0, f"Reading file {i+1}/{len(csv_files)}: {f}")
        df = pd.read_csv(f, on_bad_lines='skip', encoding='latin')
        dfs.append(df)
        update_progress(1)

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Concatenate all dataframes
    print_status("Concatenating Port1 files...", "info")
    unsub_mgport = pd.concat(dfs, ignore_index=True)
    print_status(f"Successfully read {len(unsub_mgport)} records from Port1", "success")

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error reading Port1 CSV files: {str(e)}", "error")
    raise e
#unsub_mgport = pd.read_csv('unsubcriber_sheet.csv', encoding = 'latin')
unsub_mgport.rename(columns= {'Email ID': 'Email', 'To': 'Conference Name', 'Date Info': 'DateTime Info'}, inplace=True)
unsub_mgportg = unsub_mgport.replace(to_replace= r".*\(.+?\)", value='Global Unsubscriber', regex=True)
os.chdir(r'H:/Master Bounces and Unsubs/Postpanel Unsubs')
unsub_mgportg.to_csv('unsubcriber_sheet.csv', mode='w+', index=False)

os.chdir(r"H:/Master Bounces and Unsubs/Postpanel Unsubs")

# Print status
print_status("Processing global unsubscribers...", "info")

# List all CSV files
csv_files = glob.glob('*.{}'.format('csv'))

# Create a progress bar for reading CSV files
progress, update_progress = create_progress_bar(len(csv_files), "Reading global unsubscriber files", "purple")

try:
    # Read each CSV file with progress bar
    dfs = []
    for i, f in enumerate(csv_files):
        update_progress(0, f"Reading file {i+1}/{len(csv_files)}: {f}")
        df = pd.read_csv(f)
        dfs.append(df)
        update_progress(1)

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Concatenate all dataframes
    print_status("Concatenating global unsubscriber files...", "info")
    glob_unsubs = pd.concat(dfs, ignore_index=True)
    print_status(f"Successfully read {len(glob_unsubs)} global unsubscriber records", "success")

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error reading global unsubscriber files: {str(e)}", "error")
    raise e
glob_unsubs = glob_unsubs.apply(lambda x: x.str.replace(' - ', '-'))
glob_unsubs.rename(columns= {'Email':'Email'}, inplace=True)
glob_unsubs.drop(['DateTime Info'], axis=1, inplace=True)
sp_filter = csn #input("Csn/ws: ")
options = ['Global Unsubscriber', sp_filter]
unsub_df3 = glob_unsubs[glob_unsubs['Conference Name'].isin(options)]
unsub_df3.drop_duplicates(subset='Email', inplace=True, ignore_index=True)
unsub_df3.drop(['Conference Name'], axis=1, inplace=True)
unsub_df3[['Email']] = unsub_df3[['Email']].applymap(lambda x:x.lower())
unsub_df3.to_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv", index=False)

path2 = path
csn = sp_filter #input("Enter csname: ")

os.chdir(path2)

# Print status
print_status(f"Processing conference data for {csn}...", "info")

# Create processing directory
os.makedirs(("process"), exist_ok=True)
print_status("Created processing directory", "info")

# List all CSV files
csv_files = glob.glob('*.{}'.format('csv'))

# Create a progress bar for reading CSV files
progress, update_progress = create_progress_bar(len(csv_files), "Reading conference CSV files", "orange")

try:
    # Read each CSV file with progress bar
    dfs = []
    for i, f in enumerate(csv_files):
        update_progress(0, f"Reading file {i+1}/{len(csv_files)}: {f}")
        df = pd.read_csv(f, on_bad_lines='skip', low_memory=False)
        dfs.append(df)
        update_progress(1)

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Concatenate all dataframes
    print_status("Concatenating conference files...", "info")
    df_concat = pd.concat(dfs, ignore_index=True)
    print_status(f"Successfully read {len(df_concat)} conference records", "success")

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error reading conference files: {str(e)}", "error")
    raise e
df_concat.rename(columns= {'Author Name':'Name'}, inplace=True)
df_concat.dropna(subset='Email', inplace=True)
df_concat.to_csv("./process/"+csn+"-process_unfiltered.csv", encoding='utf-8-sig', index=False)
df_hb = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Hardbounces.csv", on_bad_lines='skip')
df_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/Master_Unsubscribes.csv")
df_rep = pd.read_csv("H:/Master Bounces and Unsubs/Replied Ext/Prev_Rep_bounces_csv/"+csn+"_replied_bouncers.csv")
df_pp_gl_unsubs = pd.read_csv("H:/Master Bounces and Unsubs/Master Bounces and Unsubs/PP_Global_Unsubscribers.csv")
df_concat_unsubs = pd.concat([df_unsubs, df_pp_gl_unsubs])
df_concat_unsubs[['Email']] = df_concat_unsubs[['Email']].applymap(lambda x:x.lower())
df_concat = pd.read_csv(path2+"./process/"+csn+"-process_unfiltered.csv", low_memory=False)
df_hb_filtered = df_concat[~(df_concat.Email.isin(df_hb.Email))]
df_unsubs_filtered = df_hb_filtered[~(df_hb_filtered.Email.isin(df_concat_unsubs.Email))]
df = df_unsubs_filtered[~(df_unsubs_filtered.Email.isin(df_rep.Email))]
if 'Article Title' not in df.columns:
    df['Article Title'] = 0
df.drop(['Article Title'], axis=1, inplace=True)
df.to_csv("./process/"+csn+"-process_deduped.csv", encoding='utf-8-sig', index=False)
df = pd.read_csv("./process/"+csn+"-process_deduped.csv", usecols = ["Name", "Email"])
df1 = df.replace(r'^\s*$', np.nan, regex=True)
df2 = df1.fillna('Colleague')
result = df2.drop_duplicates(subset = 'Email')
result.to_csv("./process/"+csn+"-process.csv", mode = 'w+', encoding='utf-8-sig', index=False)
df = pd.read_csv("./process/"+csn+"-process.csv")

import math
sheets = len(df)/10000
rounded_sheets = math.ceil(sheets)
print("Number of emails: ", len(df))
print("Number of sheets: ", rounded_sheets)


os.remove("./process/"+csn+"-process_deduped.csv")
os.remove("./process/"+csn+"-process_unfiltered.csv")
#os.rename("./process/"+csn+"-process_prop_final.csv", "./process/"+csn+"-process.csv")
os.remove("./process/"+csn+"-process.csv")
os.rmdir("process")

# Print status
print_status("Generating subject lines...", "info")

# List of subject line templates
subj_list = ['Invitation to Submit Abstract for Oral Presentation', 'Invitation to Submit Abstracts for [CCT_CSNAME]', 'Call for Papers: 20-Minute Oral Presentation at [CCT_CSNAME]', 'Submit Your Abstract for [CCT_CSNAME]', 'Oral Presentation Slots Available at [CCT_CSNAME]', 'Join Us as a Presenter at [CCT_CSNAME]', 'Abstract Submission for Oral Presentations at [CCT_CSNAME] is OPEN!', 'Your Expertise Wanted: Call for 20-Minutes Oral Presentation', '[CCT_CSNAME]: Now Accepting Abstract Submissions for Oral Presentations', 'Share Your Research at [CCT_CSNAME]', 'Invitation to Submit Abstract for 20-Minute Oral Presentation', 'Present Your Findings at [CCT_CSNAME]', 'Call for Oral Presentation Abstracts at [CCT_CSNAME]', '[CCT_CSNAME]: Call for 20-Minute Oral Presentation Abstracts', 'Call for 20-Minute Oral Presentation Abstracts Now Open!', 'Be Part of the Program: Submit Your Abstract Now!', 'Call for Abstracts: [CCT_CSNAME]', 'Submit your Research Abstract for the [CCT_CSNAME]', 'Abstract Submission Open: [CCT_CSNAME]', 'Submit Your Abstracts for the [CCT_CSNAME]', 'Invitation to Speak at the [CCT_CSNAME]', 'Be Our Guest Speaker at the [CCT_CSNAME]', 'Call for Speakers: [CCT_CSNAME]', 'Discovering the Future of Technology at [CCT_CSNAME]', 'The Ultimate Networking Opportunity: [CCT_CSNAME]', "Don't Miss Out on [CCT_CSNAME]: Exploring the Latest Trends and Technologies", 'Join the Conversations at [CCT_CSNAME]: A Dynamic Forum for Ideas and Inspiration']

# Replace placeholder with actual conference name
formatted_subj_list = [subject.replace("[CCT_CSNAME]", csn) for subject in subj_list]

# Create a progress bar for generating subject lines
if len(df) > 1000:
    progress, update_progress = create_progress_bar(1, "Generating subject lines", "blue")

    try:
        # Assign random subject lines
        update_progress(0, f"Assigning random subject lines to {len(df)} records")
        df["Subject"] = pd.Series(
            random.choices(formatted_subj_list, k=len(df)),
            index=df.index
        )
        update_progress(1)

        # Close the progress bar if using Rich
        if RICH_AVAILABLE:
            progress.stop()

        print_status("Subject lines generated successfully", "success")

    except Exception as e:
        # Close the progress bar if using Rich
        if RICH_AVAILABLE:
            progress.stop()
        print_status(f"Error generating subject lines: {str(e)}", "error")
        raise e
else:
    # For smaller datasets, just assign without progress bar
    df["Subject"] = pd.Series(
        random.choices(formatted_subj_list, k=len(df)),
        index=df.index
    )
    print_status("Subject lines generated successfully", "success")

# Create output directory
os.makedirs(("output"), exist_ok=True)
print_status("Created output directory", "info")

# Get the number of rows in the dataframe
n_rows = len(df)
n_temps = 1 #int(input("No. of temps: "))

# Calculate the size of each chunk
chunk_size = n_rows // n_temps

# Print status
print_status("Saving output files...", "info")

# Create a progress bar for the file saving process
progress, update_progress = create_progress_bar(n_temps, "Saving files", "green")

try:
    # Split the dataframe into chunks and save them as separate csv files
    for i in range(n_temps):
        # Update progress description
        update_progress(0, f"Saving file {i+1}/{n_temps}")

        start = i * chunk_size
        end = (i + 1) * chunk_size if i < n_temps-1 else n_rows
        chunk = df[start:end]
        output_path = f"./output/{csn}-{i+1}.csv"
        chunk.to_csv(output_path, encoding='utf-8-sig', mode='w+', index=False)

        # Update progress
        update_progress(1)
        print_status(f"Saved {len(chunk)} records to {output_path}", "success")

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    print_status(f"Successfully saved {n_rows} records to {n_temps} files", "header")

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error saving output files: {str(e)}", "error")
    raise e

'''    part_count = len(df) // int(input("No. of temps: "))
    rows_per_file = part_count
    n_chunks = len(df) // rows_per_file

    for i in range(n_chunks):
        start = i*rows_per_file
        stop = (i+1)*rows_per_file
        sub_df = df.iloc[start:stop]
        sub_df.to_csv(f"./output/{csn}-{i}.csv", encoding='utf-8-sig', mode = 'w+', index=False)'''

'''import glob
import pandas as pd

df_all = pd.DataFrame()

path = input('Enter path: ')

for f in glob.glob(path+'/*.csv'):
    df = pd.read_csv(f)
    df.rename(columns= {'Author Name':'Name', 'col1':'Name', 'col2': 'Article Title', 'email': 'Email', 'name': 'Name'}, inplace=True)
    df['Source'] = os.path.basename(f)
    df_all = pd.concat([df_all, df])

df_all

df_all.to_csv('trash.csv', encoding='utf-8-sig', mode = 'w+', index=False)'''
pass





#mautify
print_status("\nStarting Mautic Excel Conversion", "header")

# Configure pandas to not style Excel headers
pd.io.formats.excel.ExcelFormatter.header_style = None

# Set path to output directory
path3 = path2 + '/output'
os.chdir(path3)
print_status(f"Changed directory to {path3}", "info")

# Step 1: Read and concatenate CSV files
print_status("Reading CSV files for Mautic conversion...", "info")
csv_files = glob.glob('*.{}'.format('csv'))

# Create a progress bar for reading CSV files
progress, update_progress = create_progress_bar(len(csv_files), "Reading CSV files for Mautic", "blue")

try:
    # Read each CSV file with progress bar
    dfs = []
    for i, f in enumerate(csv_files):
        update_progress(0, f"Reading file {i+1}/{len(csv_files)}: {f}")
        df = pd.read_csv(f, on_bad_lines='skip', low_memory=False)
        dfs.append(df)
        update_progress(1)

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

    # Concatenate all dataframes
    print_status("Concatenating files for Mautic...", "info")
    df_concat = pd.concat(dfs, ignore_index=True)
    print_status(f"Successfully read {len(df_concat)} records for Mautic conversion", "success")

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error reading CSV files for Mautic: {str(e)}", "error")
    raise e

# Rename columns for Mautic format
df_concat.rename(columns={'Name': 'firstname', 'Email': 'email'}, inplace=True)
if 'title' not in df_concat.columns:
    df_concat['title'] = ""

filename = csn

# Step 2: Split the DataFrame into chunks of 10,000 rows
print_status("Splitting data into chunks of 10,000 rows...", "info")
n = 10000  # Chunk row size
list_df = [df_concat[i:i+n] for i in range(0, df_concat.shape[0], n)]
print_status(f"Created {len(list_df)} chunks", "success")

# Step 3: Write each chunk to an Excel file with progress bar
path_to_directory = path3 + '/'  # Specify your desired directory

# Create a progress bar for Excel file creation
progress, update_progress = create_progress_bar(len(list_df), "Creating Excel files", "orange")

try:
    for i, chunk_df in enumerate(list_df):
        update_progress(0, f"Creating Excel file {i+1}/{len(list_df)}")
        filename = f'{path_to_directory}{csn}_mautic_{i + 1}.xlsx'
        chunk_df.to_excel(filename, columns=(['firstname', 'email', 'title']), index=False)
        update_progress(1)
        print_status(f"Created {filename} with {len(chunk_df)} records", "success")

    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()

except Exception as e:
    # Close the progress bar if using Rich
    if RICH_AVAILABLE:
        progress.stop()
    print_status(f"Error creating Excel files: {str(e)}", "error")
    raise e

# Step 4: Delete CSV files after processing
print_status("Cleaning up CSV files...", "info")
os.system('del *.csv')

print_status("Mautic Excel Conversion Completed Successfully!", "header")
