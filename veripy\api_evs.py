import requests
import pandas as pd
import datetime
import os
import time
import logging
import configparser
from pathlib import Path

def load_config(config_file="config.ini"):
    """Loads configuration from a file."""
    config = configparser.ConfigParser()
    config.read(config_file)
    return config

def setup_logging():
    """Sets up basic logging configuration."""
    logging.basicConfig(
        filename="api_evs.log",
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
    )

def verify_email(email, api_key):
    """Verifies a single email using the Elastic Email API."""
    url = f'https://api.elasticemail.com/v2/email/verify?apikey={api_key}&email={email}'
    try:
        response = requests.get(url)
        response.raise_for_status()  # Raise an exception for bad status codes
        return response.json()
    except requests.exceptions.RequestException as e:
        logging.error(f"API request failed for {email}: {e}")
        return None

def main():
    """Main function to verify emails and save results."""
    setup_logging()
    config = load_config()

    try:
        api_keys = config["api_keys"]["keys"].split(",")
        base_directory = config["directories"]["base_directory"]
        rate_limit_delay = float(config["settings"]["rate_limit_delay"])
    except (KeyError, ValueError) as e:
        logging.error(f"Error reading configuration: {e}")
        return

    try:
        csv_file_path = input("Enter the path to your CSV file: ")
        df = pd.read_csv(csv_file_path)
        emails = df[["Name", "Email"]].to_dict("records")
    except FileNotFoundError:
        logging.error(f"CSV file not found: {csv_file_path}")
        return
    except pd.errors.EmptyDataError:
        logging.error(f"CSV file is empty: {csv_file_path}")
        return
    except pd.errors.ParserError:
        logging.error(f"Error parsing CSV file: {csv_file_path}")
        return
    except KeyError:
        logging.error(f"CSV file does not contain 'Name' and 'Email' columns: {csv_file_path}")
        return

    verification_results = []
    for i, record in enumerate(emails):
        api_key = api_keys[i % len(api_keys)]
        result = verify_email(record["Email"], api_key)

        if result:
            result_status = result.get("data", {}).get("result")
            reason_status = result.get("data", {}).get("reason")
            verification_results.append(
                {
                    "Name": record["Name"],
                    "Email": record["Email"],
                    "Result": result_status,
                    "Reason": reason_status,
                }
            )
        else:
            verification_results.append(
                {
                    "Name": record["Name"],
                    "Email": record["Email"],
                    "Result": "Error",
                    "Reason": "API request failed",
                }
            )

        time.sleep(rate_limit_delay)  # Add a delay to avoid rate limiting

    result_df = pd.DataFrame(verification_results)

    now = datetime.datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")

    try:
        subject_name = input("Enter the subject name: ")
        template_type = input("Enter the template type: ")
    except EOFError:
        logging.error("Input interrupted by user.")
        return

    # Validate inputs
    if not subject_name or not template_type:
        logging.error("Subject name and template type cannot be empty.")
        return

    # Create directory structure
    output_dir = Path(base_directory) / "evs_results" / subject_name / template_type
    output_dir.mkdir(parents=True, exist_ok=True)

    output_file = output_dir / f"{subject_name}_{template_type}_{timestamp}.csv"
    result_df.to_csv(output_file, index=False)
    logging.info(f"Results saved to: {output_file}")
    print(result_df)

if __name__ == "__main__":
    main()
