##HIC Individual Countries Filter

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# TLD to Country Name Mapping
tld_to_country = {
    # A
    '.ac': 'Ascension Island', '.ad': 'Andorra', '.ae': 'United Arab Emirates', '.af': 'Afghanistan',
    '.ag': 'Antigua and Barbuda', '.ai': 'Anguilla', '.al': 'Albania', '.am': 'Armenia',
    '.ao': 'Angola', '.aq': 'Antarctica', '.ar': 'Argentina', '.as': 'American Samoa',
    '.at': 'Austria', '.au': 'Australia', '.aw': 'Aruba', '.ax': 'Åland Islands', '.az': 'Azerbaijan',
    # B
    '.ba': 'Bosnia and Herzegovina', '.bb': 'Barbados', '.bd': 'Bangladesh', '.be': 'Belgium',
    '.bf': 'Burkina Faso', '.bg': 'Bulgaria', '.bh': 'Bahrain', '.bi': 'Burundi',
    '.bj': 'Benin', '.bm': 'Bermuda', '.bn': 'Brunei', '.bo': 'Bolivia',
    '.br': 'Brazil', '.bs': 'Bahamas', '.bt': 'Bhutan', '.bw': 'Botswana',
    '.by': 'Belarus', '.bz': 'Belize',
    # C
    '.ca': 'Canada', '.cc': 'Cocos Islands', '.cd': 'Democratic Republic of the Congo', '.cf': 'Central African Republic',
    '.cg': 'Republic of the Congo', '.ch': 'Switzerland', '.ci': 'Côte d\'Ivoire', '.ck': 'Cook Islands',
    '.cl': 'Chile', '.cm': 'Cameroon', '.cn': 'China', '.co': 'Colombia',
    '.cr': 'Costa Rica', '.cu': 'Cuba', '.cv': 'Cape Verde', '.cw': 'Curaçao',
    '.cx': 'Christmas Island', '.cy': 'Cyprus', '.cz': 'Czech Republic',
    # D
    '.de': 'Germany', '.dj': 'Djibouti', '.dk': 'Denmark', '.dm': 'Dominica',
    '.do': 'Dominican Republic', '.dz': 'Algeria',
    # E
    '.ec': 'Ecuador', '.ee': 'Estonia', '.eg': 'Egypt', '.er': 'Eritrea',
    '.es': 'Spain', '.et': 'Ethiopia', '.eu': 'European Union',
    # F
    '.fi': 'Finland', '.fj': 'Fiji', '.fk': 'Falkland Islands', '.fm': 'Micronesia',
    '.fo': 'Faroe Islands', '.fr': 'France',
    # G
    '.ga': 'Gabon', '.gd': 'Grenada', '.ge': 'Georgia', '.gf': 'French Guiana',
    '.gg': 'Guernsey', '.gh': 'Ghana', '.gi': 'Gibraltar', '.gl': 'Greenland',
    '.gm': 'Gambia', '.gn': 'Guinea', '.gp': 'Guadeloupe', '.gq': 'Equatorial Guinea',
    '.gr': 'Greece', '.gs': 'South Georgia and the South Sandwich Islands', '.gt': 'Guatemala', '.gu': 'Guam',
    '.gw': 'Guinea-Bissau', '.gy': 'Guyana',
    # H
    '.hk': 'Hong Kong', '.hm': 'Heard Island and McDonald Islands', '.hn': 'Honduras', '.hr': 'Croatia',
    '.ht': 'Haiti', '.hu': 'Hungary',
    # I
    '.id': 'Indonesia', '.ie': 'Ireland', '.il': 'Israel', '.im': 'Isle of Man',
    '.in': 'India', '.io': 'British Indian Ocean Territory', '.iq': 'Iraq', '.ir': 'Iran',
    '.is': 'Iceland', '.it': 'Italy',
    # J
    '.je': 'Jersey', '.jm': 'Jamaica', '.jo': 'Jordan', '.jp': 'Japan',
    # K
    '.ke': 'Kenya', '.kg': 'Kyrgyzstan', '.kh': 'Cambodia', '.ki': 'Kiribati',
    '.km': 'Comoros', '.kn': 'Saint Kitts and Nevis', '.kp': 'North Korea', '.kr': 'South Korea',
    '.kw': 'Kuwait', '.ky': 'Cayman Islands', '.kz': 'Kazakhstan',
    # L
    '.la': 'Laos', '.lb': 'Lebanon', '.lc': 'Saint Lucia', '.li': 'Liechtenstein',
    '.lk': 'Sri Lanka', '.lr': 'Liberia', '.ls': 'Lesotho', '.lt': 'Lithuania',
    '.lu': 'Luxembourg', '.lv': 'Latvia', '.ly': 'Libya',
    # M
    '.ma': 'Morocco', '.mc': 'Monaco', '.md': 'Moldova', '.me': 'Montenegro',
    '.mg': 'Madagascar', '.mh': 'Marshall Islands', '.mk': 'North Macedonia', '.ml': 'Mali',
    '.mm': 'Myanmar', '.mn': 'Mongolia', '.mo': 'Macao', '.mp': 'Northern Mariana Islands',
    '.mq': 'Martinique', '.mr': 'Mauritania', '.ms': 'Montserrat', '.mt': 'Malta',
    '.mu': 'Mauritius', '.mv': 'Maldives', '.mw': 'Malawi', '.mx': 'Mexico',
    '.my': 'Malaysia', '.mz': 'Mozambique',
    # N
    '.na': 'Namibia', '.nc': 'New Caledonia', '.ne': 'Niger', '.nf': 'Norfolk Island',
    '.ng': 'Nigeria', '.ni': 'Nicaragua', '.nl': 'Netherlands', '.no': 'Norway',
    '.np': 'Nepal', '.nr': 'Nauru', '.nu': 'Niue', '.nz': 'New Zealand',
    # O
    '.om': 'Oman',
    # P
    '.pa': 'Panama', '.pe': 'Peru', '.pf': 'French Polynesia', '.pg': 'Papua New Guinea',
    '.ph': 'Philippines', '.pk': 'Pakistan', '.pl': 'Poland', '.pm': 'Saint Pierre and Miquelon',
    '.pn': 'Pitcairn Islands', '.pr': 'Puerto Rico', '.ps': 'Palestine', '.pt': 'Portugal',
    '.pw': 'Palau', '.py': 'Paraguay',
    # Q
    '.qa': 'Qatar',
    # R
    '.re': 'Réunion', '.ro': 'Romania', '.rs': 'Serbia', '.ru': 'Russia', '.rw': 'Rwanda',
    # S
    '.sa': 'Saudi Arabia', '.sb': 'Solomon Islands', '.sc': 'Seychelles', '.sd': 'Sudan',
    '.se': 'Sweden', '.sg': 'Singapore', '.sh': 'Saint Helena', '.si': 'Slovenia',
    '.sk': 'Slovakia', '.sl': 'Sierra Leone', '.sm': 'San Marino', '.sn': 'Senegal',
    '.so': 'Somalia', '.sr': 'Suriname', '.ss': 'South Sudan', '.st': 'São Tomé and Príncipe',
    '.su': 'Soviet Union', '.sv': 'El Salvador', '.sx': 'Sint Maarten', '.sy': 'Syria', '.sz': 'Eswatini',
    # T
    '.tc': 'Turks and Caicos Islands', '.td': 'Chad', '.tf': 'French Southern Territories', '.tg': 'Togo',
    '.th': 'Thailand', '.tj': 'Tajikistan', '.tk': 'Tokelau', '.tl': 'East Timor',
    '.tm': 'Turkmenistan', '.tn': 'Tunisia', '.to': 'Tonga', '.tr': 'Turkey',
    '.tt': 'Trinidad and Tobago', '.tv': 'Tuvalu', '.tw': 'Taiwan', '.tz': 'Tanzania',
    # U
    '.ua': 'Ukraine', '.ug': 'Uganda', '.uk': 'United Kingdom', '.us': 'United States',
    '.uy': 'Uruguay', '.uz': 'Uzbekistan',
    # V
    '.va': 'Vatican City', '.vc': 'Saint Vincent and the Grenadines', '.ve': 'Venezuela', '.vg': 'British Virgin Islands',
    '.vi': 'U.S. Virgin Islands', '.vn': 'Vietnam', '.vu': 'Vanuatu',
    # W
    '.wf': 'Wallis and Futuna', '.ws': 'Samoa',
    # Y
    '.ye': 'Yemen', '.yt': 'Mayotte',
    # Z
    '.za': 'South Africa', '.zm': 'Zambia', '.zw': 'Zimbabwe',
    # Special domains
    '.edu': 'United States', '.gov': 'United States', '.mil': 'United States'
}

# Special Chinese email domains mapping
chinese_domains = {
    '163.com': 'China', 'qq.com': 'China', '126.com': 'China', 'sina.com': 'China',
    'sohu.com': 'China', 'tom.com': 'China', 'aliyun.com': 'China', '21cn.com': 'China',
    'baidu.com': 'China', 'yeah.net': 'China', 'sogou.com': 'China', '163.net': 'China',
    'sina.net': 'China', 'chinaren.com': 'China'
}

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for special Chinese domains first
    for domain, country in chinese_domains.items():
        if email.endswith(domain):
            return country

    # Check for country TLDs
    for tld, country in tld_to_country.items():
        if email.endswith(tld):
            return country

    # If no match found, return 'Other'
    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("HIC Individual Countries Filter")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    csn = match.group(1)
    rich_progress.print_status(f"Found segment: {csn}", "success")
else:
    rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
    # Prompt for manual input
    csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
    if csn.strip():
        rich_progress.print_status(f"Using manually entered segment: {csn}", "info")
    else:
        rich_progress.print_status("No segment name provided. Exiting.", "error")
        exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file, low_memory=False)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Define all country code TLDs
print_section("Defining Country TLDs")
rich_progress.print_status("Setting up country code TLDs for filtering...", "info")

# All country code TLDs
country_tlds = [
    # A
    '.ac', '.ad', '.ae', '.af', '.ag', '.ai', '.al', '.am', '.ao', '.aq', '.ar', '.as', '.at', '.au', '.aw', '.ax', '.az',
    # B
    '.ba', '.bb', '.bd', '.be', '.bf', '.bg', '.bh', '.bi', '.bj', '.bm', '.bn', '.bo', '.br', '.bs', '.bt', '.bw', '.by', '.bz',
    # C
    '.ca', '.cc', '.cd', '.cf', '.cg', '.ch', '.ci', '.ck', '.cl', '.cm', '.cn', '.co', '.cr', '.cu', '.cv', '.cw', '.cx', '.cy', '.cz',
    # D
    '.de', '.dj', '.dk', '.dm', '.do', '.dz',
    # E
    '.ec', '.ee', '.eg', '.er', '.es', '.et', '.eu',
    # F
    '.fi', '.fj', '.fk', '.fm', '.fo', '.fr',
    # G
    '.ga', '.gd', '.ge', '.gf', '.gg', '.gh', '.gi', '.gl', '.gm', '.gn', '.gp', '.gq', '.gr', '.gs', '.gt', '.gu', '.gw', '.gy',
    # H
    '.hk', '.hm', '.hn', '.hr', '.ht', '.hu',
    # I
    '.id', '.ie', '.il', '.im', '.in', '.io', '.iq', '.ir', '.is', '.it',
    # J
    '.je', '.jm', '.jo', '.jp',
    # K
    '.ke', '.kg', '.kh', '.ki', '.km', '.kn', '.kp', '.kr', '.kw', '.ky', '.kz',
    # L
    '.la', '.lb', '.lc', '.li', '.lk', '.lr', '.ls', '.lt', '.lu', '.lv', '.ly',
    # M
    '.ma', '.mc', '.md', '.me', '.mg', '.mh', '.mk', '.ml', '.mm', '.mn', '.mo', '.mp', '.mq', '.mr', '.ms', '.mt', '.mu', '.mv', '.mw', '.mx', '.my', '.mz',
    # N
    '.na', '.nc', '.ne', '.nf', '.ng', '.ni', '.nl', '.no', '.np', '.nr', '.nu', '.nz',
    # O
    '.om',
    # P
    '.pa', '.pe', '.pf', '.pg', '.ph', '.pk', '.pl', '.pm', '.pn', '.pr', '.ps', '.pt', '.pw', '.py',
    # Q
    '.qa',
    # R
    '.re', '.ro', '.rs', '.ru', '.rw',
    # S
    '.sa', '.sb', '.sc', '.sd', '.se', '.sg', '.sh', '.si', '.sk', '.sl', '.sm', '.sn', '.so', '.sr', '.ss', '.st', '.su', '.sv', '.sx', '.sy', '.sz',
    # T
    '.tc', '.td', '.tf', '.tg', '.th', '.tj', '.tk', '.tl', '.tm', '.tn', '.to', '.tr', '.tt', '.tv', '.tw', '.tz',
    # U
    '.ua', '.ug', '.uk', '.us', '.uy', '.uz',
    # V
    '.va', '.vc', '.ve', '.vg', '.vi', '.vn', '.vu',
    # W
    '.wf', '.ws',
    # Y
    '.ye', '.yt',
    # Z
    '.za', '.zm', '.zw',
    # Special domains
    '.edu', '.gov', '.mil'
]

# Create a dictionary to store all country dataframes
country_dfs = {}

# Print section for filtering results
print_section("Filtering by Country TLDs")
rich_progress.print_status("Processing all country TLDs...", "info")

# Create a progress bar for filtering
filter_bar, update_filter = rich_progress.create_progress_bar(
    total=len(country_tlds),
    description="Filtering by TLDs",
    color_scheme="green"
)

# Filter for each country TLD
for tld in country_tlds:
    tld_clean = tld.replace('.', '')  # Remove dot for variable naming
    country_dfs[tld_clean] = d2_EVENT[d2_EVENT["Email"].str.endswith(tld, na=False)]
    update_filter(1, f"Filtered {tld}")

# Stop the progress bar
filter_bar.stop()

# Chinese domains
df_hiccn = d2_EVENT[d2_EVENT["Email"].str.endswith("163.com", na=False) |
                d2_EVENT["Email"].str.endswith("qq.com", na=False) |
                d2_EVENT["Email"].str.endswith("126.com", na=False) |
                d2_EVENT["Email"].str.endswith("sina.com", na=False) |
                d2_EVENT["Email"].str.endswith("sohu.com", na=False) |
                d2_EVENT["Email"].str.endswith("tom.com", na=False) |
                d2_EVENT["Email"].str.endswith("aliyun.com", na=False) |
                d2_EVENT["Email"].str.endswith("21cn.com", na=False) |
                d2_EVENT["Email"].str.endswith("baidu.com", na=False) |
                d2_EVENT["Email"].str.endswith(".cn", na=False) |
                d2_EVENT["Email"].str.endswith("yeah.net", na=False) |
                d2_EVENT["Email"].str.endswith("sogou.com", na=False) |
                d2_EVENT["Email"].str.endswith("163.net", na=False) |
                d2_EVENT["Email"].str.endswith("sina.net", na=False) |
                d2_EVENT["Email"].str.endswith("chinaren.com", na=False)]

# Create output directory
print_section("Creating Output Directory")
countries_dir = os.path.join(os.getcwd(), "countries")
os.makedirs(countries_dir, exist_ok=True)
rich_progress.print_status(f"Created countries directory: {countries_dir}", "info")

# Save country files (only for countries with records)
print_section("Saving Country Files")

files_to_save = []

# Add Chinese domains file
files_to_save.append((df_hiccn, f"{csn}_China.csv", "China"))

# Add country filters (only for countries with records)
for tld, df in country_dfs.items():
    if len(df) > 0:
        country_name = tld.upper()  # Simple conversion to uppercase for country name
        files_to_save.append((df, f"{csn}_{country_name}.csv", country_name))

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving country files",
    color_scheme="green"
)

# Save each file with progress tracking
for df, filename, category in files_to_save:
    try:
        output_path = os.path.join(countries_dir, filename)
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        update_save(1, f"Saved {category} ({len(df)} records)")
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")

# Stop the progress bar
save_bar.stop()

# Print completion message
print_header("Individual Countries Processing Completed!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")

# Print counts for top 10 countries by number of records
print_section("Top 10 Countries")
country_counts = {tld.replace('.', ''): len(df) for tld, df in country_dfs.items() if len(df) > 0}
top_countries = sorted(country_counts.items(), key=lambda x: x[1], reverse=True)[:10]

rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Country':<20} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")

for country, count in top_countries:
    rich_progress.print_status(f"{country:<20} {count:>10}", "info")

rich_progress.print_status(f"{'China (special)':<20} {len(df_hiccn):>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Print country distribution from the Country column
print_section("Country Distribution (from Country Column)")
country_distribution = d2_EVENT['Country'].value_counts()
rich_progress.print_status(f"Total unique countries identified: {len(country_distribution)}", "info")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Country':<30} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Show top 15 countries by count
for country, count in country_distribution.head(15).items():
    rich_progress.print_status(f"{country:<30} {count:>10}", "info")

rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"Total files created: {len(files_to_save)}", "success")
rich_progress.print_status(f"Output directory: {countries_dir}", "info")
