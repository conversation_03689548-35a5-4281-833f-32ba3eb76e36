@echo off
echo ========================================
echo Mathews Replied Bounces Processing
echo ========================================
echo.

:: Change to the working directory
cd /d "C:\Users\<USER>\OneDrive\My Files"

echo Step 1: Running separate_replied_bounces.py
echo ----------------------------------------
echo Separating replied bounces files...
echo.

python "separate_replied_bounces.py"

:: Check if the first script executed successfully
if %errorlevel% neq 0 (
    echo.
    echo ERROR: separate_replied_bounces.py failed with error code %errorlevel%
    echo Process stopped. Please check the error and try again.
    pause
    exit /b %errorlevel%
)

echo.
echo Step 1 completed successfully!
echo.

echo Step 2: Running auto_rep_upload_mathews.py
echo ------------------------------------------
echo Uploading Mathews replied bounces to DBMS...
echo.

python "auto_rep_upload_mathews.py"

:: Check if the second script executed successfully
if %errorlevel% neq 0 (
    echo.
    echo ERROR: auto_rep_upload_mathews.py failed with error code %errorlevel%
    echo Separation completed but upload failed. Please check the error.
    pause
    exit /b %errorlevel%
)

echo.
echo ========================================
echo Mathews Process Completed Successfully!
echo ========================================
echo Both scripts have executed successfully:
echo 1. Replied bounces separated
echo 2. Files uploaded to DBMS
echo.
echo Press any key to exit...
pause > nul
