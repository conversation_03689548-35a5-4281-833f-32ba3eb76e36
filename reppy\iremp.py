import os
import datetime
import subprocess
import time
import logging
import configparser

def main():
    """
    Waits for a specific file to appear in a directory and then executes another Python script.
    """

    # Load configuration from a file (e.g., config.ini)
    config = configparser.ConfigParser()
    config.read("config.ini")  # Create a config.ini file with the paths

    try:
        directory_path = config["paths"]["directory_path"]
        script_path = config["paths"]["script_path"]
        max_retries = int(config["settings"]["max_retries"])
    except (KeyError, ValueError) as e:
        logging.error(f"Error reading configuration: {e}")
        return

    # Configure logging
    logging.basicConfig(
        filename="iremp.log",
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
    )

    # Get today's date
    today_date = datetime.datetime.now().strftime("%d-%m-%Y")
    file_name = f"Replied Bounces {today_date}.xlsx"
    file_path = os.path.join(directory_path, file_name)

    retries = 0
    while retries < max_retries:
        if os.path.isfile(file_path):
            logging.info(f"File found: {file_name}")
            try:
                # Run the Python script and capture the result
                result = subprocess.run(["python", script_path], capture_output=True, text=True, check=True)
                logging.info(f"Script {script_path} executed successfully.")
                logging.debug(f"Script output:\n{result.stdout}")
                break  # Exit the loop if the script runs successfully
            except subprocess.CalledProcessError as e:
                logging.error(f"Error executing script {script_path}:")
                logging.error(f"Return code: {e.returncode}")
                logging.error(f"Stdout: {e.stdout}")
                logging.error(f"Stderr: {e.stderr}")
                return
            except FileNotFoundError:
                logging.error(f"Error: Script not found at {script_path}")
                return
        else:
            logging.info(f"File not found: {file_name}. Checking again in 1 hour.")
            retries += 1
            time.sleep(3600)

    if retries >= max_retries:
        logging.warning(f"Maximum retries ({max_retries}) reached. Exiting.")

if __name__ == "__main__":
    main()
