import time
import os
import glob
import pandas as pd
import re
import numpy as np
import win32com.client  # Consider if this is actually used
import pandas.io.formats.excel
import shutil
import pandas.io.formats
import smtplib
import ssl
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime, date
from tqdm import tqdm

pd.io.formats.excel.ExcelFormatter.header_style = None

base_dir = r"H:\Master Bounces and Unsubs"
replied_bounces_dir = os.path.join(base_dir, "Replied bounces")
replied_ext_dir = os.path.join(base_dir, "Replied Ext")
todays_rep_bounces_dir = os.path.join(replied_ext_dir, "Todays_Rep_bounces")
todays_rep_bounces_csv_dir = os.path.join(todays_rep_bounces_dir, "csv")
todays_rep_bounces_xlsx_dir = os.path.join(todays_rep_bounces_dir, "xlsx")
prev_rep_bounces_csv_dir = os.path.join(replied_ext_dir, "Prev_Rep_bounces_csv")
prev_rep_bounces_xlsx_dir = os.path.join(replied_ext_dir, "Prev_Rep_bounces_xlsx")
rep_bounces_csv_dir = os.path.join(replied_ext_dir, "Rep_bounces_csv")
rep_bounces_xlsx_dir = os.path.join(replied_ext_dir, "Rep_bounces_xlsx")

def process_replied_bounces():
    now = datetime.now()
    date_str = now.strftime("%d-%m-%Y")
    filename = f"Replied Bounces {date_str}.xlsx"
    filepath = os.path.join(replied_bounces_dir, filename)

    try:
        df1 = pd.read_excel(filepath, usecols=["Conference Short Name", "Email"])
        df2 = pd.read_excel(filepath, usecols=["Conference Short Name", "Alternate Email"])
        df2.rename(columns={'Alternate Email': 'Email'}, inplace=True)
        df = pd.concat([df1, df2])

        # Clean up email addresses
        df['Email'] = df['Email'].str.replace('mailto:', '', regex=False)
        df['Email'] = df['Email'].str.replace('mailto', '', regex=False)
        df['Email'] = df['Email'].str.replace('[:;]', ',', regex=True)
        df['Email'] = df['Email'].str.strip()

        # Split multiple emails and stack them
        df = df.assign(Email=df['Email'].str.split(',')).explode('Email')
        df['Email'] = df['Email'].str.strip()

        # Trim whitespace and specific characters
        trim_chars = '.,;() '
        df['Email'] = df['Email'].str.strip(trim_chars)

        # Validate email format (more robust regex)
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        df = df[df['Email'].str.match(email_regex, na=False)]
        df['Email'] = df['Email'].str.lower()

        df.drop_duplicates(subset="Email", inplace=True)

        os.makedirs(rep_bounces_csv_dir, exist_ok=True)
        os.makedirs(rep_bounces_xlsx_dir, exist_ok=True)

        for conference in df['Conference Short Name'].unique():
            event_df = df[df['Conference Short Name'] == conference].copy()
            event_df_dropped = event_df.drop(columns=["Conference Short Name"])
            event_df_dropped.dropna(subset=['Email'], inplace=True)

            csv_filename = os.path.join(rep_bounces_csv_dir, f"{conference}_replied_bouncers.csv")
            xlsx_filename = os.path.join(rep_bounces_xlsx_dir, f"{conference}_replied_bouncers.xlsx")

            event_df_dropped.to_csv(csv_filename, index=False)
            event_df_dropped.to_excel(xlsx_filename, index=False)

            prev_csv_path = os.path.join(prev_rep_bounces_csv_dir, f"{conference}_replied_bouncers.csv")
            latest_csv_path = csv_filename

            if os.path.exists(prev_csv_path):
                df_prev = pd.read_csv(prev_csv_path)
                df_latest = pd.read_csv(latest_csv_path)
                df_new = df_latest[~df_latest['Email'].isin(df_prev['Email'])]
                os.makedirs(os.path.join(todays_rep_bounces_dir, "csv"), exist_ok=True)
                os.makedirs(os.path.join(todays_rep_bounces_dir, "xlsx"), exist_ok=True)
                df_new.to_csv(os.path.join(todays_rep_bounces_csv_dir, f"{conference}_replied_bouncers.csv"), index=False)
                df_new.to_excel(os.path.join(todays_rep_bounces_xlsx_dir, f"{conference}_replied_bouncers.xlsx"), index=False)
            else:
                os.makedirs(os.path.join(todays_rep_bounces_dir, "csv"), exist_ok=True)
                os.makedirs(os.path.join(todays_rep_bounces_dir, "xlsx"), exist_ok=True)
                event_df_dropped.to_csv(os.path.join(todays_rep_bounces_csv_dir, f"{conference}_replied_bouncers.csv"), index=False)
                event_df_dropped.to_excel(os.path.join(todays_rep_bounces_xlsx_dir, f"{conference}_replied_bouncers.xlsx"), index=False)

        # Mailwizz blocklist
        mw_blocklist = df.dropna(subset=['Email']).rename(columns={'Conference Short Name': 'Reason'})
        os.makedirs(todays_rep_bounces_dir, exist_ok=True)
        mw_blocklist.to_csv(os.path.join(todays_rep_bounces_dir, 'Blocklist_mailwizz.csv'), index=False)

    except FileNotFoundError:
        print(f"Error: File not found - {filepath}")
    except Exception as e:
        print(f"An error occurred during processing: {e}")

def remove_empty_files(folder):
    for root, _, files in os.walk(folder):
        for f in files:
            complete_path = os.path.join(root, f)
            try:
                if os.path.getsize(complete_path) <= 7:  # Adjust size threshold if needed
                    os.remove(complete_path)
            except FileNotFoundError:
                print(f"File not found: {complete_path}")

def remove_files_by_name(folder, pattern):
    for filename in glob.glob(os.path.join(folder, pattern)):
        try:
            os.remove(filename)
        except FileNotFoundError:
            print(f"File not found: {filename}")

def zip_directory(directory, zip_filename):
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, _, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, directory)
                zipf.write(file_path, relative_path)

def send_email(subject, body, receiver_email, attachment_path):
    sender_email = "<EMAIL>"
    password = "Micah@2022"

    message = MIMEMultipart()
    message["From"] = sender_email
    message["To"] = receiver_email
    message["Subject"] = subject
    message["Bcc"] = sender_email

    message.attach(MIMEText(body, "plain"))

    try:
        with open(attachment_path, "rb") as attachment:
            part = MIMEBase("application", "octet-stream")
            part.set_payload(attachment.read())
        encoders.encode_base64(part)
        part.add_header("Content-Disposition", f"attachment; filename= {os.path.basename(attachment_path)}")
        message.attach(part)
        text = message.as_string()

        context = ssl.create_default_context()
        with smtplib.SMTP_SSL("smtppro.zoho.in", 465, context=context) as server:
            server.login(sender_email, password)
            server.sendmail(sender_email, receiver_email, text)
        print(f"Email sent successfully to {receiver_email}")
    except FileNotFoundError:
        print(f"Error: Attachment file not found - {attachment_path}")
    except Exception as e:
        print(f"An error occurred while sending email to {receiver_email}: {e}")

if __name__ == "__main__":
    for _ in tqdm(range(1), desc="Processing", colour='green'):
        process_replied_bounces()

        remove_empty_files(todays_rep_bounces_csv_dir)
        remove_empty_files(todays_rep_bounces_xlsx_dir)

        remove_files_by_name(todays_rep_bounces_csv_dir, "*2016*")
        remove_files_by_name(todays_rep_bounces_xlsx_dir, "*2016*")
        remove_files_by_name(todays_rep_bounces_csv_dir, "London*")
        remove_files_by_name(todays_rep_bounces_xlsx_dir, "London*")

        zip_filename = os.path.join(replied_ext_dir, f"Todays_Rep_bounces {date.today().strftime('%d-%m-%Y')}.zip")
        zip_directory(todays_rep_bounces_dir, zip_filename)

        subject = os.path.basename(zip_filename) + " & Mailwizz Blocklist"
        body = "Please find attached"

        send_email(subject, body, "<EMAIL>", zip_filename)
        send_email(subject, body, "<EMAIL>", zip_filename)

        shutil.rmtree(prev_rep_bounces_csv_dir, ignore_errors=True)
        shutil.rmtree(prev_rep_bounces_xlsx_dir, ignore_errors=True)
        os.makedirs(prev_rep_bounces_csv_dir, exist_ok=True)
        os.makedirs(prev_rep_bounces_xlsx_dir, exist_ok=True)

        shutil.rmtree(rep_bounces_csv_dir, ignore_errors=True)
        shutil.rmtree(rep_bounces_xlsx_dir, ignore_errors=True)
        os.rename(os.path.join(replied_ext_dir, 'Rep_bounces_csv'), prev_rep_bounces_csv_dir)
        os.rename(os.path.join(replied_ext_dir, 'Rep_bounces_xlsx'), prev_rep_bounces_xlsx_dir)
        os.makedirs(rep_bounces_csv_dir)
        os.makedirs(rep_bounces_xlsx_dir)

    print("Completed")